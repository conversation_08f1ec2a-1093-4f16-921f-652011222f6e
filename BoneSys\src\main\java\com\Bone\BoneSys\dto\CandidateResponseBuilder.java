package com.Bone.BoneSys.dto;

import com.Bone.BoneSys.util.DataValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 候选患者响应构建器
 * 负责构建和验证候选患者列表响应
 */
@Slf4j
public class CandidateResponseBuilder {
    
    /**
     * 构建增强的候选患者列表响应
     */
    public static EnhancedCandidateListResponse buildResponse(Page<EnhancedCandidateItem> candidatePage) {
        if (candidatePage == null) {
            return createEmptyResponse();
        }
        
        try {
            // 验证和清理数据
            List<EnhancedCandidateItem> validatedCandidates = 
                DataValidationUtil.validateCandidateList(candidatePage.getContent());
            
            // 创建分页信息
            PaginationInfo pagination = new PaginationInfo(
                candidatePage.getNumber() + 1, // 转换为1基索引
                candidatePage.getSize(),
                candidatePage.getTotalPages(),
                candidatePage.getTotalElements(),
                candidatePage.hasNext(),
                candidatePage.hasPrevious()
            );
            
            // 记录数据质量信息
            String qualityReport = DataValidationUtil.generateDataQualityReport(validatedCandidates);
            log.info("候选患者数据质量: {}", qualityReport);
            
            return new EnhancedCandidateListResponse(validatedCandidates, pagination);
            
        } catch (Exception e) {
            log.error("构建候选患者响应时发生错误", e);
            return createEmptyResponse();
        }
    }
    
    /**
     * 构建增强的候选患者列表响应（从列表）
     */
    public static EnhancedCandidateListResponse buildResponse(
            List<EnhancedCandidateItem> candidates, 
            int currentPage, 
            int totalPages, 
            int totalElements, 
            int pageSize) {
        
        try {
            // 验证和清理数据
            List<EnhancedCandidateItem> validatedCandidates = 
                DataValidationUtil.validateCandidateList(candidates);
            
            // 创建分页信息
            PaginationInfo pagination = new PaginationInfo(
                currentPage, pageSize, totalPages, (long) totalElements,
                currentPage < totalPages, currentPage > 1);
            
            return new EnhancedCandidateListResponse(validatedCandidates, pagination);
            
        } catch (Exception e) {
            log.error("构建候选患者响应时发生错误", e);
            return createEmptyResponse();
        }
    }
    
    /**
     * 创建空响应
     */
    public static EnhancedCandidateListResponse createEmptyResponse() {
        return new EnhancedCandidateListResponse(
            List.of(),
            new PaginationInfo(1, 10, 1, 0L, false, false)
        );
    }
    
    /**
     * 创建错误响应
     */
    public static EnhancedCandidateListResponse createErrorResponse(String errorMessage) {
        log.error("创建错误响应: {}", errorMessage);
        return createEmptyResponse();
    }
    
    /**
     * 验证响应数据完整性
     */
    public static boolean validateResponse(EnhancedCandidateListResponse response) {
        if (response == null) {
            return false;
        }
        
        if (response.getCandidates() == null || response.getPagination() == null) {
            return false;
        }
        
        // 验证分页信息的合理性
        PaginationInfo pagination = response.getPagination();
        if (pagination.getCurrentPage() < 1 || 
            pagination.getTotalPages() < 1 || 
            pagination.getTotalRecords() < 0 || 
            pagination.getPageSize() < 1) {
            return false;
        }
        
        // 验证候选患者数据
        for (EnhancedCandidateItem candidate : response.getCandidates()) {
            if (!candidate.isValid()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取响应统计信息
     */
    public static String getResponseStatistics(EnhancedCandidateListResponse response) {
        if (response == null || response.getCandidates() == null) {
            return "响应为空";
        }
        
        List<EnhancedCandidateItem> candidates = response.getCandidates();
        PaginationInfo pagination = response.getPagination();
        
        int withRecords = (int) candidates.stream().mapToLong(c -> c.hasRecords() ? 1 : 0).sum();
        int withTreatments = (int) candidates.stream().mapToLong(c -> c.hasTreatments() ? 1 : 0).sum();
        
        return String.format(
            "响应统计: 当前页=%d/%d, 本页记录=%d, 总记录=%d, 有档案=%d, 有治疗=%d",
            pagination.getCurrentPage(),
            pagination.getTotalPages(),
            candidates.size(),
            pagination.getTotalRecords(),
            withRecords,
            withTreatments
        );
    }
}