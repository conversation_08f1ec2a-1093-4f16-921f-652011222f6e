.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.box_1 {
  height: 55.89vw;
  background: url(./img/6fb3e10f3e96c7288259592a9ab58684.png)
    0vw -0.37vw no-repeat;
  background-size: 99.79vw 56.25vw;
  margin-left: 0.21vw;
  width: 99.8vw;
}

.section_1 {
  height: 56.25vw;
  background: url(./img/b111809ee101ba601c84c4c92f0294ca.png)
    0vw -0.37vw no-repeat;
  background-size: 99.79vw 56.61vw;
  width: 99.8vw;
}

.block_1 {
  width: 100vw;
  height: 56.25vw;
  background: url(./img/86810806097762d56f754980fac2b438.png)
    0vw -0.32vw no-repeat;
  background-size: 100vw 56.56vw;
  margin-left: -0.2vw;
}

.section_2 {
  width: 52.24vw;
  height: 3.65vw;
  margin: 0.67vw 0 0 5.57vw;
}

.image_1 {
  width: 8.81vw;
  height: 3.65vw;
}

.text_1 {
  width: 15.32vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.47vw;
}

.section_3 {
  width: 82.56vw;
  height: 4.74vw;
  margin: 2.76vw 0 0 7.6vw;
}

.group_1 {
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 24.69vw;
  height: 3.44vw;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin-top: 0.47vw;
}

.text_2 {
  width: 7.97vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 1.87vw;
}

.image_2 {
  width: 0.06vw;
  height: 2.24vw;
  margin: 0.67vw 0 0 1.56vw;
}

.text_3 {
  width: 4.95vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 3.9vw 0 4.37vw;
}

.group_2 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 18.03vw;
  height: 3.44vw;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 0.46vw 0 0 3.02vw;
}

.text_4 {
  width: 3.65vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 1.92vw;
}

.image_3 {
  width: 0.06vw;
  height: 2.24vw;
  margin: 0.67vw 0 0 1.4vw;
}

.text_5 {
  width: 4.95vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 3.07vw 0 2.96vw;
}

.group_3 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 18.03vw;
  height: 3.44vw;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 0.52vw 0 0 3.75vw;
}

.text_6 {
  width: 3.86vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 1.25vw;
}

.image_4 {
  width: 0.06vw;
  height: 2.24vw;
  margin: 0.67vw 0 0 1.4vw;
}

.text_7 {
  width: 4.95vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 3.85vw 0 2.65vw;
}

.text-wrapper_1 {
  height: 4.74vw;
  background: url(./img/a4bebd0b7c83d15be147f3b345ef791d.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 3.23vw;
  width: 11.83vw;
}

.text_8 {
  width: 4.17vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 3.69vw;
}

.section_4 {
  width: 87.97vw;
  height: 40.79vw;
  background: url(./img/79c3ec967a296bb1f063e3795e500ab9.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 1.14vw 0 2.5vw 5.88vw;
}

.text-wrapper_2 {
  width: 71.25vw;
  height: 1.78vw;
  margin: 2.44vw 0 0 8.75vw;
}

.text_9 {
  width: 8.13vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_10 {
  width: 3.7vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 8.39vw;
}

.text_11 {
  width: 8.18vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 11.98vw;
}

.text_12 {
  width: 3.91vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 12.4vw;
}

.text_13 {
  width: 3.91vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 10.68vw;
}

.image_5 {
  width: 76.88vw;
  height: 0.06vw;
  margin: 0.83vw 0 0 6.04vw;
}

.block_2 {
  width: 73.65vw;
  height: 2.45vw;
  margin: 0.62vw 0 0 8.9vw;
}

.text_14 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.42vw;
}

.text_15 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.31vw 0 0 6.35vw;
}

.text_16 {
  width: 5.99vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.31vw 0 0 10.88vw;
}

.image-text_1 {
  width: 10.94vw;
  height: 1.98vw;
  margin: 0.31vw 0 0 8.59vw;
}

.label_1 {
  width: 2.04vw;
  height: 1.93vw;
  margin-top: 0.06vw;
}

.text-group_1 {
  width: 8.18vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text-wrapper_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 4.95vw;
  width: 4.64vw;
}

.text_17 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.46vw 0 0 0.72vw;
}

.text-wrapper_4 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.73vw;
  width: 4.64vw;
}

.text_18 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.46vw 0 0 0.72vw;
}

.image_6 {
  width: 76.88vw;
  height: 0.06vw;
  margin: 0.57vw 0 0 6.04vw;
}

.block_3 {
  width: 73.65vw;
  height: 2.5vw;
  margin: 0.62vw 0 0 8.9vw;
}

.text_19 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.58vw;
}

.text_20 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.46vw 0 0 6.35vw;
}

.text_21 {
  width: 5.99vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.41vw 0 0 10.88vw;
}

.image-text_2 {
  width: 10.84vw;
  height: 2.04vw;
  margin: 0.46vw 0 0 8.69vw;
}

.label_2 {
  width: 1.98vw;
  height: 1.98vw;
  margin-top: 0.06vw;
}

.text-group_2 {
  width: 8.18vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text-wrapper_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 4.95vw;
  width: 4.64vw;
}

.text_22 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.46vw 0 0 0.72vw;
}

.text-wrapper_6 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.73vw;
  width: 4.64vw;
}

.text_23 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.46vw 0 0 0.72vw;
}

.image-wrapper_1 {
  height: 0.06vw;
  background: url(./img/982c824b98c17706cb7e17a9100be65c.png)
    0vw 0vw no-repeat;
  background-size: 76.87vw 0.1vw;
  width: 76.88vw;
  margin: 0.52vw 0 0 6.04vw;
}

.image_7 {
  width: 76.88vw;
  height: 0.06vw;
}

.block_4 {
  width: 73.65vw;
  height: 2.45vw;
  margin: 0.62vw 0 0 8.9vw;
}

.text_24 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.53vw;
}

.text_25 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.41vw 0 0 6.35vw;
}

.text_26 {
  width: 3.86vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.36vw 0 0 11.97vw;
}

.label_3 {
  width: 2.4vw;
  height: 2.4vw;
  margin: 0.05vw 0 0 9.63vw;
}

.text_27 {
  width: 5.89vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.41vw 0 0 1.45vw;
}

.text-wrapper_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 6.15vw;
  width: 4.64vw;
}

.text_28 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.46vw 0 0 0.72vw;
}

.text-wrapper_8 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.73vw;
  width: 4.64vw;
}

.text_29 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.46vw 0 0 0.72vw;
}

.image-wrapper_2 {
  height: 0.06vw;
  background: url(./img/3c5d899b5feda3dc325b832e42bab2a8.png)
    0vw 0vw no-repeat;
  background-size: 76.87vw 0.1vw;
  width: 76.88vw;
  margin: 0.57vw 0 0 6.04vw;
}

.image_8 {
  width: 76.88vw;
  height: 0.06vw;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 2.97vw;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 13.55vw;
  margin: 16.04vw 0 5.57vw 71.14vw;
}

.image_9 {
  width: 12.24vw;
  height: 1.98vw;
  margin: 0.46vw 0 0 0.52vw;
}
