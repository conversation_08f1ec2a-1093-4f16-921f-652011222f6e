import http from '@/utils/axios'

/**
 * 档案管理相关API
 */

// 增强的候选患者项目接口
export interface EnhancedCandidateItem {
  patientId: number
  cardId: string
  name: string
  age: string
  gender: string
  visitTime: string      // 格式：YYYY.MM.DD
  treatmentParts: string // 格式：肩颈部, 腰背部
  totalSessions: number
}

// 分页信息接口
export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalRecords: number
  pageSize: number
}

// 增强的候选患者列表响应接口
export interface EnhancedCandidateListResponse {
  candidates: EnhancedCandidateItem[]
  pagination: PaginationInfo
}

// API响应包装接口
export interface ApiResponse<T> {
  success: boolean
  message: string
  data: T
  code?: number
}

// 获取档案列表
export const getRecords = (params: {
  page?: number
  size?: number
  search?: string
  cardId?: string
}) => {
  return http.get('/records', { params })
}

// 获取新建档案候选列表（增强版）
export const getEnhancedCandidates = (params: {
  page?: number
  size?: number
  search?: string
}): Promise<ApiResponse<EnhancedCandidateListResponse>> => {
  return http.get('/records/candidates', { params })
}

// 获取新建档案候选列表（兼容旧版本）
export const getRecordsCandidates = (params: {
  page?: number
  size?: number
  search?: string
}) => {
  return http.get('/records/candidates/legacy', { params })
}

// 创建档案
export const createRecord = (recordData: {
  patientCardId: string
  name: string
  gender: string
  age: string
  contactInfo: string
  recordNumber: string
  diagnosisDescription: string
  bodyParts?: Array<{
    bodyPart: string
    totalUsageCount: number
    totalDurationMinutes: number
  }>
}) => {
  return http.post('/debug/patient-record/create', recordData)
}

// 删除档案
export const deleteRecord = (recordId: string | number, password: string) => {
  return http.delete(`/records/${recordId}`, {
    data: { password }
  })
}

// 验证患者数据一致性
export const validatePatientData = (patientId: number): Promise<ApiResponse<any>> => {
  return http.get(`/records/candidates/validate/${patientId}`)
}

// 批量数据质量检查
export const performQualityCheck = (params: {
  page?: number
  size?: number
}): Promise<ApiResponse<any>> => {
  return http.get('/records/candidates/quality-check', { params })
}

// 获取数据缺失报告
export const getMissingDataReport = (params: {
  page?: number
  size?: number
}): Promise<ApiResponse<any>> => {
  return http.get('/records/candidates/missing-data-report', { params })
}

// 获取患者数据完整性信息
export const getPatientDataCompleteness = (patientId: number): Promise<ApiResponse<any>> => {
  return http.get(`/records/candidates/completeness/${patientId}`)
}

// 测试数据缺失处理逻辑
export const testMissingDataHandling = (): Promise<ApiResponse<any>> => {
  return http.get('/records/candidates/test-missing-data')
}

// 验证数据一致性
export const validateDataConsistency = (): Promise<ApiResponse<any>> => {
  return http.get('/records/candidates/validate-consistency')
}

// 验证单个患者数据一致性
export const validateSinglePatientConsistency = (patientId: number): Promise<ApiResponse<any>> => {
  return http.get(`/records/candidates/validate-consistency/${patientId}`)
}

// 修复数据不一致问题
export const fixDataInconsistencies = (patientIds: number[]): Promise<ApiResponse<any>> => {
  return http.post('/records/candidates/fix-inconsistencies', { patientIds })
} 