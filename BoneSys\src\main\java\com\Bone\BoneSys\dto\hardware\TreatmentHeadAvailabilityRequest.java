package com.Bone.BoneSys.dto.hardware;

import java.util.List;

/**
 * 治疗头可用性检查请求
 */
public class TreatmentHeadAvailabilityRequest {
    
    private String treatmentMode; // 治疗模式：ON_SITE（现场治疗）或 TAKE_AWAY（取走治疗）
    private List<BodyPartPatchRequest> bodyPartPatches; // 各部位的贴片需求列表
    
    // 保持向后兼容的字段（已废弃，但保留以防旧代码使用）
    @Deprecated
    private int requiredCount;
    @Deprecated
    private List<String> bodyParts;
    @Deprecated
    private String patchType;
    
    public TreatmentHeadAvailabilityRequest() {}
    
    public TreatmentHeadAvailabilityRequest(String treatmentMode, List<BodyPartPatchRequest> bodyPartPatches) {
        this.treatmentMode = treatmentMode;
        this.bodyPartPatches = bodyPartPatches;
    }
    
    // 保持向后兼容的构造函数
    @Deprecated
    public TreatmentHeadAvailabilityRequest(int requiredCount, String treatmentMode, List<String> bodyParts, String patchType) {
        this.requiredCount = requiredCount;
        this.treatmentMode = treatmentMode;
        this.bodyParts = bodyParts;
        this.patchType = patchType;
    }
    
    @Deprecated
    public TreatmentHeadAvailabilityRequest(int requiredCount, String treatmentMode, List<String> bodyParts) {
        this.requiredCount = requiredCount;
        this.treatmentMode = treatmentMode;
        this.bodyParts = bodyParts;
        this.patchType = "SHALLOW";
    }
    
    /**
     * 计算总的治疗头需求数量
     */
    public int calculateTotalRequiredCount() {
        if (bodyPartPatches != null && !bodyPartPatches.isEmpty()) {
            return bodyPartPatches.stream()
                .mapToInt(BodyPartPatchRequest::getPatchCount)
                .sum();
        }
        return requiredCount; // 向后兼容
    }
    
    /**
     * 获取浅部贴片需求数量
     */
    public int getShallowPatchCount() {
        if (bodyPartPatches != null) {
            return bodyPartPatches.stream()
                .filter(patch -> "SHALLOW".equalsIgnoreCase(patch.getPatchType()))
                .mapToInt(BodyPartPatchRequest::getPatchCount)
                .sum();
        }
        return "SHALLOW".equalsIgnoreCase(patchType) ? requiredCount : 0;
    }
    
    /**
     * 获取深部贴片需求数量
     */
    public int getDeepPatchCount() {
        if (bodyPartPatches != null) {
            return bodyPartPatches.stream()
                .filter(patch -> "DEEP".equalsIgnoreCase(patch.getPatchType()))
                .mapToInt(BodyPartPatchRequest::getPatchCount)
                .sum();
        }
        return "DEEP".equalsIgnoreCase(patchType) ? requiredCount : 0;
    }
    
    /**
     * 验证请求数据的有效性
     */
    public boolean isValid() {
        // 检查治疗模式
        if (treatmentMode == null || treatmentMode.trim().isEmpty()) {
            return false;
        }
        
        if (!"ON_SITE".equalsIgnoreCase(treatmentMode) && !"TAKE_AWAY".equalsIgnoreCase(treatmentMode)) {
            return false;
        }
        
        // 检查贴片需求列表
        if (bodyPartPatches == null || bodyPartPatches.isEmpty()) {
            // 如果使用旧格式，检查旧格式的有效性
            return isValidLegacyFormat();
        }
        
        // 验证每个贴片需求
        for (BodyPartPatchRequest patch : bodyPartPatches) {
            if (!isValidPatchRequest(patch)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取验证错误信息
     */
    public String getValidationError() {
        if (treatmentMode == null || treatmentMode.trim().isEmpty()) {
            return "治疗模式不能为空";
        }
        
        if (!"ON_SITE".equalsIgnoreCase(treatmentMode) && !"TAKE_AWAY".equalsIgnoreCase(treatmentMode)) {
            return "治疗模式必须为 ON_SITE 或 TAKE_AWAY";
        }
        
        if (bodyPartPatches == null || bodyPartPatches.isEmpty()) {
            String legacyError = getLegacyValidationError();
            return legacyError != null ? legacyError : "贴片需求列表不能为空";
        }
        
        for (int i = 0; i < bodyPartPatches.size(); i++) {
            BodyPartPatchRequest patch = bodyPartPatches.get(i);
            String patchError = getPatchValidationError(patch, i + 1);
            if (patchError != null) {
                return patchError;
            }
        }
        
        return null;
    }
    
    /**
     * 验证单个贴片需求的有效性
     */
    private boolean isValidPatchRequest(BodyPartPatchRequest patch) {
        if (patch == null) {
            return false;
        }
        
        // 检查身体部位
        if (patch.getBodyPart() == null || patch.getBodyPart().trim().isEmpty()) {
            return false;
        }
        
        // 检查贴片类型
        if (patch.getPatchType() == null || 
            (!"SHALLOW".equalsIgnoreCase(patch.getPatchType()) && !"DEEP".equalsIgnoreCase(patch.getPatchType()))) {
            return false;
        }
        
        // 检查贴片数量
        if (patch.getPatchCount() < 1 || patch.getPatchCount() > 4) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取单个贴片需求的验证错误信息
     */
    private String getPatchValidationError(BodyPartPatchRequest patch, int index) {
        if (patch == null) {
            return String.format("第%d个贴片需求不能为空", index);
        }
        
        if (patch.getBodyPart() == null || patch.getBodyPart().trim().isEmpty()) {
            return String.format("第%d个贴片需求的身体部位不能为空", index);
        }
        
        if (patch.getPatchType() == null || 
            (!"SHALLOW".equalsIgnoreCase(patch.getPatchType()) && !"DEEP".equalsIgnoreCase(patch.getPatchType()))) {
            return String.format("第%d个贴片需求的贴片类型必须为 SHALLOW 或 DEEP", index);
        }
        
        if (patch.getPatchCount() < 1 || patch.getPatchCount() > 4) {
            return String.format("第%d个贴片需求的贴片数量必须在1-4之间", index);
        }
        
        return null;
    }
    
    /**
     * 验证旧格式数据的有效性
     */
    private boolean isValidLegacyFormat() {
        if (requiredCount < 1 || requiredCount > 20) {
            return false;
        }
        
        if (patchType != null && 
            !"SHALLOW".equalsIgnoreCase(patchType) && !"DEEP".equalsIgnoreCase(patchType)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取旧格式的验证错误信息
     */
    private String getLegacyValidationError() {
        if (requiredCount < 1 || requiredCount > 20) {
            return "需求数量必须在1-20之间";
        }
        
        if (patchType != null && 
            !"SHALLOW".equalsIgnoreCase(patchType) && !"DEEP".equalsIgnoreCase(patchType)) {
            return "贴片类型必须为 SHALLOW 或 DEEP";
        }
        
        return null;
    }
    
    /**
     * 获取所有身体部位列表
     */
    public List<String> getAllBodyParts() {
        if (bodyPartPatches != null && !bodyPartPatches.isEmpty()) {
            return bodyPartPatches.stream()
                .map(BodyPartPatchRequest::getBodyPart)
                .distinct()
                .collect(java.util.stream.Collectors.toList());
        }
        return bodyParts; // 向后兼容
    }
    
    /**
     * 检查是否使用旧格式
     */
    public boolean isLegacyFormat() {
        return (bodyPartPatches == null || bodyPartPatches.isEmpty()) && 
               (requiredCount > 0 || bodyParts != null || patchType != null);
    }
    
    /**
     * 将旧格式自动转换为新格式
     * 这个方法会在内部自动调用，确保向后兼容性
     */
    public void convertLegacyToNewFormat() {
        if (!isLegacyFormat()) {
            return; // 已经是新格式，无需转换
        }
        
        // 记录转换日志
        System.out.println("Converting legacy format request to new format: " +
                          "requiredCount=" + requiredCount + 
                          ", patchType=" + patchType + 
                          ", bodyParts=" + bodyParts);
        
        // 创建新格式的贴片需求列表
        List<BodyPartPatchRequest> convertedPatches = new java.util.ArrayList<>();
        
        if (requiredCount > 0) {
            String effectivePatchType = (patchType != null) ? patchType : "SHALLOW";
            
            if (bodyParts != null && !bodyParts.isEmpty()) {
                // 如果有指定身体部位，按部位分配
                int patchesPerPart = Math.max(1, requiredCount / bodyParts.size());
                int remainingPatches = requiredCount;
                
                for (int i = 0; i < bodyParts.size() && remainingPatches > 0; i++) {
                    String bodyPart = bodyParts.get(i);
                    int patchCount = Math.min(patchesPerPart, remainingPatches);
                    
                    // 如果是最后一个部位，分配所有剩余的贴片
                    if (i == bodyParts.size() - 1) {
                        patchCount = remainingPatches;
                    }
                    
                    convertedPatches.add(new BodyPartPatchRequest(bodyPart, effectivePatchType, patchCount));
                    remainingPatches -= patchCount;
                }
            } else {
                // 如果没有指定身体部位，创建默认部位
                String defaultBodyPart = "未指定部位";
                convertedPatches.add(new BodyPartPatchRequest(defaultBodyPart, effectivePatchType, requiredCount));
            }
        }
        
        // 设置转换后的新格式数据
        this.bodyPartPatches = convertedPatches;
        
        // 记录转换结果
        System.out.println("Legacy format converted successfully. New format: " + convertedPatches);
    }
    
    /**
     * 获取转换后的贴片需求列表（确保向后兼容性）
     */
    public List<BodyPartPatchRequest> getEffectiveBodyPartPatches() {
        if (bodyPartPatches != null && !bodyPartPatches.isEmpty()) {
            return bodyPartPatches;
        }
        
        // 如果是旧格式，自动转换
        if (isLegacyFormat()) {
            convertLegacyToNewFormat();
            return bodyPartPatches;
        }
        
        return new java.util.ArrayList<>();
    }
    
    /**
     * 获取格式类型描述
     */
    public String getFormatType() {
        return isLegacyFormat() ? "LEGACY" : "NEW";
    }
    
    /**
     * 获取兼容性信息
     */
    public String getCompatibilityInfo() {
        if (isLegacyFormat()) {
            return String.format("使用旧格式API (requiredCount=%d, patchType=%s, bodyParts=%s)，" +
                               "已自动转换为新格式以保持兼容性", 
                               requiredCount, patchType, bodyParts);
        } else {
            return "使用新格式API (bodyPartPatches)";
        }
    }
    
    /**
     * 检查是否有指定类型的贴片需求
     */
    public boolean hasPatchType(String patchType) {
        if (bodyPartPatches != null) {
            return bodyPartPatches.stream()
                .anyMatch(patch -> patchType.equalsIgnoreCase(patch.getPatchType()));
        }
        return patchType.equalsIgnoreCase(this.patchType);
    }
    
    // Getters and Setters
    public String getTreatmentMode() {
        return treatmentMode;
    }
    
    public void setTreatmentMode(String treatmentMode) {
        this.treatmentMode = treatmentMode;
    }
    
    public List<BodyPartPatchRequest> getBodyPartPatches() {
        return bodyPartPatches;
    }
    
    public void setBodyPartPatches(List<BodyPartPatchRequest> bodyPartPatches) {
        this.bodyPartPatches = bodyPartPatches;
    }
    
    // 向后兼容的getter/setter
    @Deprecated
    public int getRequiredCount() {
        return calculateTotalRequiredCount();
    }
    
    @Deprecated
    public void setRequiredCount(int requiredCount) {
        this.requiredCount = requiredCount;
    }
    
    @Deprecated
    public List<String> getBodyParts() {
        return bodyParts;
    }
    
    @Deprecated
    public void setBodyParts(List<String> bodyParts) {
        this.bodyParts = bodyParts;
    }
    
    @Deprecated
    public String getPatchType() {
        return patchType;
    }
    
    @Deprecated
    public void setPatchType(String patchType) {
        this.patchType = patchType;
    }
    
    @Override
    public String toString() {
        if (bodyPartPatches != null && !bodyPartPatches.isEmpty()) {
            return String.format("TreatmentHeadAvailabilityRequest{treatmentMode='%s', bodyPartPatches=%s, " +
                               "totalRequired=%d, shallowRequired=%d, deepRequired=%d}", 
                               treatmentMode, bodyPartPatches, calculateTotalRequiredCount(), 
                               getShallowPatchCount(), getDeepPatchCount());
        } else {
            return String.format("TreatmentHeadAvailabilityRequest{treatmentMode='%s', requiredCount=%d, " +
                               "bodyParts=%s, patchType='%s'} [Legacy Format]", 
                               treatmentMode, requiredCount, bodyParts, patchType);
        }
    }
}