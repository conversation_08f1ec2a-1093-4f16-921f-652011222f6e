package com.Bone.BoneSys.dto.hardware;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 治疗启动请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TreatmentStartRequest {
    
    /**
     * 治疗时长（分钟）
     */
    private Integer duration;
    
    /**
     * 治疗强度（mW/cm²）
     */
    private Integer intensity;
    
    /**
     * 治疗频率（Hz）
     */
    private Integer frequency;
}