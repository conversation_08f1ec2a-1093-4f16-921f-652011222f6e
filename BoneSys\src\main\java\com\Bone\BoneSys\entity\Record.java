package com.Bone.BoneSys.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * 档案实体类
 * 对应数据库表：records
 */
@Entity
@Table(name = "records")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = {"patient", "processes", "bodyPartStats"})
@ToString(exclude = {"patient", "processes", "bodyPartStats"})
public class Record {
    
    /**
     * 档案ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    /**
     * 档案唯一编号
     */
    @Column(name = "record_number", unique = true, nullable = false, length = 50)
    private String recordNumber;
    
    /**
     * 关联的患者
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patient_id", nullable = false)
    private Patient patient;
    
    /**
     * 诊断描述
     */
    @Column(name = "diagnosis_description", columnDefinition = "TEXT")
    private String diagnosisDescription;
    
    /**
     * 已完成的总会话次数
     */
    @Column(name = "sessions_completed_count", nullable = false)
    private Integer sessionsCompletedCount = 0;
    
    /**
     * 建档日期
     */
    @Column(name = "created_at", nullable = false)
    private LocalDate createdAt;
    
    /**
     * 档案下的治疗进程
     */
    @OneToMany(mappedBy = "record", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Process> processes;
    
    /**
     * 档案下的部位统计
     */
    @OneToMany(mappedBy = "record", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BodyPartStat> bodyPartStats;
    
    /**
     * 设置创建日期
     */
    @PrePersist
    public void prePersist() {
        if (this.createdAt == null) {
            this.createdAt = LocalDate.now();
        }
    }
}