# FREEBONE医疗设备管理系统 - API接口文档

**版本**: v1.0.0  
**更新时间**: 2025-07-27  
**基础URL**: `http://localhost:8080/api`

---

## 📋 目录

1. [认证模块](#1-认证模块)
2. [主界面模块](#2-主界面模块)
3. [档案管理模块](#3-档案管理模块)
4. [参数设置模块](#4-参数设置模块)
5. [进程管理模块](#5-进程管理模块)
6. [治疗头管理模块](#6-治疗头管理模块)
7. [系统设置模块](#7-系统设置模块)
8. [实时通知模块](#8-实时通知模块)
9. [通用响应格式](#9-通用响应格式)

---

## 1. 认证模块

### 1.1 用户登录
**页面**: `新版-登录页面.png`

```http
POST /api/auth/login
```

**请求体**:
```json
{
  "password": "string"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 86400
  },
  "timestamp": "2025-07-27T10:00:00"
}
```

---

## 2. 主界面模块

### 2.1 获取主界面数据
**页面**: `新版-主界面.png`

```http
GET /api/dashboard/main
```

**响应**:
```json
{
  "code": 200,
  "message": "主界面数据获取成功",
  "data": {
    "availableHeads": 18,
    "totalHeads": 20,
    "systemInfo": {
      "systemName": "FREEBONE医疗系统",
      "version": "1.0.0",
      "uptime": "72小时15分钟"
    },
    "dataOverview": {
      "totalPatients": 150,
      "totalRecords": 320,
      "totalProcesses": 890,
      "todayProcesses": 8
    }
  }
}
```

---

## 3. 档案管理模块

### 3.1 获取档案管理页面数据
**页面**: `新版-档案管理.png`

```http
GET /api/records
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 10)
- `search`: 搜索关键词 (可选)
- `cardId`: 就诊卡号筛选 (可选)

**响应**:
```json
{
  "code": 200,
  "message": "档案列表获取成功",
  "data": {
    "records": [
      {
        "patientId": 1,
        "cardId": "P001",
        "name": "张三",
        "age": "45岁",
        "gender": "男",
        "totalTreatments": 15,
        "createdDate": "2025-01-15"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 32,
      "totalRecords": 320,
      "pageSize": 10
    }
  }
}
```

### 3.2 删除档案
**页面**: `新版-档案管理-确定删除弹窗.png`

```http
DELETE /api/records/{recordId}
```

**请求体**:
```json
{
  "password": "string"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "删除成功"
}
```

### 3.3 获取患者个人信息
**页面**: `新版-个人信息.png`

```http
GET /api/patients/{patientId}
```

**查询参数**:
- `historyPage`: 治疗历史页码 (默认: 1)
- `historySize`: 治疗历史每页大小 (默认: 10)

**响应**:
```json
{
  "code": 200,
  "message": "患者信息获取成功",
  "data": {
    "basicInfo": {
      "avatarUrl": "/images/avatars/default.png",
      "name": "张三",
      "patientNumber": "P001",
      "cardId": "C001",
      "gender": "男",
      "age": "45岁",
      "phone": "13800138000"
    },
    "treatmentStats": {
      "totalDuration": "25小时30分钟",
      "bodyPartStats": [
        {
          "bodyPart": "肩颈部",
          "duration": "8小时20分钟"
        },
        {
          "bodyPart": "腰背部", 
          "duration": "17小时10分钟"
        }
      ]
    },
    "treatmentHistory": {
      "records": [
        {
          "treatmentDate": "2025-07-25",
          "bodyPart": "腰背部",
          "intensity": "45mW/C",
          "headNumber": 15,
          "duration": "20分钟"
        }
      ],
      "pagination": {
        "currentPage": 1,
        "totalPages": 5,
        "totalRecords": 48
      }
    }
  }
}
```

### 3.4 获取患者诊断详情
**页面**: `新版-个人信息-诊断详细弹窗.png`

```http
GET /api/patients/{patientId}/diagnoses
```

**响应**:
```json
{
  "code": 200,
  "message": "诊断信息获取成功",
  "data": {
    "diagnoses": [
      {
        "diagnosisDate": "2025-07-20",
        "doctorName": "李医生",
        "description": "腰椎间盘突出症，建议进行超声波治疗，每次20分钟，强度45mW/C..."
      }
    ]
  }
}
```

### 3.5 获取新建档案候选列表
**页面**: `新版-新建档案.png`

```http
GET /api/records/candidates
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 10)
- `search`: 搜索关键词 (可选)

**响应**:
```json
{
  "code": 200,
  "message": "候选列表获取成功",
  "data": {
    "candidates": [
      {
        "cardId": "C002",
        "name": "李四",
        "age": "38岁",
        "gender": "女",
        "appointmentTime": "2025-07-27 14:30",
        "bodyPart": "肩颈部",
        "sessions": 3
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5
    }
  }
}
```

### 3.6 获取下一个患者编号
**页面**: `新版-新建档案-患者信息弹窗.png`

```http
GET /api/patients/next-number
```

**响应**:
```json
{
  "code": 200,
  "message": "患者编号生成成功",
  "data": {
    "patientNumber": "P156"
  }
}
```

### 3.7 创建新患者
**页面**: `新版-新建档案-患者信息弹窗.png`

```http
POST /api/patients
```

**请求体**:
```json
{
  "patientNumber": "P156",
  "name": "王五",
  "age": "32",
  "gender": "男",
  "phone": "13900139000",
  "cardId": "C156",
  "diagnosisDescription": "颈椎病"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "患者创建成功",
  "data": {
    "patientId": 156,
    "patientNumber": "P156"
  }
}
```

---

## 4. 参数设置模块

### 4.1 获取治疗参数设置
**页面**: `新版-参数设置.png`

```http
GET /api/settings/treatment-parameters
```

**响应**:
```json
{
  "code": 200,
  "message": "治疗参数获取成功",
  "data": {
    "currentParameters": [
      {
        "bodyPart": "肩颈部",
        "duration": 20,
        "intensity": 45,
        "frequency": 1000,
        "patchType": "SHALLOW"
      },
      {
        "bodyPart": "腰背部",
        "duration": 25,
        "intensity": 60,
        "frequency": 100,
        "patchType": "DEEP"
      }
    ],
    "availableOptions": {
      "intensityOptions": [30, 45, 60],
      "frequencyOptions": [100, 1000],
      "durationRange": {
        "min": 5,
        "max": 60
      },
      "patchTypes": ["SHALLOW", "DEEP"]
    }
  }
}
```

### 4.2 更新治疗参数
**页面**: `新版-参数设置.png`

```http
PUT /api/settings/treatment-parameters
```

**请求体**:
```json
{
  "parameters": [
    {
      "bodyPart": "肩颈部",
      "duration": 20,
      "intensity": 45,
      "frequency": 1000,
      "patchType": "SHALLOW"
    }
  ]
}
```

### 4.3 获取治疗头状态
**页面**: `新版-参数设置-治疗头选择弹窗.png`

```http
GET /api/hardware/heads/status
```

**响应**:
```json
{
  "code": 200,
  "message": "治疗头状态获取成功",
  "data": {
    "upperCompartment": [
      {
        "slotNumber": 1,
        "type": "SHALLOW",
        "status": "CHARGED"
      }
    ],
    "lowerCompartment": [
      {
        "slotNumber": 11,
        "type": "DEEP", 
        "status": "CHARGING"
      }
    ]
  }
}
```

### 4.4 导出参数配置
**页面**: `新版-参数设置-参数下载弹窗.png`

```http
POST /api/settings/export
```

**响应**:
```json
{
  "code": 200,
  "message": "参数导出成功",
  "data": {
    "downloadUrl": "/downloads/parameters_20250727.json",
    "filename": "治疗参数_20250727.json"
  }
}
```

---

## 5. 进程管理模块

### 5.1 获取进程管理页面数据
**页面**: `新版-进程管理.png`

```http
GET /api/processes
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 10)
- `cardId`: 就诊卡号筛选 (可选)
- `patientName`: 患者姓名筛选 (可选)
- `status`: 状态筛选 (可选)

**响应**:
```json
{
  "code": 200,
  "message": "进程列表获取成功",
  "data": {
    "processes": [
      {
        "processId": 1,
        "cardId": "C001",
        "patientName": "张三",
        "bodyPart": "腰背部",
        "status": "TREATING"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 15,
      "totalRecords": 145
    },
    "statusOptions": [
      "TREATING",
      "COMPLETED", 
      "AWAITING_RETURN"
    ]
  }
}
```

### 5.2 获取治疗进程实时数据
**页面**: `新版-治疗进程-本地治疗.png` / `新版-治疗进程-取走治疗.png`

```http
GET /api/processes/{processId}/realtime
```

**响应**:
```json
{
  "code": 200,
  "message": "实时数据获取成功",
  "data": {
    "patientName": "张三",
    "treatmentMode": "ON_SITE",
    "bodyParts": [
      {
        "bodyPart": "腰背部",
        "remainingTime": "15分30秒",
        "intensity": "45mW/C"
      },
      {
        "bodyPart": "肩颈部", 
        "remainingTime": "8分15秒",
        "intensity": "30mW/C"
      }
    ]
  }
}
```

### 5.3 启动治疗进程
**页面**: 参数设置页面治疗启动

```http
POST /api/processes/start
```

**请求体**:
```json
{
  "recordId": 1,
  "treatmentMode": "ON_SITE",
  "treatmentDetails": [
    {
      "bodyPart": "腰背部",
      "headNumber": 15,
      "duration": 20,
      "intensity": 45,
      "frequency": 1000,
      "patchType": "DEEP",
      "patchQuantity": 2
    }
  ]
}
```

**响应**:
```json
{
  "code": 200,
  "message": "治疗进程启动成功",
  "data": {
    "processId": 100,
    "status": "STARTED",
    "totalHeads": 2,
    "startedHeads": [15, 16],
    "message": "成功启动2/2个治疗头"
  }
}
```

---

## 6. 治疗头管理模块

### 6.1 获取治疗头管理页面数据
**页面**: `新版-治疗头管理.png`

```http
GET /api/hardware/heads
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 10)

**响应**:
```json
{
  "code": 200,
  "message": "治疗头列表获取成功",
  "data": {
    "heads": [
      {
        "headNumber": 1,
        "textStatus": "充电结束",
        "iconStatus": "CHARGED",
        "usageCount": 245,
        "totalUsageTime": "48小时30分钟",
        "batteryLevel": 95,
        "lightColor": "GREEN"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalRecords": 20
    }
  }
}
```

---

## 7. 系统设置模块

### 7.1 获取系统设置
**页面**: `新版-设置页面.png`

```http
GET /api/settings/system
```

**响应**:
```json
{
  "code": 200,
  "message": "系统设置获取成功",
  "data": {
    "volume": 75,
    "screenTimeout": "20min",
    "language": "中文",
    "reminderTimes": ["10min", "15min", "20min"]
  }
}
```

### 7.2 更新系统设置
**页面**: `新版-设置页面.png`

```http
PUT /api/settings/system
```

**请求体**:
```json
{
  "volume": 80,
  "screenTimeout": "30min",
  "language": "中文",
  "reminderTimes": ["10min", "20min"]
}
```

---

## 8. 实时通知模块

### 8.1 WebSocket连接
**页面**: 各种实时弹窗通知

```
WebSocket: ws://localhost:8080/ws/notifications
```

**治疗完成通知**:
```json
{
  "type": "TREATMENT_COMPLETED",
  "data": {
    "patientName": "易烊千玺"
  },
  "timestamp": "2025-07-27T15:30:00"
}
```

**取回提醒通知**:
```json
{
  "type": "PICKUP_REMINDER", 
  "data": {
    "headNumbers": [1, 2, 3]
  },
  "timestamp": "2025-07-27T15:35:00"
}
```

**治疗头异常通知**:
```json
{
  "type": "HARDWARE_ERROR",
  "data": {
    "errorCode": "1001",
    "message": "蓝牙连接失败",
    "headNumbers": [5]
  },
  "timestamp": "2025-07-27T15:40:00"
}
```

**治疗头不足通知**:
```json
{
  "type": "INSUFFICIENT_HEADS",
  "data": {
    "errorCode": "1002", 
    "message": "可用治疗头数量不足",
    "requiredCount": 3,
    "availableCount": 1
  },
  "timestamp": "2025-07-27T15:45:00"
}
```

---

## 9. 通用响应格式

### 9.1 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { /* 具体数据 */ },
  "timestamp": "2025-07-27T10:00:00"
}
```

### 9.2 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "详细错误信息",
  "timestamp": "2025-07-27T10:00:00"
}
```

### 9.3 常用状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误
- `1001`: 硬件通信异常
- `1002`: 治疗头数量不足

---

## 📝 接口实现状态

### ✅ 已实现接口
- `POST /api/auth/login` - 用户登录
- `GET /api/dashboard/main` - 主界面数据
- `GET /api/records` - 档案管理页面数据
- `DELETE /api/records/{id}` - 删除档案
- `GET /api/records/candidates` - 新建档案候选列表
- `GET /api/patients/{id}` - 患者个人信息
- `GET /api/patients/{id}/diagnoses` - 患者诊断详情
- `GET /api/patients/next-number` - 获取下一个患者编号
- `POST /api/patients` - 创建新患者
- `GET /api/settings/treatment-parameters` - 治疗参数设置
- `PUT /api/settings/treatment-parameters` - 更新治疗参数
- `GET /api/hardware/heads/status` - 治疗头选择状态
- `POST /api/settings/export` - 参数导出
- `GET /api/processes` - 进程管理页面数据
- `GET /api/processes/{id}/realtime` - 治疗进程实时数据
- `POST /api/processes/start` - 启动治疗进程
- `GET /api/hardware/heads` - 治疗头管理页面数据
- `WebSocket /ws/notifications` - 实时通知推送

### ⚠️ 需要补充接口
- `GET /api/settings/system` - 系统设置
- `PUT /api/settings/system` - 更新系统设置

### 📊 接口覆盖率
- **总接口数**: 20个
- **已实现**: 18个 (90%)
- **待实现**: 2个 (10%)

---

**文档版本**: v1.0.0  
**最后更新**: 2025-07-27  
**维护人员**: 开发团队
