html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.section_1 {
  width: 4.827rem;
  height: 0.667rem;
  margin: -250.614rem 0 0 -54.614rem;
}

.block_1 {
  height: 28.8rem;
  width: 51.2rem;
  position: absolute;
  left: 0;
  top: 0;
}

.image-wrapper_5 {
  width: 4.827rem;
  height: 0.667rem;
  margin: -250.614rem 0 0 -54.614rem;
}

.section_2 {
  width: 4.827rem;
  height: 0.667rem;
}

.image-wrapper_6 {
  width: 1.6rem;
  height: 1.6rem;
  margin: 254.667rem 0 0 31.147rem;
}

.image_3 {
  width: 1.6rem;
  height: 1.6rem;
}

.group_1 {
  width: 11.12rem;
  height: 2.347rem;
  margin: 1.814rem 0 0 20.027rem;
}

.image_1 {
  width: 2.347rem;
  height: 2.347rem;
}

.text_1 {
  width: 7.6rem;
  height: 1.52rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 1.546rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 9.494rem;
  margin-top: 0.4rem;
}

.group_2 {
  width: 4.72rem;
  height: 2rem;
  margin: 2.88rem 0 0 23.414rem;
}

.text-wrapper_1 {
  width: 4.72rem;
  height: 2rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 5.334rem;
}

.paragraph_1 {
  width: 4.72rem;
  height: 2rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 5.334rem;
}

.text_2 {
  width: 4.72rem;
  height: 2rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 1.12rem;
}

.image-wrapper_7 {
  width: 3.2rem;
  height: 3.2rem;
  margin: 2.8rem 0 7.44rem 24.4rem;
}

.image_2 {
  width: 3.2rem;
  height: 3.2rem;
}
