package com.Bone.BoneSys.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据库配置类
 * 配置JPA和事务管理
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.Bone.BoneSys.repository")
@EnableTransactionManagement
public class DatabaseConfig {
    
    // JPA配置已在application.properties中配置
    // 如需要自定义DataSource或EntityManagerFactory，可在此处添加
}