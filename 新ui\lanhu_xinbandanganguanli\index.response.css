.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.block_1 {
  height: 56.25vw;
  background: url(./img/51da9ff83e9a4afabde39995a337d4f7.png)
    0vw 0vw no-repeat;
  background-size: 100vw 56.25vw;
  margin-left: 0.11vw;
  width: 99.9vw;
}

.box_1 {
  width: 99.9vw;
  height: 56.25vw;
  background: url(./img/5ef5f27141c70c1e7e5a0f61a5b734f0.png)
    0vw 0vw no-repeat;
  background-size: 101.66vw 56.97vw;
}

.group_9 {
  width: 52.24vw;
  height: 3.65vw;
  margin: 1.04vw 0 0 5.36vw;
}

.image_1 {
  width: 8.81vw;
  height: 3.65vw;
}

.text_1 {
  width: 15.32vw;
  height: 2.56vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.53vw;
}

.group_10 {
  width: 82.56vw;
  height: 4.74vw;
  margin: 2.6vw 0 0 7.39vw;
}

.group_1 {
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 24.69vw;
  height: 3.44vw;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin-top: 0.68vw;
}

.text_2 {
  width: 7.97vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 1.87vw;
}

.image_2 {
  width: 0.06vw;
  height: 2.24vw;
  margin: 0.67vw 0 0 1.56vw;
}

.text_3 {
  width: 4.95vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 3.9vw 0 4.37vw;
}

.group_2 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 18.03vw;
  height: 3.44vw;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 0.67vw 0 0 3.02vw;
}

.text_4 {
  width: 3.65vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 1.92vw;
}

.image_3 {
  width: 0.06vw;
  height: 2.24vw;
  margin: 0.67vw 0 0 1.4vw;
}

.text_5 {
  width: 4.95vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 3.07vw 0 2.96vw;
}

.text-wrapper_1 {
  height: 4.74vw;
  background: url(./img/e6ee0182dc4d4797b0875b0a7f9e836c.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 25vw;
  width: 11.83vw;
}

.text_6 {
  width: 4.17vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 0 0 3.69vw;
}

.section_3 {
  width: 87.97vw;
  height: 40.79vw;
  background: url(./img/f7ef7ec148e0f2b11bc0f46f49c5c8a0.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 1.3vw 0 2.13vw 5.67vw;
}

.text-wrapper_10 {
  width: 71.25vw;
  height: 1.78vw;
  margin: 2.44vw 0 0 8.75vw;
}

.text_7 {
  width: 8.13vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.06vw;
}

.text_8 {
  width: 3.7vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.05vw 0 0 5.83vw;
}

.text_9 {
  width: 3.86vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 4.9vw;
}

.text_10 {
  width: 3.81vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.05vw 0 0 3.17vw;
}

.text_11 {
  width: 8.18vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.05vw 0 0 5.15vw;
}

.text_12 {
  width: 8.08vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.05vw 0 0 3.9vw;
}

.text_13 {
  width: 3.91vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.05vw 0 0 8.64vw;
}

.image_4 {
  width: 76.88vw;
  height: 0.06vw;
  margin: 0.88vw 0 0 6.04vw;
}

.box_4 {
  width: 72.77vw;
  height: 2.45vw;
  margin: 0.57vw 0 0 9.79vw;
}

.text_14 {
  width: 5.94vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.79vw;
}

.text_15 {
  width: 5.99vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.72vw 0 0 5.88vw;
}

.text_16 {
  width: 1.78vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.78vw 0 0 4.68vw;
}

.text_17 {
  width: 1.25vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.78vw 0 0 5.72vw;
}

.text_18 {
  width: 2.35vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.72vw 0 0 9.16vw;
}

.text_19 {
  width: 8.29vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 5.72vw;
}

.text-wrapper_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 5.99vw;
  width: 4.64vw;
}

.text_20 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 0.72vw;
}

.text-wrapper_4 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.73vw;
  width: 4.64vw;
}

.text_21 {
  width: 3.18vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 0.72vw;
}

.image_5 {
  width: 76.88vw;
  height: 0.06vw;
  margin: 0.62vw 0 0 6.04vw;
}

.box_5 {
  width: 72.77vw;
  height: 2.45vw;
  margin: 0.57vw 0 0 9.79vw;
}

.text_22 {
  width: 5.94vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.94vw;
}

.text_23 {
  width: 5.99vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.88vw 0 0 5.88vw;
}

.text_24 {
  width: 1.78vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.93vw 0 0 4.68vw;
}

.text_25 {
  width: 1.25vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.93vw 0 0 5.72vw;
}

.text_26 {
  width: 2.4vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 9.11vw;
}

.text_27 {
  width: 8.29vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.88vw 0 0 5.72vw;
}

.text-wrapper_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 5.99vw;
  width: 4.64vw;
}

.text_28 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 0.72vw;
}

.text-wrapper_6 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.73vw;
  width: 4.64vw;
}

.text_29 {
  width: 3.18vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 0.72vw;
}

.image-wrapper_1 {
  height: 0.06vw;
  background: url(./img/86bf3c543f63e690373da9f224fe1a88.png)
    0vw -0.06vw no-repeat;
  background-size: 76.87vw 0.1vw;
  width: 76.88vw;
  margin: 0.62vw 0 0 6.04vw;
}

.image_6 {
  width: 76.88vw;
  height: 0.06vw;
}

.box_6 {
  width: 72.77vw;
  height: 2.45vw;
  margin: 0.57vw 0 0 9.79vw;
}

.text_30 {
  width: 5.94vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.89vw;
}

.text_31 {
  width: 5.99vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 5.88vw;
}

.text_32 {
  width: 1.78vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.88vw 0 0 4.68vw;
}

.text_33 {
  width: 1.25vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.88vw 0 0 5.72vw;
}

.text_34 {
  width: 2.35vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 9.16vw;
}

.text-wrapper_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 20vw;
  width: 4.64vw;
}

.text_35 {
  width: 3.13vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 0.72vw;
}

.text-wrapper_8 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 2.45vw;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.73vw;
  width: 4.64vw;
}

.text_36 {
  width: 3.18vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 0.72vw;
}

.image-wrapper_2 {
  height: 0.06vw;
  background: url(./img/69de334a917d2b69b72c6daeca0f5a57.png)
    0vw -0.06vw no-repeat;
  background-size: 76.87vw 0.1vw;
  width: 76.88vw;
  margin: 0.62vw 0 0 6.04vw;
}

.image_7 {
  width: 76.88vw;
  height: 0.06vw;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 2.97vw;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 13.55vw;
  margin: 15.98vw 0 5.57vw 71.14vw;
}

.image_8 {
  width: 12.24vw;
  height: 1.98vw;
  margin: 0.46vw 0 0 0.52vw;
}
