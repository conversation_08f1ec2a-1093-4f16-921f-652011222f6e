@import './base.css';

/* 医疗设备专用样式 - 支持1920x1080全屏显示 */

#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
  position: fixed;
  top: 0;
  left: 0;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 移除原有的响应式布局，使用固定布局适配医疗设备 */
* {
  box-sizing: border-box;
}

/* 禁用滚动条 */
body::-webkit-scrollbar {
  display: none;
}

body {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
