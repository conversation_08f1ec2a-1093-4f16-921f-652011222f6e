package com.Bone.BoneSys.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 应用配置属性类
 */
@Data
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {
    
    /**
     * JWT配置
     */
    private Jwt jwt = new Jwt();
    
    /**
     * 串口配置
     */
    private Serial serial = new Serial();
    
    @Data
    public static class Jwt {
        private String secret = "BoneSysSecretKeyForJWTTokenGeneration2025";
        private Long expiration = 86400000L; // 24小时
    }
    
    @Data
    public static class Serial {
        private String portName = "COM1";
        private Integer baudRate = 115200;
        private Integer dataBits = 8;
        private Integer stopBits = 1;
        private Integer parity = 0;
    }
}