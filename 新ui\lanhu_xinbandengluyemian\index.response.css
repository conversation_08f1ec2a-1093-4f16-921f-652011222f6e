.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.section_1 {
  height: 56.25vw;
  background: url(./img/6ce9f9861ced27679296af9c9ac6c482.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 100vw;
}

.section_2 {
  width: 68.7vw;
  height: 40.58vw;
  margin: 6.04vw 0 0 15.67vw;
}

.box_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  width: 68.7vw;
  height: 40.58vw;
  border: 1px solid rgba(96, 96, 96, 1);
}

.image_1 {
  width: 35.27vw;
  height: 5.68vw;
  margin: 4.73vw 0 0 7.03vw;
}

.text-group_1 {
  width: 13.23vw;
  height: 5.27vw;
  margin: 3.17vw 0 0 7.5vw;
}

.text-wrapper_1 {
  width: 8.18vw;
  height: 2.24vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: ArialMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
  margin-left: 0.16vw;
}

.text_1 {
  width: 8.18vw;
  height: 2.24vw;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 2.6vw;
  font-family: ArialMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.text_2 {
  width: 8.18vw;
  height: 2.24vw;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 2.6vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.62vw;
}

.text_3 {
  width: 13.23vw;
  height: 1.98vw;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 2.6vw;
  font-family: ArialMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.52vw;
  margin-top: 1.05vw;
}

.section_3 {
  width: 32.14vw;
  height: 3.6vw;
  margin: 9.84vw 0 8.28vw 18.22vw;
}

.block_1 {
  width: 17.35vw;
  height: 2.92vw;
  background: url(./img/62c8f553b93483119365b407a30a731f.png) -0.47vw
    0vw no-repeat;
  background-size: 18.33vw 4.06vw;
  margin-top: 0.37vw;
}

.label_1 {
  width: 1.67vw;
  height: 1.88vw;
  margin: 0.46vw 0 0 1.71vw;
}

.box_2 {
  width: 9.22vw;
  height: 1.31vw;
  margin: 0.83vw 0 0 1.25vw;
}

.text_4 {
  width: 7.5vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(116, 116, 116, 1);
  font-size: 1.04vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 0.27vw;
  margin-left: 1.1vw;
}

.image_2 {
  width: 9.22vw;
  height: 0.06vw;
  margin-top: 0.16vw;
}

.label_2 {
  width: 1.25vw;
  height: 1.1vw;
  margin: 0.88vw 1.19vw 0 1.04vw;
}

.block_2 {
  height: 3.6vw;
  background: url(./img/8b5f27b4d61eee989b13cfe693bdf49d.png) -0.47vw
    0vw no-repeat;
  background-size: 13.12vw 4.79vw;
  width: 12.09vw;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  width: 11.78vw;
  height: 3.34vw;
  margin: 0.15vw 0 0 0.15vw;
}

.image-wrapper_1 {
  height: 1.93vw;
  background: url(./img/680c683b8cfaea5137d473c73bd994eb.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1.83vw;
  margin: 0.67vw 0 0 2.23vw;
}

.image_3 {
  width: 1.93vw;
  height: 1.05vw;
  margin: 0.41vw 0 0 -0.36vw;
}

.text_5 {
  width: 3.49vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.4vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 0.93vw 2.6vw 0 1.61vw;
}

.image-wrapper_2 {
  width: 7.19vw;
  height: 2.61vw;
  margin: 4.73vw 0 2.29vw 88.95vw;
}

.image_4 {
  width: 7.19vw;
  height: 2.61vw;
}
