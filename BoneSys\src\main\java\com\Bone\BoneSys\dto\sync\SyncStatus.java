package com.Bone.BoneSys.dto.sync;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 同步状态数据传输对象
 * 包含同步服务的整体运行状态和统计信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncStatus {
    
    /**
     * 最后一次同步时间
     */
    private LocalDateTime lastSyncTime;
    
    /**
     * 最后一次同步是否成功
     */
    private boolean lastSyncSuccess;
    
    /**
     * 总同步次数
     */
    private long totalSyncCount;
    
    /**
     * 成功同步次数
     */
    private long successfulSyncCount;
    
    /**
     * 失败同步次数
     */
    private long failedSyncCount;
    
    /**
     * 平均执行时间（毫秒）
     */
    private double averageExecutionTime;
    
    /**
     * 当前状态
     * RUNNING - 正在执行同步
     * IDLE - 空闲状态
     * ERROR - 错误状态
     * DISABLED - 已禁用
     */
    private String currentStatus;
    
    /**
     * 最后一次错误消息
     */
    private String lastErrorMessage;
    
    /**
     * 获取成功率
     * 
     * @return 成功率百分比 (0-100)
     */
    public double getSuccessRate() {
        if (totalSyncCount == 0) {
            return 0.0;
        }
        return (double) successfulSyncCount / totalSyncCount * 100.0;
    }
    
    /**
     * 获取失败率
     * 
     * @return 失败率百分比 (0-100)
     */
    public double getFailureRate() {
        if (totalSyncCount == 0) {
            return 0.0;
        }
        return (double) failedSyncCount / totalSyncCount * 100.0;
    }
    
    /**
     * 检查服务是否健康
     * 
     * @return 如果最近同步成功且当前状态正常，返回true
     */
    public boolean isHealthy() {
        return !"ERROR".equals(currentStatus) && 
               !"DISABLED".equals(currentStatus) &&
               (lastSyncTime == null || lastSyncSuccess);
    }
    
    /**
     * 获取状态描述
     * 
     * @return 详细的状态描述字符串
     */
    public String getStatusDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("Status: ").append(currentStatus);
        
        if (totalSyncCount > 0) {
            desc.append(", Success Rate: ").append(String.format("%.1f%%", getSuccessRate()));
            desc.append(" (").append(successfulSyncCount).append("/").append(totalSyncCount).append(")");
        }
        
        if (lastSyncTime != null) {
            desc.append(", Last Sync: ").append(lastSyncTime);
        }
        
        if (!lastSyncSuccess && lastErrorMessage != null) {
            desc.append(", Last Error: ").append(lastErrorMessage);
        }
        
        return desc.toString();
    }
    
    /**
     * 获取性能指标摘要
     * 
     * @return 性能指标字符串
     */
    public String getPerformanceSummary() {
        return String.format("Avg Execution: %.1fms, Success Rate: %.1f%%, Total Syncs: %d",
                           averageExecutionTime, getSuccessRate(), totalSyncCount);
    }
}