html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.image-wrapper_1 {
  height: 28.8rem;
  background: url(./img/a05eb3d383cf70e0da6d25beca0e335d.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 0.08rem;
  width: 51.12rem;
}

.image_1 {
  width: 50.934rem;
  height: 26.48rem;
  margin: 2.32rem 0 0 0.187rem;
}

.box_1 {
  height: 28.8rem;
  background: url(./img/a64bc209b8c81865ebcc45621fe19bbe.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 51.174rem;
  position: absolute;
  left: 0.027rem;
  top: 0;
}

.group_1 {
  width: 27.84rem;
  height: 1.867rem;
  margin: 0.534rem 0 0 2.8rem;
}

.image_2 {
  width: 4.507rem;
  height: 1.867rem;
}

.text_1 {
  width: 10.027rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: Microsoft<PERSON>aHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.267rem;
}

.group_2 {
  position: relative;
  width: 49.174rem;
  height: 21.654rem;
  margin: 2.614rem 0 0 0.88rem;
}

.box_2 {
  width: 40.854rem;
  height: 21.654rem;
  background: url(./img/7165f76fa0b053e05201a568aa4fc31f.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 4.187rem;
}

.text-wrapper_1 {
  width: 36rem;
  height: 0.88rem;
  margin: 3.094rem 0 0 3.44rem;
}

.text_2 {
  width: 2rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_3 {
  width: 2rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 4.347rem;
}

.text_4 {
  width: 2rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 4.56rem;
}

.text_5 {
  width: 3.227rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.56rem;
}

.text_6 {
  width: 4.134rem;
  height: 0.854rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.934rem;
}

.text_7 {
  width: 5.254rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.987rem;
}

.image_3 {
  width: 36.854rem;
  height: 0.027rem;
  margin: 0.454rem 0 0 2.24rem;
}

.box_3 {
  width: 33.547rem;
  height: 1.12rem;
  margin: 0.32rem 0 0 3.84rem;
}

.text_8 {
  width: 1.04rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.214rem;
}

.image_4 {
  width: 1.547rem;
  height: 0.88rem;
  margin: 0.187rem 0 0 2.24rem;
}

.text_9 {
  width: 4.187rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.16rem 0 0 1.067rem;
}

.text_10 {
  width: 0.427rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.214rem 0 0 3.254rem;
}

.text_11 {
  width: 0.4rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.214rem 0 0 4.747rem;
}

.text_12 {
  width: 2.854rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.214rem 0 0 4.987rem;
}

.label_1 {
  width: 1.12rem;
  height: 1.12rem;
  margin-left: 5.68rem;
}

.image_5 {
  width: 36.854rem;
  height: 0.027rem;
  margin: 0.427rem 0 0 2.24rem;
}

.box_4 {
  width: 33.547rem;
  height: 1.2rem;
  margin: 0.427rem 0 0 3.84rem;
}

.text_13 {
  width: 1.2rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.187rem;
}

.image_6 {
  width: 1.52rem;
  height: 1.2rem;
  margin-left: 2.08rem;
}

.text_14 {
  width: 3.014rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.134rem 0 0 1.654rem;
}

.text_15 {
  width: 0.507rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.16rem 0 0 3.814rem;
}

.text_16 {
  width: 0.507rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.16rem 0 0 4.667rem;
}

.text_17 {
  width: 2.854rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 4.934rem;
}

.label_2 {
  width: 1.12rem;
  height: 1.12rem;
  margin-left: 5.68rem;
}

.image-wrapper_2 {
  height: 0.027rem;
  background: url(./img/5ff61b1d884414dd419a6d37f974583e.png)
    0rem -0.027rem no-repeat;
  background-size: 36.854rem 0.054rem;
  width: 36.854rem;
  margin: 0.24rem 0 0 2.24rem;
}

.image_7 {
  width: 36.854rem;
  height: 0.027rem;
}

.box_5 {
  width: 33.547rem;
  height: 1.12rem;
  margin: 0.427rem 0 0 3.84rem;
}

.text_18 {
  width: 1.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.16rem;
}

.label_3 {
  width: 1.174rem;
  height: 1.067rem;
  margin-left: 2.107rem;
}

.text_19 {
  width: 3.014rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.107rem 0 0 2rem;
}

.text_20 {
  width: 0.267rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.134rem 0 0 3.894rem;
}

.text_21 {
  width: 0.267rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.134rem 0 0 4.907rem;
}

.text_22 {
  width: 2.854rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.16rem 0 0 5.094rem;
}

.label_4 {
  width: 1.12rem;
  height: 1.12rem;
  margin-left: 5.68rem;
}

.image-wrapper_3 {
  height: 0.027rem;
  background: url(./img/372e95f51139b9c892d32fe3b3ae2b6e.png)
    0rem -0.027rem no-repeat;
  background-size: 36.854rem 0.054rem;
  width: 36.854rem;
  margin: 0.32rem 0 11.52rem 2.24rem;
}

.image_8 {
  width: 36.854rem;
  height: 0.027rem;
}

.image_9 {
  width: 4.507rem;
  height: 4.587rem;
  margin: 7.6rem 0 0 -0.374rem;
}

.image_10 {
  position: absolute;
  left: 0;
  top: 7.6rem;
  width: 4.507rem;
  height: 4.587rem;
}

.text-wrapper_2 {
  width: 1.627rem;
  height: 0.694rem;
  margin: 0.054rem 0 1.387rem 25.12rem;
}

.text_23 {
  width: 1.627rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 0.72rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.307rem;
}
