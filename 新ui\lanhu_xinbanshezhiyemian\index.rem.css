html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.box_1 {
  height: 28.8rem;
  background: url(./img/79feb2122d04465638aba851729e875a.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 51.2rem;
}

.group_1 {
  width: 35.174rem;
  height: 20.774rem;
  margin: 3.094rem 0 0 8.027rem;
}

.block_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  height: 20.774rem;
  border: 1px solid rgba(96, 96, 96, 1);
  width: 35.174rem;
  position: relative;
}

.image-wrapper_1 {
  width: 1.6rem;
  height: 1.6rem;
  margin: 0.827rem 0 0 32.427rem;
}

.image_1 {
  width: 1.6rem;
  height: 1.6rem;
}

.text-wrapper_1 {
  width: 3.494rem;
  height: 1.28rem;
  margin-left: 16.214rem;
}

.text_1 {
  width: 3.494rem;
  height: 1.28rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.group_2 {
  width: 19.094rem;
  height: 1.627rem;
  margin: 2.4rem 0 0 5.894rem;
}

.text_2 {
  width: 5.574rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.093rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.24rem;
}

.text-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 5.227rem;
  margin: -0.054rem 0 0 1.92rem;
}

.text_3 {
  width: 4.08rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 0.534rem;
}

.text-wrapper_3 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 5.227rem;
  margin: -0.054rem -0.054rem 0 1.2rem;
}

.text_4 {
  width: 4.054rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 0.534rem;
}

.group_3 {
  width: 18.374rem;
  height: 1.227rem;
  margin: 1.094rem 0 0 5.92rem;
}

.text_5 {
  width: 5.547rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.093rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.image_2 {
  width: 1.334rem;
  height: 1.2rem;
  margin-left: 3.094rem;
}

.image_3 {
  width: 5.867rem;
  height: 0.08rem;
  margin: 0.587rem 0 0 0.694rem;
}

.image_4 {
  width: 1.334rem;
  height: 1.2rem;
  margin: 0.027rem 0 0 0.507rem;
}

.group_4 {
  width: 16.48rem;
  height: 1.627rem;
  margin: 1.2rem 0 0 5.894rem;
}

.text_6 {
  width: 5.574rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.093rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.294rem;
}

.text-wrapper_4 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 4.614rem;
  margin: -0.054rem 0 0 1.92rem;
}

.text_7 {
  width: 3.28rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 0.694rem;
}

.image-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 15px;
  height: 1.094rem;
  width: 2.587rem;
  margin: 0.294rem 0 0 1.787rem;
}

.label_1 {
  width: 0.88rem;
  height: 0.88rem;
  margin: 0.134rem 0 0 1.414rem;
}

.group_5 {
  width: 17.92rem;
  height: 1.627rem;
  margin: 0.854rem 0 0 5.92rem;
}

.text_8 {
  width: 5.547rem;
  height: 1.12rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.093rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.267rem;
}

.text-wrapper_5 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 4.614rem;
  margin: -0.054rem 0 0 1.92rem;
}

.text_9 {
  width: 1.894rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.374rem 0 0 1.414rem;
}

.text-wrapper_6 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 4.614rem;
  margin: -0.054rem -0.054rem 0 1.28rem;
}

.text_10 {
  width: 3.387rem;
  height: 0.507rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.613rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.774rem;
  margin: 0.614rem 0 0 0.667rem;
}

.group_6 {
  width: 23.894rem;
  height: 1.627rem;
  margin: 0.907rem 0 0 5.92rem;
}

.text_11 {
  width: 5.547rem;
  height: 1.094rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.093rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.08rem;
}

.text-wrapper_7 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 4.614rem;
  margin: -0.054rem 0 0 1.92rem;
}

.text_12 {
  width: 3.227rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 0.747rem;
}

.text-wrapper_8 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 4.614rem;
  margin: -0.054rem 0 0 1.227rem;
}

.text_13 {
  width: 3.227rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 0.747rem;
}

.text-wrapper_9 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 1.734rem;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 4.614rem;
  margin: -0.054rem -0.054rem 0 1.414rem;
}

.text_14 {
  width: 3.28rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 0.694rem;
}

.image-wrapper_3 {
  width: 4.267rem;
  height: 0.667rem;
  margin: 0.88rem 0 1.334rem 15.974rem;
}

.image_5 {
  width: 4.267rem;
  height: 0.667rem;
}

.group_7 {
  background-color: rgba(239, 128, 41, 1);
  border-radius: 7px;
  position: absolute;
  left: 17.52rem;
  top: 8.96rem;
  width: 0.587rem;
  height: 1.147rem;
  border: 3px solid rgba(135, 132, 130, 1);
}

.image-wrapper_4 {
  width: 3.68rem;
  height: 1.334rem;
  margin: 2.427rem 0 1.174rem 44.854rem;
}

.image_6 {
  width: 3.68rem;
  height: 1.334rem;
}
