html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.box_1 {
  height: 28.774rem;
  background: url(./img/1a341ef99c616d2adf6f24df400246f3.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 51.174rem;
  margin: 0.027rem 0 0 0.027rem;
}

.group_1 {
  height: 28.8rem;
  background: url(./img/33e32ea7db779740051708feb5f55caa.png)
    0rem -0.054rem no-repeat;
  background-size: 51.2rem 28.854rem;
  width: 51.2rem;
  margin: -0.027rem 0 0 -0.027rem;
}

.section_1 {
  box-shadow: 4px 7px 52px 1px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 16px;
  height: 25.307rem;
  border: 3px solid rgba(92, 212, 200, 1);
  width: 32.214rem;
  position: relative;
  margin: 1.68rem 0 0 8.827rem;
}

.text-wrapper_1 {
  width: 2.32rem;
  height: 0.88rem;
  margin: 22.347rem 0 0 7.787rem;
}

.text_1 {
  width: 2.32rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
}

.group_2 {
  position: absolute;
  left: 3.84rem;
  top: 0.827rem;
  width: 27.174rem;
  height: 21.627rem;
}

.box_2 {
  width: 10.88rem;
  height: 21.04rem;
  margin-top: 0.587rem;
}

.text_2 {
  width: 2.32rem;
  height: 0.854rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 3.867rem;
}

.box_3 {
  height: 9.787rem;
  background: url(./img/5152c094067a79cd409c54eef84cf3bc.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.134rem;
  width: 10.88rem;
  position: relative;
}

.image-wrapper_1 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 1.654rem 0 0 1.467rem;
}

.image_1 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_2 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_2 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 0.667rem 0 0 1.467rem;
}

.image_3 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_4 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_3 {
  width: 7.28rem;
  height: 0.56rem;
  margin: 0.854rem 0 0 1.467rem;
}

.image_5 {
  width: 3.254rem;
  height: 0.48rem;
  margin-top: 0.08rem;
}

.image_6 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_4 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 0.667rem 0 0 1.467rem;
}

.image_7 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_8 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_5 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 0.854rem 0 2.614rem 1.467rem;
}

.image_9 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_10 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_6 {
  height: 1.334rem;
  background: url(./img/d9d24d14a9e926fb56de667c05530e1b.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 3.254rem;
  position: absolute;
  left: 5.52rem;
  top: 0.774rem;
}

.image_11 {
  width: 3.254rem;
  height: 1.334rem;
}

.image_12 {
  position: absolute;
  left: 5.52rem;
  top: 3.28rem;
  width: 3.254rem;
  height: 1.334rem;
}

.image-wrapper_7 {
  position: absolute;
  left: 1.494rem;
  top: 0.8rem;
  width: 3.254rem;
  height: 1.334rem;
  background: url(./img/44e10394a537fdee3a5270878f1c7ba2.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_13 {
  position: absolute;
  left: 0;
  top: 2.454rem;
  width: 3.254rem;
  height: 1.334rem;
}

.image_14 {
  position: absolute;
  left: 0.027rem;
  top: 1.147rem;
  width: 3.254rem;
  height: 1.334rem;
}

.box_4 {
  height: 9.787rem;
  background: url(./img/81af175f3bb76cdbc04c0e0a963c84af.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.48rem;
  width: 10.88rem;
  position: relative;
}

.image-wrapper_8 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 1.654rem 0 0 1.467rem;
}

.image_15 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_16 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_9 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 0.667rem 0 0 1.467rem;
}

.image_17 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_18 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_10 {
  width: 7.28rem;
  height: 0.56rem;
  margin: 0.854rem 0 0 1.467rem;
}

.image_19 {
  width: 3.254rem;
  height: 0.48rem;
  margin-top: 0.08rem;
}

.image_20 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_11 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 0.667rem 0 0 1.467rem;
}

.image_21 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_22 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_12 {
  width: 7.28rem;
  height: 0.48rem;
  margin: 0.854rem 0 2.614rem 1.467rem;
}

.image_23 {
  width: 3.254rem;
  height: 0.48rem;
}

.image_24 {
  width: 3.254rem;
  height: 0.48rem;
}

.image-wrapper_13 {
  height: 1.334rem;
  background: url(./img/1b43bd65afe98bab350d75e7bba53a41.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 3.254rem;
  position: absolute;
  left: 5.52rem;
  top: 0.774rem;
}

.image_25 {
  width: 3.254rem;
  height: 1.334rem;
}

.image_26 {
  position: absolute;
  left: 5.52rem;
  top: 3.28rem;
  width: 3.254rem;
  height: 1.334rem;
}

.image-wrapper_14 {
  position: absolute;
  left: 1.494rem;
  top: 0.8rem;
  width: 3.254rem;
  height: 1.334rem;
  background: url(./img/c49ec2ffeadf1111877902f6f3472ea7.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_27 {
  position: absolute;
  left: 0;
  top: 2.454rem;
  width: 3.254rem;
  height: 1.334rem;
}

.image_28 {
  position: absolute;
  left: 0.027rem;
  top: 1.147rem;
  width: 3.254rem;
  height: 1.334rem;
}

.box_5 {
  width: 11.76rem;
  height: 18.08rem;
  margin: 1.84rem 0 0 2.48rem;
}

.group_3 {
  width: 11.76rem;
  height: 14.454rem;
  background: url(./img/5f43da0a126981d2eae897b287a64411.png) -0.24rem
    0rem no-repeat;
  background-size: 12.267rem 15.04rem;
}

.group_4 {
  width: 7.947rem;
  height: 1.68rem;
  margin: 2.4rem 0 0 1.867rem;
}

.image_29 {
  width: 1.68rem;
  height: 1.68rem;
}

.text_3 {
  width: 5.467rem;
  height: 1.094rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 1.093rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 6.774rem;
  margin-top: 0.294rem;
}

.paragraph_1 {
  width: 9.414rem;
  height: 5.6rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 1.574rem;
  margin: 2.187rem 0 2.587rem 1.334rem;
}

.group_5 {
  height: 1.84rem;
  background: url(./img/5285ef37f0c7ca55a77fffdac391cdf9.png) -0.267rem
    0rem no-repeat;
  background-size: 6.747rem 2.454rem;
  width: 6.187rem;
  margin: 1.787rem 0 0 2.934rem;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 1.707rem;
  width: 6.027rem;
  margin: 0.08rem 0 0 0.054rem;
}

.text_4 {
  width: 2.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.427rem 0 0 1.92rem;
}

.image_30 {
  width: 1.6rem;
  height: 1.6rem;
  margin-left: 0.454rem;
}
