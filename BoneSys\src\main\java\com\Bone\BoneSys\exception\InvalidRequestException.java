package com.Bone.BoneSys.exception;

import java.util.List;
import java.util.ArrayList;

/**
 * 无效请求异常
 * 当请求参数验证失败时抛出
 */
public class InvalidRequestException extends TreatmentHeadRecommendationException {
    
    private List<String> validationErrors;
    
    public InvalidRequestException(String message) {
        super("INVALID_REQUEST", message, message);
        this.validationErrors = new ArrayList<>();
        this.validationErrors.add(message);
    }
    
    public InvalidRequestException(List<String> validationErrors) {
        super("INVALID_REQUEST", 
              generateUserMessage(validationErrors), 
              generateTechnicalMessage(validationErrors));
        this.validationErrors = new ArrayList<>(validationErrors);
    }
    
    public InvalidRequestException(String message, List<String> validationErrors) {
        super("INVALID_REQUEST", message, generateTechnicalMessage(validationErrors));
        this.validationErrors = new ArrayList<>(validationErrors);
    }
    
    public List<String> getValidationErrors() {
        return new ArrayList<>(validationErrors);
    }
    
    /**
     * 添加验证错误
     */
    public void addValidationError(String error) {
        if (validationErrors == null) {
            validationErrors = new ArrayList<>();
        }
        validationErrors.add(error);
    }
    
    /**
     * 生成用户友好的错误消息
     */
    private static String generateUserMessage(List<String> errors) {
        if (errors == null || errors.isEmpty()) {
            return "请求参数无效";
        }
        
        if (errors.size() == 1) {
            return errors.get(0);
        }
        
        return String.format("请求参数存在%d个错误，请检查后重试", errors.size());
    }
    
    /**
     * 生成技术错误消息
     */
    private static String generateTechnicalMessage(List<String> errors) {
        if (errors == null || errors.isEmpty()) {
            return "Request validation failed";
        }
        
        return "Validation errors: " + String.join("; ", errors);
    }
    
    /**
     * 获取格式化的错误列表
     */
    public String getFormattedErrors() {
        if (validationErrors == null || validationErrors.isEmpty()) {
            return "无具体错误信息";
        }
        
        StringBuilder formatted = new StringBuilder();
        for (int i = 0; i < validationErrors.size(); i++) {
            formatted.append(String.format("%d. %s", i + 1, validationErrors.get(i)));
            if (i < validationErrors.size() - 1) {
                formatted.append("\n");
            }
        }
        
        return formatted.toString();
    }
}