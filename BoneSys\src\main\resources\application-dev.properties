# 开发环境配置

# 数据库配置（开发环境）
spring.datasource.url=*************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=hello.world333

# JPA配置（开发环境）
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# 日志配置（开发环境）
logging.level.com.Bone.BoneSys=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG

# 串口配置（开发环境 - 可能需要调整端口）
serial.port.name=
serial.port.auto-detect=true

# 跨域配置（开发环境）
cors.allowed-origins=http://localhost:3000,http://localhost:8080,http://localhost:8081