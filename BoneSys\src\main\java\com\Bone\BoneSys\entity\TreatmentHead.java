package com.Bone.BoneSys.entity;

import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 治疗头实体类
 * 对应数据库表：treatment_heads
 * 
 * 治疗头编号：1-20
 * 槽位设计：
 * - 上仓（浅部）：槽号1-10
 * - 下仓（深部）：槽号11-20
 * 指示灯颜色：0=关闭, 1=橙色, 2=蓝色, 3=绿色
 */
@Entity
@Table(name = "treatment_heads")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TreatmentHead {
    
    /**
     * 治疗头ID（主键）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "head_id")
    private Long headId;
    
    /**
     * 治疗头编号 (1-20)
     */
    @Column(name = "head_number", unique = true, nullable = false)
    private Integer headNumber;
    
    /**
     * 槽位号 (1-10浅部上仓, 11-20深部下仓)
     */
    @Column(name = "slot_number", unique = true)
    private Integer slotNumber;
    
    /**
     * 指示灯颜色 (0=关闭, 1=橙色, 2=蓝色, 3=绿色)
     */
    @Column(name = "light_color", nullable = false)
    private Integer lightColor = 0;
    
    /**
     * 实时状态（充电中、充电完成、治疗中）
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "realtime_status", nullable = false, length = 20)
    private TreatmentHeadStatus realtimeStatus = TreatmentHeadStatus.CHARGING;
    
    /**
     * 实时电量 (0-100)
     */
    @Column(name = "battery_level")
    private Integer batteryLevel;
    
    /**
     * 累计总使用次数
     */
    @Column(name = "total_usage_count", nullable = false)
    private Integer totalUsageCount = 0;
    
    /**
     * 累计总使用时长（分钟）
     */
    @Column(name = "total_usage_minutes", nullable = false)
    private Integer totalUsageMinutes = 0;
    
    /**
     * 设计的总可用次数
     */
    @Column(name = "max_usage_count", nullable = false)
    private Integer maxUsageCount = 500;
    
    /**
     * 计算使用百分比
     */
    @Transient
    public Double getUsagePercentage() {
        if (maxUsageCount == null || maxUsageCount == 0) {
            return 0.0;
        }
        return (double) totalUsageCount / maxUsageCount * 100;
    }
    
    /**
     * 判断是否需要更换
     */
    @Transient
    public Boolean needsReplacement() {
        return totalUsageCount >= maxUsageCount * 0.9; // 90%时提醒更换
    }
    
    /**
     * 判断电量是否充足
     */
    @Transient
    public Boolean isBatteryLow() {
        return batteryLevel != null && batteryLevel < 20; // 低于20%为电量不足
    }
    
    /**
     * 判断是否为浅部治疗头（上仓）
     */
    @Transient
    public Boolean isShallowCompartment() {
        return slotNumber != null && slotNumber >= 1 && slotNumber <= 10;
    }
    
    /**
     * 判断是否为深部治疗头（下仓）
     */
    @Transient
    public Boolean isDeepCompartment() {
        return slotNumber != null && slotNumber >= 11 && slotNumber <= 20;
    }
    
    /**
     * 获取仓位类型描述
     */
    @Transient
    public String getCompartmentType() {
        if (isShallowCompartment()) {
            return "上仓(浅部)";
        } else if (isDeepCompartment()) {
            return "下仓(深部)";
        } else {
            return "未知";
        }
    }
    
    /**
     * 获取指示灯颜色描述
     */
    @Transient
    public String getLightColorDescription() {
        switch (lightColor) {
            case 0: return "关闭";
            case 1: return "橙色";
            case 2: return "蓝色";
            case 3: return "绿色";
            default: return "未知";
        }
    }
    
    /**
     * 判断指示灯是否开启
     */
    @Transient
    public Boolean isLightOn() {
        return lightColor != null && lightColor > 0;
    }
    
    /**
     * 判断是否正在充电
     */
    @Transient
    public Boolean isCharging() {
        return realtimeStatus == TreatmentHeadStatus.CHARGING;
    }
    
    /**
     * 判断是否充电完成
     */
    @Transient
    public Boolean isCharged() {
        return realtimeStatus == TreatmentHeadStatus.CHARGED;
    }
    
    /**
     * 判断是否正在治疗
     */
    @Transient
    public Boolean isTreating() {
        return realtimeStatus == TreatmentHeadStatus.TREATING;
    }
    
    /**
     * 判断是否可用于治疗（充电完成状态）
     */
    @Transient
    public Boolean isAvailableForTreatment() {
        return realtimeStatus == TreatmentHeadStatus.CHARGED && !isBatteryLow();
    }
    
    /**
     * 获取状态描述
     */
    @Transient
    public String getStatusDescription() {
        switch (realtimeStatus) {
            case CHARGING: return "充电中";
            case CHARGED: return "充电完成";
            case TREATING: return "治疗中";
            default: return "未知状态";
        }
    }
}