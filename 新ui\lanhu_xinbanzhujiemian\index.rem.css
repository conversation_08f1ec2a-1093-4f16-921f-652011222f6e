html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.section_1 {
  height: 28.8rem;
  background: url(./img/a5d457e6086964608e72d50c0edf1e49.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 51.2rem;
}

.section_7 {
  width: 47.2rem;
  height: 1.84rem;
  margin: 0.507rem 0 0 2.774rem;
}

.section_4 {
  height: 1.84rem;
  background: url(./img/5c6ea8aed80b3a597eb2970818e1a4e2.png) -0.027rem -0.027rem
    no-repeat;
  background-size: 6.8rem 1.867rem;
  width: 6.774rem;
}

.image_5 {
  width: 6.427rem;
  height: 1.68rem;
  background: url(./img/2cdfabd530238828dce57f74290be48d.png)
    0rem 0rem no-repeat;
  background-size: 6.427rem 1.68rem;
  margin: 0.08rem 0 0 0.16rem;
}

.group_1 {
  width: 0.88rem;
  height: 0.8rem;
  margin: 0.427rem 0 0 0.534rem;
}

.text_3 {
  width: 4.054rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.534rem;
  margin: 0.4rem 0.534rem 0 0;
}

.text_1 {
  width: 2.587rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(26, 26, 26, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin-top: 0.48rem;
}

.image-wrapper_4 {
  width: 18.4rem;
  height: 2.96rem;
  margin: 2.587rem 0 0 16.507rem;
}

.image_2 {
  width: 18.4rem;
  height: 2.96rem;
}

.image-wrapper_5 {
  width: 36.134rem;
  height: 12.32rem;
  margin: 2.774rem 0 0 7.2rem;
}

.image_3 {
  width: 17.68rem;
  height: 12.32rem;
}

.image_4 {
  width: 16.294rem;
  height: 11.2rem;
  margin-top: 0.48rem;
}

.section_8 {
  width: 8.96rem;
  height: 2.667rem;
  margin: 0.48rem 0 2.667rem 21.067rem;
}

.section_2 {
  height: 2.667rem;
  background: url(./img/804309bf3a6c998e1d267a34afb72f1d.png) -0.8rem -0.4rem
    no-repeat;
  background-size: 10.614rem 4.347rem;
  width: 8.96rem;
}

.text-wrapper_1 {
  height: 2.454rem;
  background: url(./img/c1300af86347b82de267ac251b20c424.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 8.72rem;
  margin: 0.107rem 0 0 0.134rem;
}

.text_2 {
  width: 3.68rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.88rem;
  margin: 0.88rem 0 0 3.467rem;
}
