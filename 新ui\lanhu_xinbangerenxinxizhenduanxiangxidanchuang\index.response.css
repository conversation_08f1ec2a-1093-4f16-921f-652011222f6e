.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.section_1 {
  height: 56.25vw;
  background: url(./img/ae1459a70cc42a3228338312850b789b.png) -0.16vw -0.11vw
    no-repeat;
  background-size: 101.66vw 56.97vw;
  width: 100vw;
}

.box_1 {
  height: 56.25vw;
  background: url(./img/8011ab97892b73a8ac95abaff0b3a6f1.png) -0.16vw -0.11vw
    no-repeat;
  background-size: 101.66vw 56.97vw;
  width: 100vw;
}

.box_2 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 26px;
  width: 63.49vw;
  height: 46.52vw;
  border: 4px solid rgba(62, 160, 149, 1);
  margin: 5.62vw 0 0 18.85vw;
}

.group_1 {
  width: 34.17vw;
  height: 3.81vw;
  margin: 2.13vw 0 0 24.11vw;
}

.text_1 {
  width: 15.32vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.84vw;
}

.image_1 {
  width: 3.75vw;
  height: 3.81vw;
}

.group_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 26px;
  width: 48.18vw;
  height: 26.46vw;
  border: 4px solid rgba(184, 254, 246, 1);
  margin: 3.48vw 0 0 7.7vw;
}

.group_3 {
  height: 3.6vw;
  background: url(./img/ab196a073e26a39b689f8b4623da4884.png) -0.53vw
    0vw no-repeat;
  background-size: 13.17vw 4.79vw;
  width: 12.09vw;
  margin: 2.86vw 0 4.16vw 25.52vw;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 3.34vw;
  width: 11.78vw;
  margin: 0.15vw 0 0 0.1vw;
}

.text_2 {
  width: 7.97vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 0.83vw 0 0 2.18vw;
}
