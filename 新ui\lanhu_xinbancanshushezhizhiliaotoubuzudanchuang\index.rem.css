html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.box_1 {
  height: 28.8rem;
  background: url(./img/66c5f27211164f992364facf79ecf52d.png) -0.107rem
    0rem no-repeat;
  background-size: 51.307rem 28.8rem;
  width: 51.2rem;
}

.group_1 {
  height: 28.8rem;
  background: url(./img/732eb040d6ffb4c5cf58adcb09bc3cc4.png) -1.52rem -214.8rem
    no-repeat;
  background-size: 52.72rem 243.6rem;
  width: 51.2rem;
}

.group_2 {
  height: 20.054rem;
  background: url(./img/dc02f849428605e71b359a0193094b4e.png) -0.24rem
    0rem no-repeat;
  background-size: 16.827rem 20.64rem;
  width: 16.32rem;
  margin: 3.92rem 0 0 17.547rem;
}

.image-wrapper_2 {
  width: 1.6rem;
  height: 1.6rem;
  margin: 0.827rem 0 0 13.6rem;
}

.image_2 {
  width: 1.6rem;
  height: 1.6rem;
}

.box_4 {
  width: 11.12rem;
  height: 2.347rem;
  margin: 1.787rem 0 0 2.48rem;
}

.image_1 {
  width: 2.347rem;
  height: 2.347rem;
}

.text_1 {
  width: 7.6rem;
  height: 1.52rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 1.546rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 9.494rem;
  margin-top: 0.4rem;
}

.box_5 {
  width: 8.374rem;
  height: 2rem;
  margin: 2.747rem 0 8.747rem 4.16rem;
}

.text-wrapper_1 {
  width: 8.374rem;
  height: 2rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 5.334rem;
}

.paragraph_1 {
  width: 8.374rem;
  height: 2rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 5.334rem;
}

.text_2 {
  width: 8.374rem;
  height: 2rem;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 1.12rem;
}
