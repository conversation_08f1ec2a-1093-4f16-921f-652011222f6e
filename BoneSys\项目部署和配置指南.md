# FREEBONE医疗设备管理系统 - 项目部署和配置指南

**版本**: 1.0  
**更新时间**: 2025-07-28  
**适用环境**: 开发、测试、生产

---

## 🚀 快速启动

### 1. 环境要求

- **Java**: JDK 17 或更高版本
- **数据库**: MySQL 8.0+
- **IDE**: IntelliJ IDEA 或 Eclipse
- **硬件**: 串口设备（生产环境）

### 2. 数据库配置

```sql
-- 创建数据库
CREATE DATABASE bonesys CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据结构
source SQL/build_final.sql;

-- 导入测试数据（可选）
source SQL/corrected_test_data.sql;
```

### 3. 应用启动

```bash
# 开发环境启动
./gradlew bootRun

# 或使用批处理文件
start-dev.bat    # 开发环境
start-prod.bat   # 生产环境
```

---

## ⚙️ 硬件配置

### 🔧 模拟硬件 vs 真实硬件切换

系统支持两种硬件模式，通过配置文件控制：

#### 模拟硬件模式（开发/测试）
```properties
# application.properties 或 application-dev.properties
hardware.simulator.enabled=true
```

**特点**:
- ✅ 无需真实硬件设备
- ✅ 完整模拟20个治疗头
- ✅ 实时状态变化
- ✅ 所有硬件指令完整支持
- ✅ 适合前端开发和测试

#### 真实硬件模式（生产环境）
```properties
# application-prod.properties
hardware.simulator.enabled=false

# 串口配置
serial.port.name=COM1                    # Windows: COM1, COM2...
                                         # Linux: /dev/ttyUSB0, /dev/ttyS0...
                                         # macOS: /dev/cu.usbserial-*
serial.port.auto-detect=true            # 自动检测串口
serial.port.baud-rate=115200            # 波特率
serial.port.data-bits=8                 # 数据位
serial.port.stop-bits=1                 # 停止位
serial.port.parity=0                    # 校验位 (0=无校验)
serial.port.timeout=5000                # 超时时间(ms)
```

### 🔌 串口连接配置

#### 自动检测模式（推荐）
```properties
serial.port.auto-detect=true
# 系统会自动选择合适的串口设备
```

#### 手动指定模式
```properties
serial.port.auto-detect=false
serial.port.name=COM3  # 指定具体串口
```

#### 连接管理配置
```properties
# 连接健康检查
serial.connection.health-check.interval=30000    # 30秒检查一次
serial.connection.max-retry-attempts=5           # 最大重试次数
serial.connection.retry-delay=2000               # 重试间隔(ms)
```

---

## 🗄️ 数据库配置

### 开发环境
```properties
# application-dev.properties
spring.datasource.url=***************************************
spring.datasource.username=root
spring.datasource.password=your_password
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
```

### 生产环境
```properties
# application-prod.properties
spring.datasource.url=*************************************
spring.datasource.username=bonesys_user
spring.datasource.password=${DB_PASSWORD}
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
```

---

## 🔄 治疗头同步配置

### 自动同步设置
```properties
# 启用自动同步
treatment-head.sync.enabled=true

# 同步间隔（毫秒）
treatment-head.sync.interval=10000        # 10秒同步一次

# 启动延迟
treatment-head.sync.startup-delay=5000    # 启动后5秒开始同步

# 超时和重试
treatment-head.sync.timeout=5000          # 同步超时时间
treatment-head.sync.retry-attempts=3      # 重试次数
treatment-head.sync.retry-delay=2000      # 重试间隔
```

### 性能监控配置
```properties
# 性能指标报告间隔
hardware.performance.metrics-report.interval=60000  # 1分钟

# 性能数据清理间隔
hardware.performance.cleanup.interval=10000         # 10秒
```

---

## 🌐 网络和安全配置

### 跨域配置
```properties
cors.allowed-origins=http://localhost:3000,http://localhost:8081
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
```

### JWT配置
```properties
jwt.secret=your-secret-key-here
jwt.expiration=86400000  # 24小时
```

### 服务器配置
```properties
server.port=8080
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
```

---

## 📊 日志配置

### 开发环境日志
```properties
# 详细日志用于调试
logging.level.com.Bone.BoneSys=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
```

### 生产环境日志
```properties
# 精简日志用于生产
logging.level.com.Bone.BoneSys=INFO
logging.level.org.springframework.security=WARN
logging.file.name=logs/bonesys.log
logging.logback.rollingpolicy.max-file-size=10MB
logging.logback.rollingpolicy.max-history=30
```

---

## 🔧 硬件通信协议

### 支持的硬件指令

| 指令 | 功能 | 格式 | 说明 |
|------|------|------|------|
| `TRZI` | 查询所有治疗头状态 | `TRZI\r\n` | 获取20个治疗头的完整状态 |
| `TWSC` | 设置指示灯 | `TWSC+数量+头号+颜色...` | 控制治疗头指示灯 |
| `TWSN` | 关闭指示灯 | `TWSN+数量+头号...` | 关闭指定治疗头指示灯 |
| `TWSDT` | 发送治疗参数 | `TWSDT+时间+强度+频率...` | 批量发送治疗参数 |
| `TWS` | 开始治疗 | `TWS+头号+时间+强度+频率` | 启动单个治疗头治疗 |
| `TWZO` | 停止治疗 | `TWZO+头号` | 停止指定治疗头治疗 |

### 通信参数
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控制**: 无

---

## 🚀 部署步骤

### 1. 开发环境部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd BoneSys

# 2. 配置数据库
# 修改 application-dev.properties 中的数据库连接信息

# 3. 启动应用（模拟硬件模式）
./gradlew bootRun --args='--spring.profiles.active=dev'
```

### 2. 生产环境部署

```bash
# 1. 构建应用
./gradlew build

# 2. 配置生产环境
# 修改 application-prod.properties

# 3. 连接真实硬件
# 确保串口设备已连接并配置正确

# 4. 启动应用（真实硬件模式）
java -jar build/libs/BoneSys-1.0.jar --spring.profiles.active=prod
```

### 3. Docker部署（可选）

```dockerfile
FROM openjdk:17-jdk-slim
COPY build/libs/BoneSys-1.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

---

## 🔍 故障排除

### 常见问题

#### 1. 串口连接失败
```
错误: Serial port is not connected
解决: 
- 检查串口设备是否正确连接
- 确认串口名称配置正确
- 检查串口权限（Linux/macOS）
- 尝试自动检测模式
```

#### 2. 数据库连接失败
```
错误: Connection refused
解决:
- 确认MySQL服务已启动
- 检查数据库连接配置
- 确认数据库用户权限
- 检查防火墙设置
```

#### 3. 治疗头同步失败
```
错误: Treatment head sync failed
解决:
- 检查硬件连接状态
- 确认同步配置正确
- 查看详细错误日志
- 尝试手动触发同步
```

### 调试工具

#### 1. 数据库状态检查
```bash
curl http://localhost:8080/api/database/test
```

#### 2. 硬件连接状态
```bash
curl http://localhost:8080/api/hardware/status
```

#### 3. 同步状态检查
```bash
curl http://localhost:8080/api/hardware/treatment-heads/sync/status
```

---

## 📚 相关文档

- **API接口文档**: `docx/API接口文档.md`
- **硬件通信协议**: `docx/硬件指令.md`
- **数据库设计**: `SQL/build_final.sql`
- **系统架构**: `docx/API设计完整性检查报告.md`

---

## 🎯 配置模板

### 开发环境配置模板
```properties
# application-dev.properties
spring.profiles.active=dev
hardware.simulator.enabled=true
spring.datasource.url=***************************************
spring.datasource.username=root
spring.datasource.password=your_dev_password
logging.level.com.Bone.BoneSys=DEBUG
```

### 生产环境配置模板
```properties
# application-prod.properties
spring.profiles.active=prod
hardware.simulator.enabled=false
serial.port.name=COM1
serial.port.auto-detect=true
spring.datasource.url=*************************************
spring.datasource.username=bonesys_user
spring.datasource.password=${DB_PASSWORD}
logging.level.com.Bone.BoneSys=INFO
```

---

**配置完成后，系统即可正常运行！**

如有问题，请参考故障排除部分或查看详细日志。