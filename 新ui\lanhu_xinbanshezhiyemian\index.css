.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1080px;
  background: url(./img/79feb2122d04465638aba851729e875a.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.group_1 {
  width: 1319px;
  height: 779px;
  margin: 116px 0 0 301px;
}

.block_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  height: 779px;
  border: 1px solid rgba(96, 96, 96, 1);
  width: 1319px;
  position: relative;
}

.image-wrapper_1 {
  width: 60px;
  height: 60px;
  margin: 31px 0 0 1216px;
}

.image_1 {
  width: 60px;
  height: 60px;
}

.text-wrapper_1 {
  width: 131px;
  height: 48px;
  margin-left: 608px;
}

.text_1 {
  width: 131px;
  height: 48px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYa<PERSON><PERSON>;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.group_2 {
  width: 716px;
  height: 61px;
  margin: 90px 0 0 221px;
}

.text_2 {
  width: 209px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 9px;
}

.text-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 196px;
  margin: -2px 0 0 72px;
}

.text_3 {
  width: 153px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 20px;
}

.text-wrapper_3 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 196px;
  margin: -2px -2px 0 45px;
}

.text_4 {
  width: 152px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 20px;
}

.group_3 {
  width: 689px;
  height: 46px;
  margin: 41px 0 0 222px;
}

.text_5 {
  width: 208px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.image_2 {
  width: 50px;
  height: 45px;
  margin-left: 116px;
}

.image_3 {
  width: 220px;
  height: 3px;
  margin: 22px 0 0 26px;
}

.image_4 {
  width: 50px;
  height: 45px;
  margin: 1px 0 0 19px;
}

.group_4 {
  width: 618px;
  height: 61px;
  margin: 45px 0 0 221px;
}

.text_6 {
  width: 209px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 11px;
}

.text-wrapper_4 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 72px;
}

.text_7 {
  width: 123px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 0 0 26px;
}

.image-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 15px;
  height: 41px;
  width: 97px;
  margin: 11px 0 0 67px;
}

.label_1 {
  width: 33px;
  height: 33px;
  margin: 5px 0 0 53px;
}

.group_5 {
  width: 672px;
  height: 61px;
  margin: 32px 0 0 222px;
}

.text_8 {
  width: 208px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
}

.text-wrapper_5 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 72px;
}

.text_9 {
  width: 71px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 14px 0 0 53px;
}

.text-wrapper_6 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px -2px 0 48px;
}

.text_10 {
  width: 127px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 23px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 29px;
  margin: 23px 0 0 25px;
}

.group_6 {
  width: 896px;
  height: 61px;
  margin: 34px 0 0 222px;
}

.text_11 {
  width: 208px;
  height: 41px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 3px;
}

.text-wrapper_7 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 72px;
}

.text_12 {
  width: 121px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 0 0 28px;
}

.text-wrapper_8 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 46px;
}

.text_13 {
  width: 121px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 0 0 28px;
}

.text-wrapper_9 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px -2px 0 53px;
}

.text_14 {
  width: 123px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 0 0 26px;
}

.image-wrapper_3 {
  width: 160px;
  height: 25px;
  margin: 33px 0 50px 599px;
}

.image_5 {
  width: 160px;
  height: 25px;
}

.group_7 {
  background-color: rgba(239, 128, 41, 1);
  border-radius: 7px;
  position: absolute;
  left: 657px;
  top: 336px;
  width: 22px;
  height: 43px;
  border: 3px solid rgba(135, 132, 130, 1);
}

.image-wrapper_4 {
  width: 138px;
  height: 50px;
  margin: 91px 0 44px 1682px;
}

.image_6 {
  width: 138px;
  height: 50px;
}
