<template>
    <div class="page flex-col">
      <div class="group_1 flex-col">
      <div class="box_1 flex-col">
        <div class="block_1 flex-row justify-between">
          <img
            class="image_1"
            referrerpolicy="no-referrer"
            src="../assets/images/personalinformation/dc89535b738c9579089ef14b8aa7dc2d.png"
            @click="goBack"
          />
          <span class="text_1">个人信息</span>
        </div>
        <div class="block_2 flex-row justify-between">
          <div class="box_2 flex-row">
            <div class="block_3 flex-col">
              <!-- 头像占位 -->
              <img class="image_5" src="../assets/images/personalinformation/avatar.png" />
          </div>
            <div class="block_4 flex-col">
              <div class="box_4 flex-col"></div>
              <span class="text_3">姓名：{{ patientData.name || '' }}</span>
              <span class="text_4">性别：{{ patientData.sex === 1 ? '男' : patientData.sex === 0 ? '女' : '' }}</span>
              <span class="text_6">年龄：{{ patientData.age || '' }}</span>

              <div class="text-wrapper_1 flex-row">
                <span class="text_5">编号：{{ patientData.patientNumber || '' }}</span>
                <span class="text_7">电话：{{ patientData.phone || '' }}</span>
              </div>
              <div class="text-wrapper_2 flex-row justify-between">
                <span class="text_8">就诊卡号：{{ patientData.medicalRecordId || '' }}</span>
              </div>
            </div>
            <div class="block_5 flex-col">
              <div class="section_1 flex-col">
                <div class="text-wrapper_4 flex-col" @click="showDiagnosisDialog"><span class="text_14">诊断详情</span></div>
              </div>
            </div>
          </div>
          <div class="box_5 flex-col">
            <div class="text-wrapper_5 flex-row">
              <span class="text_15">肩颈部：</span>
              <span class="text_16">{{ treatmentSummary.shoulderNeck || '0' }}min</span>
              <span class="text_17">上肢：</span>
              <span class="text_18">{{ treatmentSummary.upperLimb || '0' }}min</span>
            </div>
            <div class="text-wrapper_6 flex-row">
              <span class="text_19">腰背部：</span>
              <span class="text_20">{{ treatmentSummary.lumbarBack || '0' }}min</span>
              <span class="text_21">下肢：</span>
              <span class="text_22">{{ treatmentSummary.lowerLimb || '0' }}min</span>
            </div>
            <div class="text-wrapper_7 flex-row">
              <span class="text_23">其他部位：</span>
              <span class="text_24">{{ treatmentSummary.other || '0' }}min</span>
              <span class="text_25">髋部：</span>
              <span class="text_26">{{ treatmentSummary.hip || '0' }}min</span>
            </div>
            <div class="group_2 flex-row">
              <div class="text-wrapper_8 flex-col"><span class="text_27">治疗时间统计 </span></div>
            </div>
          </div>
        </div>
        <div class="section_3 flex-col">
          <div class="table-header flex-row">
            <span class="header-item">就诊卡号</span>
            <span class="header-item">姓名</span>
            <span class="header-item">日期</span>
            <span class="header-item">治疗部位</span>
            <span class="header-item">有效声强</span>
            <span class="header-item">治疗头编号</span>
            <span class="header-item">治疗时长</span>
          </div>
          <img
            class="header-divider"
            referrerpolicy="no-referrer"
            src="../assets/images/personalinformation/f6c0e0d2b0627d99de5a294d543c2857.png"
          />
          
          <!-- 治疗记录列表 -->
          <template v-if="paginatedTreatmentRecords.length > 0">
            <div v-for="(record, index) in paginatedTreatmentRecords" :key="index" class="patient-row-container">
              <div class="patient-row flex-row">
                <span class="row-item">{{ record.medicalRecordId || '' }}</span>
                <span class="row-item">{{ record.patientName || '' }}</span>
                <span class="row-item">{{ formatDate(record.treatmentDate) || '' }}</span>
                <span class="row-item">{{ getTreatmentAreaName(record.treatmentArea) || '' }}</span>
                <div class="intensity-cell">
                  <span class="intensity-value">{{ extractIntensityNumber(record.effectiveIntensity) }}</span>
                  <div class="intensity-unit-container">
                    <img class="unit-background" src="../assets/images/personalinformation/矩形.png" />
                    <img class="unit-label" src="../assets/images/personalinformation/duration-label.png" />
                  </div>
                </div>
                <span class="row-item-id">{{ record.treatmentHeadNumber || '' }}</span>
                <span class="row-item-time">{{ record.treatmentDuration || '' }}min</span>
              </div>
              <img
                class="row-divider"
                referrerpolicy="no-referrer"
                src="../assets/images/personalinformation/6a6652c6bb73775047f4c74738e4cdaf.png"
              />
            </div>
          </template>
          <div v-else class="no-data">
            暂无治疗记录
          </div>
          
          <!-- 分页控件 -->
          <div class="pagination-container" v-if="totalTreatmentPages > 1">
            <div class="pagination-controls">
              <div class="pagination-btn prev-btn"
                  @click="prevTreatmentPage"
                  :class="{ disabled: currentTreatmentPage === 1 }">
                <img src="../assets/images/patientmangement/pscfuqtlgklge35gqvwmjemf1ftzt5cw0k164a5578-5e3c-4723-bc66-875bb1108d45.png" />
              </div>
              <div class="page-info">
                第 {{ currentTreatmentPage }} 页/共 {{ totalTreatmentPages }} 页
              </div>
              <div class="pagination-btn next-btn"
                  @click="nextTreatmentPage"
                  :class="{ disabled: currentTreatmentPage === totalTreatmentPages }">
                <img src="../assets/images/patientmangement/psmqhsvt3d8c3z2wc722ghc6ga6sph03fs58fbf288-2ea0-4006-995a-1fb501817a56.png" />
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
  
    <!-- 诊断详情弹窗 - 恢复原始风格 -->
    <div v-if="showDiagnosisDialogFlag" class="diagnosis-dialog-overlay" @click.self="closeDiagnosisDialog">
      <div class="diagnosis-box flex-col">
        <div class="diagnosis-content-box flex-col">
          <div class="diagnosis-header">
            <span class="diagnosis-title">诊断详情</span>
            <img
              class="diagnosis-close"
              referrerpolicy="no-referrer"
              src="../assets/images/personalinformation/a9c5385f3faed3fd6264136b3ea77b1e.png"
              @click="closeDiagnosisDialog"
            />
          </div>
          <div class="diagnosis-body flex-col">
            <textarea 
              class="diagnosis-textarea" 
              v-model="editableDiagnosis"
              placeholder="请输入诊断信息"
            ></textarea>
          </div>
          <div class="diagnosis-footer flex-col" @click="saveDiagnosis">
            <div class="diagnosis-confirm flex-col">
              <span class="diagnosis-confirm-text">确认修改</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

  </template>
  
<script>
  import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { getPatientById, getPatientByCardId, updatePatientDiagnosis } from '@/api/patients';
import http from '@/utils/axios';
  
export default {
  name: 'PatientDetailView',
  setup() {
    const route = useRoute();
  const router = useRouter();
  
      // 患者数据
    const patientData = ref({
      id: '',
      name: '',
      sex: null,
      age: '',
      phone: '',
      medicalRecordId: '',
      diagnosis: '',
      creationTimestamp: ''
    });

    // 治疗记录数据
    const treatmentRecords = ref([]);

    // 治疗记录分页
    const currentTreatmentPage = ref(1);
    const treatmentPageSize = ref(3);

    // 诊断详情弹窗
    const showDiagnosisDialogFlag = ref(false);
    
    // 可编辑的诊断信息
    const editableDiagnosis = ref('');

    // 治疗汇总数据
    const treatmentSummary = ref({
      shoulderNeck: 0,
      upperLimb: 0,
      lumbarBack: 0,
      lowerLimb: 0,
      hip: 0,
      other: 0,
      total: 0
    });

    // 计算当前页的治疗记录
    const paginatedTreatmentRecords = computed(() => {
      const startIndex = (currentTreatmentPage.value - 1) * treatmentPageSize.value;
      return treatmentRecords.value.slice(startIndex, startIndex + treatmentPageSize.value);
    });

    // 计算治疗记录总页数
    const totalTreatmentPages = computed(() => {
      return Math.ceil(treatmentRecords.value.length / treatmentPageSize.value);
    });

    // 根据患者ID获取患者详细信息
    const fetchPatientDetailByPatientId = async (patientId) => {
      try {
        console.log('获取患者详情，患者ID:', patientId);
        
        const response = await getPatientById(patientId, {
          historyPage: 1,
          historySize: 10
        });
        
        if (response.data && response.data.basicInfo) {
          const basicInfo = response.data.basicInfo;
          patientData.value = {
            id: patientId,
            name: basicInfo.name,
            sex: basicInfo.gender === '男' ? 1 : basicInfo.gender === '女' ? 0 : null,
            age: basicInfo.age?.replace('岁', '') || '',
            phone: basicInfo.phone,
            medicalRecordId: basicInfo.cardId,
            patientNumber: basicInfo.patientNumber,
            // 确保诊断信息来自records表的diagnosis_description字段
            diagnosis: basicInfo.diagnosis || '暂无诊断信息'
          };
          
          console.log('患者详情获取成功:', patientData.value);
          console.log('诊断信息:', patientData.value.diagnosis);
          
          // 处理治疗记录数据
          if (response.data.treatmentHistory && response.data.treatmentHistory.records && response.data.treatmentHistory.records.length > 0) {
            treatmentRecords.value = response.data.treatmentHistory.records.map(record => ({
              medicalRecordId: patientData.value.medicalRecordId,
              patientName: patientData.value.name,
              treatmentDate: record.treatmentDate,
              treatmentArea: record.bodyPart,
              effectiveIntensity: record.intensity,
              treatmentHeadNumber: record.headNumber,
              treatmentDuration: record.duration
            }));
            
            console.log('治疗记录数据:', treatmentRecords.value);
          } else {
            console.log('无治疗记录数据');
            treatmentRecords.value = [];
          }
          
          // 处理治疗汇总数据
          if (response.data.treatmentStats && response.data.treatmentStats.bodyPartStats) {
            const stats = response.data.treatmentStats.bodyPartStats;
            const summary = {
              shoulderNeck: 0,
              upperLimb: 0,
              lumbarBack: 0,
              lowerLimb: 0,
              hip: 0,
              other: 0,
              total: 0
            };
            
            stats.forEach(stat => {
              const duration = parseDuration(stat.duration);
              switch (stat.bodyPart) {
                case '肩颈部':
                  summary.shoulderNeck = duration;
                  break;
                case '上肢':
                  summary.upperLimb = duration;
                  break;
                case '腰背部':
                  summary.lumbarBack = duration;
                  break;
                case '下肢':
                  summary.lowerLimb = duration;
                  break;
                case '髋部':
                  summary.hip = duration;
                  break;
                default:
                  summary.other = duration;
                  break;
              }
            });
            
            // 计算总时长
            summary.total = parseDuration(response.data.treatmentStats.totalDuration);
            treatmentSummary.value = summary;
            
            console.log('治疗汇总数据:', treatmentSummary.value);
          }
        } else {
          MessagePlugin.error('未找到该患者信息');
        }
      } catch (error) {
        console.error('获取患者详情失败', error);
        MessagePlugin.error('获取患者详情失败，请稍后重试');
      }
    };
    
    // 解析时间字符串为分钟数
    const parseDuration = (durationStr) => {
      if (!durationStr) return 0;
      
      const hourMatch = durationStr.match(/(\d+)小时/);
      const minuteMatch = durationStr.match(/(\d+)分钟/);
      
      const hours = hourMatch ? parseInt(hourMatch[1]) : 0;
      const minutes = minuteMatch ? parseInt(minuteMatch[1]) : 0;
      
      return hours * 60 + minutes;
    };

    // 计算治疗汇总数据
    const calculateTreatmentSummary = () => {
      const summary = {
        shoulderNeck: 0,
        upperLimb: 0,
        lumbarBack: 0,
        lowerLimb: 0,
        hip: 0,
        other: 0,
        total: 0
      };

      treatmentRecords.value.forEach(record => {
        const duration = parseDuration(record.treatmentDuration);
        const area = record.treatmentArea;
        
        // 根据治疗部位累计时间
        if (area === 1 || area === '肩颈部') {
          summary.shoulderNeck += duration;
        } else if (area === 2 || area === '上肢') {
          summary.upperLimb += duration;
        } else if (area === 3 || area === '腰背部') {
          summary.lumbarBack += duration;
        } else if (area === 4 || area === '下肢') {
          summary.lowerLimb += duration;
        } else if (area === 5 || area === '髋部') {
          summary.hip += duration;
        } else {
          summary.other += duration;
        }
        
        summary.total += duration;
      });

      treatmentSummary.value = summary;
      console.log('治疗汇总计算完成:', treatmentSummary.value);
    };

    // 获取治疗部位名称
    const getTreatmentAreaName = (area) => {
      // 直接返回治疗部位名称，后端已经返回中文名称
      return area || '其他部位';
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}.${date.getMonth() + 1}.${date.getDate()}`;
    };

    // 提取强度数字部分
    const extractIntensityNumber = (intensity) => {
      if (!intensity) return '';
      // 提取数字部分，如从"600.00mW/C"中提取"600"
      const match = intensity.toString().match(/^(\d+(?:\.\d+)?)/);
      return match ? Math.floor(parseFloat(match[1])).toString() : '';
    };

    // 返回上一页
    const goBack = () => {
      router.go(-1);
    };

    // 显示诊断详情弹窗 - 添加调试信息
    const showDiagnosisDialog = () => {
      console.log('点击了诊断详情按钮');
      console.log('当前患者数据:', patientData.value);
      console.log('当前诊断信息:', patientData.value.diagnosis);
      
      // 从数据库中获取最新的诊断信息
      editableDiagnosis.value = patientData.value.diagnosis || '';
      showDiagnosisDialogFlag.value = true;
      
      console.log('弹窗状态设置为:', showDiagnosisDialogFlag.value);
      console.log('可编辑诊断内容:', editableDiagnosis.value);
    };

    // 关闭诊断详情弹窗
    const closeDiagnosisDialog = () => {
      showDiagnosisDialogFlag.value = false;
    };

    // 保存诊断信息
    const saveDiagnosis = async () => {
      try {
        // 验证输入
        if (!editableDiagnosis.value.trim()) {
          MessagePlugin.warning('请输入诊断信息');
          return;
        }

        // 调用API更新诊断信息
        const response = await updatePatientDiagnosis(
          patientData.value.id.toString(), 
          editableDiagnosis.value.trim()
        );

        if (response.status === 200) {
          // 更新本地数据
          patientData.value.diagnosis = editableDiagnosis.value.trim();
          // 关闭弹窗
          closeDiagnosisDialog();
          MessagePlugin.success('诊断信息保存成功');
        } else {
          MessagePlugin.error('保存失败，请重试');
        }
      } catch (error) {
        console.error('保存诊断信息失败', error);
        if (error.response && error.response.data && error.response.data.message) {
          MessagePlugin.error(`保存失败: ${error.response.data.message}`);
        } else {
          MessagePlugin.error('保存诊断信息失败，请稍后重试');
        }
      }
    };

    // 治疗记录分页 - 上一页
    const prevTreatmentPage = () => {
      if (currentTreatmentPage.value > 1) {
        currentTreatmentPage.value--;
      }
    };

    // 治疗记录分页 - 下一页
    const nextTreatmentPage = () => {
      if (currentTreatmentPage.value < totalTreatmentPages.value) {
        currentTreatmentPage.value++;
      }
    };

    // 组件挂载时获取数据
    onMounted(() => {
       // 从路由参数中获取患者ID或就诊卡号
       const patientId = route.params.patientId || route.query.patientId;
       
       if (patientId) {
         // 检查是否为数字ID（patientId）还是字符串ID（cardId）
         if (/^\d+$/.test(patientId)) {
           // 纯数字，作为patientId处理
           fetchPatientDetailByPatientId(patientId);
         } else {
           // 包含字母或特殊字符，作为cardId处理，先查找对应的patientId
           fetchPatientByCardId(patientId);
         }
       } else {
         MessagePlugin.warning('未找到患者标识');
       }
     });
     
     // 通过cardId查找并获取患者详情
     const fetchPatientByCardId = async (cardId) => {
       try {
         console.log('通过卡号查找患者:', cardId);
         
         // 使用原有的debug接口通过cardId查找
         const response = await getPatientByCardId(cardId);
         
         if (response.data && response.data.patient) {
           const patientId = response.data.patient.id;
           console.log('找到患者ID:', patientId);
           // 使用找到的patientId获取详细信息
           await fetchPatientDetailByPatientId(patientId);
         } else {
           MessagePlugin.error('未找到该患者信息');
         }
       } catch (error) {
         console.error('通过卡号查找患者失败:', error);
         MessagePlugin.error('查找患者信息失败，请稍后重试');
       }
     };

    return {
      patientData,
      treatmentRecords,
      treatmentSummary,
      showDiagnosisDialogFlag,
      editableDiagnosis,
      currentTreatmentPage,
      treatmentPageSize,
      totalTreatmentPages,
      paginatedTreatmentRecords,
      goBack,
      formatDate,
      getTreatmentAreaName,
      extractIntensityNumber,
      showDiagnosisDialog,
      closeDiagnosisDialog,
      saveDiagnosis,
      prevTreatmentPage,
      nextTreatmentPage,
      calculateTreatmentSummary,
      parseDuration
    };
  }
};
</script>
<style scoped lang="css">
/* 保留原有样式不变 */
  .page {
    background-color: rgba(255, 255, 255, 1);
    position: relative;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
  }
  
  .group_1 {
  height: 1078px;
  background: url(../assets/images/personalinformation/4da74a82a76211bac6221a1e0edabddc.png) -3px -2px
      no-repeat;
  background-size: 1920px 1080px;
  width: 1917px;
    position: relative;
  }
  
.box_1 {
  position: absolute;
  width: 1920px;
  height: 1080px;
  background: url(../assets/images/personalinformation/c30b12fbf3037e890c790dcd36bdf001.png) -3px -2px
    no-repeat;
  background-size: 1923px 1082px;
}

.block_1 {
  width: 1004px;
  height: 70px;
  margin: 18px 0 0 100px;
  }
  
  .image_1 {
  width: 169px;
  height: 70px;
    cursor: pointer;
}

.text_1 {
  width: 294px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 49px;
  margin: 0 auto;
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
}

.block_2 {
  width: 1627px;
  height: 275px;
  margin: 70px 0 0 136px;
  display: flex;
  justify-content: space-between;
}

.box_2 {
  width: 941px;
  height: 273px;
  background: url(../assets/images/personalinformation/e974f01faef346da53cab0c398e50bea.png)
    0px -1px no-repeat;
  background-size: 942px 274px;
  position: relative;
}

.block_3 {
  background-color: rgba(239, 239, 239, 1);
  border-radius: 50%;
  width: 155px;
  height: 155px;
  border: 1px solid rgba(203, 203, 203, 1);
  margin: 20px 0 0 0px;
  position: absolute;
  top: 30px; /* 调整垂直位置 */
  left: 30px;
}

.image_5{
  border-radius: 50%;
}
.block_4 {
  position: absolute;
  width: 321px;
  height: 156px;
  margin:43px 0 0 100px;
  top: 0px; /* 调整垂直位置 */
  left: 120px;
}

.box_4 {
    position: relative;
  border-radius: 20px;
  width: 80px;
  height: 49px;
  margin-left: 1px;
  top: 0px; /* 调整垂直位置 */
  left: 120px;
}

.text_3 {
  width: 200px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin-top: 14px;
  position: absolute;
  top: 0;
  left: 0;
}

.text_4 {
  width: 200px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin-top: 14px;
  position: absolute;
  top: 0;
  left: 200px;
}

.text_6 {
  width: 200px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  position: absolute;
  top: 14px;
  left: 400px;
}

.text-wrapper_1 {
  width: 317px;
  height: 25px;
  margin-top: 21px;
}

.text_5 {
  width: 200px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_7 {
  width: 200px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin-top: 70px;
  position: absolute;
  top: 0;
  left: 200px;
}

.text-wrapper_2 {
  width: 257px;
  height: 25px;
  margin-top: 36px;
}

.text_8 {
  width: 300px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.block_5 {
  width: 368px;
  height: 220px;
  margin: 37px 27px 0 20px;
}

.section_1 {
  height: 69px;
  background: url(../assets/images/personalinformation/8868f2acf37f3079fab139b3ff5da01f.png) -10px
    0px no-repeat;
  background-size: 252px 92px;
  width: 232px;
  margin: 20px 0 0 650px;
  position: relative;
  top: 150px;
  left: 0px;
  display: flex; /* 添加弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  /* 移除 margin 设置 */
  display: flex; /* 添加弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.text_14 {
  width: 175px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 20px;
  margin-left: 5px;
  letter-spacing: 10px;
  cursor: pointer; /* 添加指针样式 */
}

.box_5 {
  height: 275px;
  background: url(../assets/images/personalinformation/e19c135acf78908ffc6870560b6ac03d.png)
      100% no-repeat;
    background-size: 100% 100%;
  width: 636px;
  align-self: flex-start;
}

.text-wrapper_5 {
  position: relative;
  width: 468px;
  height: 26px;
  margin: -15px 0 0 68px;
  top: 0px; /* 调整垂直位置 */
  left: 0px;
}

.text_15 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 25px;
  margin-left: 76px;
  position: absolute;
  left: -50px;
  top: 50px;
}

.text_16 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 25px;
  margin-left: 76px;
  position: absolute;
  left: 65px;
  top: 50px;
}

.text_17 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 25px;
  margin-left: 76px;
  position: absolute;
  left: 200px;
  top: 50px;
}

.text_18 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 25px;
  margin: 2px 0 0 19px;
  position: absolute;
  left: 325px;
  top: 50px;
}

.text-wrapper_6 {
  width: 468px;
  height: 25px;
  margin: 15px 0 0 68px;
}

.text_19 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 76px;
  position: absolute;
  right:635px;
}

.text_20 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 19px;
  position: absolute;
  right: 501px;
}

.text_21 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 76px;
  position: absolute;
  right:385px;
}

.text_22 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 19px;
  position: absolute;
  right: 280px;
}

.text-wrapper_7 {
  width: 468px;
  height: 29px;
  margin: 35px 0 0 68px;
}

.text_23 {
  width: 120px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 4px;
  margin-left: 24px;
}

.text_24 {
  width: 83px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 7px 0 0 23px;
}

.text_25 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
    font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 60px;
  position: absolute;
  right:383px;
}

.text_26 {
  width: 100px;
    height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 18px;
  position: absolute;
  right: 280px;
}

.group_2 {
  width: 232px;
  height: 46px;
  margin: 80px 0 20px 377px;
}

.text-wrapper_8 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  height: 50px;
  border: 2px solid rgba(245, 245, 245, 0.1);
  width: 236px;
  margin: 12px 0 0 -2px;
  display: flex; /* 添加弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.text_27 {
  width: 195px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center; /* 文字居中 */
  white-space: nowrap;
  line-height: 16px;
  /* 移除 margin 设置，因为父元素已经居中 */
}

.section_3 {
  width: 1689px;
  height: 574px;
  background: url(../assets/images/personalinformation/89849cff18b90a27d9b139bda364e8ce.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 30px 0 43px 109px;
  position: relative;
}

.table-header {
  width: 1500px;
  height: 30px;
  margin: 47px 0 0 168px;
  display: flex;
  align-items: center;
}

.header-item {
  font-size: 33px;
  font-family: MicrosoftYaHei;
  text-align: center;
  white-space: nowrap;
  margin-top: 80px;
  color: rgba(0, 0, 0, 1);
}

.header-item:nth-child(1) { width: 143px; }
.header-item:nth-child(2) { width: 66px; margin-left: 122px; }
.header-item:nth-child(3) { width: 63px; margin-left: 118px; }
.header-item:nth-child(4) { width: 143px; margin-left: 93px; }
.header-item:nth-child(5) { width: 143px; margin-left: 89px; }
.header-item:nth-child(6) { width: 179px; margin-left: 66px; }
.header-item:nth-child(7) { width: 142px; margin-left: 77px; }

.header-divider {
  width: 1476px;
  height: 1px;
  margin: 60px 0 0 113px;
}

.patient-row-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.patient-row {
  width: 1500px;
  height: 70px;
  margin: 0 0 0 168px;
  display: flex;
  align-items: center;
}

.row-item {
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 1);
}

.row-item-id {
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 1);
  margin-left: 120px;
}
.row-item-time {
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 1);
  margin-left: 170px;
}

.row-item:nth-child(1) { width: 143px; }
.row-item:nth-child(2) { width: 66px; margin-left: 122px; }
.row-item:nth-child(3) { width: 63px; margin-left: 118px; }
.row-item:nth-child(4) { width: 143px; margin-left: 93px; }
.row-item:nth-child(6) { width: 179px; margin-left: 66px; }
.row-item:nth-child(7) { width: 142px; margin-left: 77px; }

/* 有效声强特殊单元格样式 */
.intensity-cell {
  width: 187.8px;
  height: 41.4px;
  margin-left: 73px;
  background: url(../assets/images/personalinformation/89849cff18b90a27d9b139bda364e8ce.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  align-items: center;
}

.intensity-value {
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  color: rgba(0, 0, 0, 1);
  margin-left: 20px;
  margin-top: -10px;
  z-index: 2;
}

.intensity-unit-container {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  width: 117.3px;
  height: 41.4px;
}

.unit-background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.unit-label {
  width: 80%;
  height: 60%;
  position: absolute;
  top: 20%;
  left: 10%;
  z-index: 2;
}

.row-divider {
  width: 1476px;
  height: 1px;
  margin: 10px 0 10px 113px;
}

/* 暂无数据样式 */
.no-data {
  font-size: 28px;
  font-family: MicrosoftYaHei;
  color: rgba(150, 150, 150, 1);
  text-align: center;
  padding: 60px 0;
  margin: 40px 0;
}

/* 分页控件样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0 20px 0;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 30px;
}

.pagination-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 1);
  border: 2px solid rgba(200, 200, 200, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover {
  background-color: rgba(240, 240, 240, 1);
  border-color: rgba(100, 100, 100, 1);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn.disabled:hover {
  background-color: rgba(255, 255, 255, 1);
  border-color: rgba(200, 200, 200, 1);
}

.pagination-btn img {
  width: 30px;
  height: 30px;
}

.page-info {
  font-size: 24px;
  font-family: MicrosoftYaHei;
  color: rgba(78, 78, 78, 1);
  white-space: nowrap;
}

/* 诊断详情弹窗样式 - 使用设计稿样式 */
.diagnosis-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.diagnosis-box {
  height: 1080px;
  background: url(../assets/images/personalinformation/8011ab97892b73a8ac95abaff0b3a6f1.png) -3px -2px no-repeat;
  background-size: 1952px 1094px;
  width: 1920px;
  position: relative;
}

.diagnosis-content-box {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 26px;
  width: 1219px;
  height: 893px;
  border: 4px solid rgba(62, 160, 149, 1);
  margin: 108px 0 0 362px;
  position: relative;
}

.diagnosis-header {
  width: 656px;
  height: 73px;
  margin: 41px 0 0 481px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.diagnosis-title {
  width: 294px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 16px;
}

.diagnosis-close {
  width: 72px;
  height: 73px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.diagnosis-close:hover {
  transform: scale(1.1);
}

.diagnosis-body {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 26px;
  width: 925px;
  height: 508px;
  border: 4px solid rgba(184, 254, 246, 1);
  margin: 67px 0 0 148px;
  position: relative;
}

.diagnosis-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  font-size: 28px;
  color: rgba(89, 89, 89, 1);
  font-family: MicrosoftYaHei;
  line-height: 1.6;
  padding: 30px;
  box-sizing: border-box;
  background-color: transparent;
  border-radius: 22px;
}

.diagnosis-textarea::placeholder {
  color: rgba(150, 150, 150, 1);
  font-size: 28px;
  font-family: MicrosoftYaHei;
}

.diagnosis-footer {
  height: 69px;
  background: url(../assets/images/personalinformation/ab196a073e26a39b689f8b4623da4884.png) -10px 0px no-repeat;
  background-size: 253px 92px;
  width: 232px;
  margin: 55px 0 80px 490px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.diagnosis-confirm {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 3px 0 0 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.diagnosis-confirm:hover {
  background-color: rgba(240, 240, 240, 1);
  transform: scale(1.05);
}

.diagnosis-confirm:active {
  transform: scale(0.95);
}

.diagnosis-confirm-text {
  width: 153px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin: 16px 0 0 0px;
}

/* 移除简单弹窗样式 */
.simple-dialog-overlay,
.simple-dialog-content,
.simple-dialog-header,
.simple-dialog-body,
.simple-dialog-footer,
.diagnosis-input,
.cancel-btn,
.confirm-btn,
.close-btn {
  display: none !important;
}
</style>








