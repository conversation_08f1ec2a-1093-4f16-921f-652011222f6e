package com.Bone.BoneSys.service;

import com.Bone.BoneSys.entity.User;
import com.Bone.BoneSys.repository.UserRepository;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Date;
import java.util.Optional;

@Service
public class AuthService {
    private final UserRepository userRepository;

    @Value("${jwt.secret:BoneSysSecretKeyForJWTTokenGeneration2025}")
    private String jwtSecret;

    @Value("${jwt.expiration:86400000}")
    private long jwtExpiration;

    public AuthService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * 登录校验，使用明文密码比较
     * @param password 用户输入的密码
     * @return LoginResult（包含token和匹配类型）
     * @throws Exception 登录失败抛出异常
     */
    public LoginResult login(String password) throws Exception {
        Optional<User> userOpt = userRepository.findTheOnlyUser();
        if (userOpt.isEmpty()) {
            throw new Exception("用户不存在");
        }
        User user = userOpt.get();
        String userPassword = user.getPassword();
        if (userPassword == null || userPassword.isEmpty() || !userPassword.equals(password)) {
            throw new Exception("密码错误");
        }
        // 生成JWT
        SecretKey key = Keys.hmacShaKeyFor(Base64.getDecoder().decode(jwtSecret));
        String token = Jwts.builder()
                .setSubject(user.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration))
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
        // 更新登录时间
        user.setLastUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
        return new LoginResult(token, "user");
    }

    /**
     * 修改用户密码：明文密码修改
     */
    public void changeUserPassword(String oldUserPassword, String newUserPassword) throws Exception {
        Optional<User> userOpt = userRepository.findTheOnlyUser();
        if (userOpt.isEmpty()) {
            throw new Exception("用户不存在");
        }
        User user = userOpt.get();
        String currentPassword = user.getPassword();
        if (currentPassword == null || currentPassword.isEmpty() || !currentPassword.equals(oldUserPassword)) {
            throw new Exception("原密码错误");
        }
        user.setPassword(newUserPassword);
        user.setLastUpdatedAt(java.time.LocalDateTime.now());
        userRepository.save(user);
    }

    public static class LoginResult {
        private final String token;
        private final String matchedType; // "factory" 或 "user"
        public LoginResult(String token, String matchedType) {
            this.token = token;
            this.matchedType = matchedType;
        }
        public String getToken() { return token; }
        public String getMatchedType() { return matchedType; }
    }
}