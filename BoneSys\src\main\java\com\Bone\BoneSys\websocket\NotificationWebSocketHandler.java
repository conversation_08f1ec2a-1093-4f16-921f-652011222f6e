package com.Bone.BoneSys.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * WebSocket通知处理器
 * 对应UI页面：各种实时弹窗通知
 */
@Component
public class NotificationWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(NotificationWebSocketHandler.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储所有活跃的WebSocket连接
    private final List<WebSocketSession> sessions = new CopyOnWriteArrayList<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("WebSocket connection established: {}", session.getId());
        sessions.add(session);
        
        // 发送连接成功消息
        sendNotification(session, new NotificationMessage(
            "CONNECTION_ESTABLISHED",
            "WebSocket连接已建立",
            null
        ));
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        logger.info("Received WebSocket message from {}: {}", session.getId(), message.getPayload());
        
        // 处理客户端发送的消息（如心跳包）
        if ("ping".equals(message.getPayload())) {
            session.sendMessage(new TextMessage("pong"));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket transport error for session {}: {}", session.getId(), exception.getMessage());
        sessions.remove(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        logger.info("WebSocket connection closed: {} with status: {}", session.getId(), closeStatus);
        sessions.remove(session);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 发送治疗完成通知
     * 对应UI页面：新版-主界面-治疗完成弹窗.png, 新版-治疗进程-治疗完成弹窗.png
     */
    public void sendTreatmentCompletedNotification(String patientName) {
        TreatmentCompletedData data = new TreatmentCompletedData();
        data.setPatientName(patientName);
        
        NotificationMessage notification = new NotificationMessage(
            "TREATMENT_COMPLETED",
            data,
            getCurrentTimestamp()
        );
        
        broadcastNotification(notification);
        logger.info("Sent treatment completed notification for patient: {}", patientName);
    }

    /**
     * 发送取回提醒通知
     * 对应UI页面：新版-主界面-取回提醒弹窗.png
     */
    public void sendPickupReminderNotification(List<Integer> headNumbers) {
        PickupReminderData data = new PickupReminderData();
        data.setHeadNumbers(headNumbers);
        
        NotificationMessage notification = new NotificationMessage(
            "PICKUP_REMINDER",
            data,
            getCurrentTimestamp()
        );
        
        broadcastNotification(notification);
        logger.info("Sent pickup reminder notification for heads: {}", headNumbers);
    }

    /**
     * 发送治疗头异常通知
     * 对应UI页面：新版-治疗进程-治疗头异常结束弹窗.png
     */
    public void sendHardwareErrorNotification(String errorCode, String message, List<Integer> headNumbers) {
        HardwareErrorData data = new HardwareErrorData();
        data.setErrorCode(errorCode);
        data.setMessage(message);
        data.setHeadNumbers(headNumbers);
        
        NotificationMessage notification = new NotificationMessage(
            "HARDWARE_ERROR",
            data,
            getCurrentTimestamp()
        );
        
        broadcastNotification(notification);
        logger.info("Sent hardware error notification: {} for heads: {}", message, headNumbers);
    }

    /**
     * 发送治疗头不足通知
     * 对应UI页面：新版-参数设置-治疗头不足弹窗.png
     */
    public void sendInsufficientHeadsNotification(int requiredCount, int availableCount) {
        InsufficientHeadsData data = new InsufficientHeadsData();
        data.setErrorCode("1002");
        data.setMessage("可用治疗头数量不足");
        data.setRequiredCount(requiredCount);
        data.setAvailableCount(availableCount);
        
        NotificationMessage notification = new NotificationMessage(
            "INSUFFICIENT_HEADS",
            data,
            getCurrentTimestamp()
        );
        
        broadcastNotification(notification);
        logger.info("Sent insufficient heads notification: required={}, available={}", requiredCount, availableCount);
    }

    /**
     * 广播通知给所有连接的客户端
     */
    private void broadcastNotification(NotificationMessage notification) {
        String message;
        try {
            message = objectMapper.writeValueAsString(notification);
        } catch (Exception e) {
            logger.error("Error serializing notification", e);
            return;
        }

        List<WebSocketSession> closedSessions = new CopyOnWriteArrayList<>();
        
        for (WebSocketSession session : sessions) {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(message));
                } else {
                    closedSessions.add(session);
                }
            } catch (IOException e) {
                logger.error("Error sending notification to session {}: {}", session.getId(), e.getMessage());
                closedSessions.add(session);
            }
        }
        
        // 清理已关闭的连接
        sessions.removeAll(closedSessions);
    }

    /**
     * 发送通知给特定会话
     */
    private void sendNotification(WebSocketSession session, NotificationMessage notification) {
        try {
            String message = objectMapper.writeValueAsString(notification);
            session.sendMessage(new TextMessage(message));
        } catch (Exception e) {
            logger.error("Error sending notification to session {}: {}", session.getId(), e.getMessage());
        }
    }

    /**
     * 获取当前时间戳
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
    }

    // 通知消息基类
    @Data
    public static class NotificationMessage {
        private String type;
        private Object data;
        private String timestamp;

        public NotificationMessage(String type, Object data, String timestamp) {
            this.type = type;
            this.data = data;
            this.timestamp = timestamp;
        }
    }

    // 治疗完成通知数据
    @Data
    public static class TreatmentCompletedData {
        private String patientName;
    }

    // 取回提醒通知数据
    @Data
    public static class PickupReminderData {
        private List<Integer> headNumbers;
    }

    // 硬件错误通知数据
    @Data
    public static class HardwareErrorData {
        private String errorCode;
        private String message;
        private List<Integer> headNumbers;
    }

    // 治疗头不足通知数据
    @Data
    public static class InsufficientHeadsData {
        private String errorCode;
        private String message;
        private int requiredCount;
        private int availableCount;
    }
}
