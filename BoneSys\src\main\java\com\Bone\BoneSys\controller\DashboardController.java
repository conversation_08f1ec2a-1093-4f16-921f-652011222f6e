package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import com.Bone.BoneSys.repository.*;
import com.Bone.BoneSys.service.HardwareService;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 主界面数据控制器
 * 提供主界面所需的系统状态信息和快捷操作数据
 */
@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = "*")
public class DashboardController {

    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);

    @Autowired private UserRepository userRepository;
    @Autowired private PatientRepository patientRepository;
    @Autowired private RecordRepository recordRepository;
    @Autowired private TreatmentHeadRepository treatmentHeadRepository;
    @Autowired private ProcessRepository processRepository;
    @Autowired private TreatmentDetailRepository treatmentDetailRepository;
    @Autowired private BodyPartStatRepository bodyPartStatRepository;
    @Autowired private HardwareService hardwareService;

    /**
     * 获取主界面数据
     * GET /api/dashboard/main
     */
    @GetMapping("/main")
    public ApiResponse<MainDashboardResponse> getMainDashboard() {
        try {
            logger.info("Fetching main dashboard data");

            MainDashboardResponse response = new MainDashboardResponse();
            
            // 1. 治疗头状态统计
            TreatmentHeadStatusInfo headStatus = buildTreatmentHeadStatus();
            response.setAvailableHeads(headStatus.getAvailableCount().intValue());
            response.setTotalHeads(20); // 固定总数为20
            
            // 2. 系统基本信息
            response.setSystemInfo(buildSystemInfo());
            
            // 3. 数据统计概览
            response.setDataOverview(buildDataOverview());

            logger.info("Successfully built main dashboard data: availableHeads={}, totalHeads={}", 
                       response.getAvailableHeads(), response.getTotalHeads());
            return ApiResponse.success("主界面数据获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching main dashboard data", e);
            return ApiResponse.error(500, "获取主界面数据失败: " + e.getMessage());
        }
    }

    /**
     * 构建系统基本信息
     */
    private SystemInfo buildSystemInfo() {
        SystemInfo info = new SystemInfo();
        info.setCurrentTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        info.setSystemName("FREEBONE医疗系统");
        info.setVersion("1.0.0");
        info.setUptime(calculateUptime());
        return info;
    }

    /**
     * 构建数据统计概览
     */
    private DataOverview buildDataOverview() {
        DataOverview overview = new DataOverview();
        overview.setTotalPatients(patientRepository.count());
        overview.setTotalRecords(recordRepository.count());
        overview.setTotalProcesses(processRepository.count());
        
        // 今日新增进程数量
        overview.setTodayProcesses(processRepository.countTodayProcesses());
        
        logger.debug("Data overview: patients={}, records={}, processes={}, todayProcesses={}", 
                    overview.getTotalPatients(), overview.getTotalRecords(), 
                    overview.getTotalProcesses(), overview.getTodayProcesses());
        
        return overview;
    }

    /**
     * 构建治疗头状态统计
     */
    private TreatmentHeadStatusInfo buildTreatmentHeadStatus() {
        TreatmentHeadStatusInfo status = new TreatmentHeadStatusInfo();
        
        // 按状态统计
        List<Object[]> statusCounts = treatmentHeadRepository.countByStatus();
        Map<String, Long> statusMap = new HashMap<>();
        for (Object[] row : statusCounts) {
            TreatmentHeadStatus headStatus = (TreatmentHeadStatus) row[0];
            Long count = (Long) row[1];
            statusMap.put(headStatus.name(), count);
        }
        
        status.setChargingCount(statusMap.getOrDefault("CHARGING", 0L));
        status.setChargedCount(statusMap.getOrDefault("CHARGED", 0L));
        status.setTreatingCount(statusMap.getOrDefault("TREATING", 0L));
        
        // 可用治疗头数量（电量>60%且状态为CHARGED）
        status.setAvailableCount((long) treatmentHeadRepository.findAvailableHeads(60).size());

        // 低电量治疗头数量
        status.setLowBatteryCount((long) treatmentHeadRepository.findLowBatteryHeads(20).size());

        // 需要更换的治疗头数量
        status.setNeedReplacementCount((long) treatmentHeadRepository.findHeadsNeedingReplacement().size());
        
        return status;
    }

    /**
     * 构建治疗进程状态统计
     */
    private ProcessStatusInfo buildProcessStatus() {
        ProcessStatusInfo status = new ProcessStatusInfo();
        
        // 按状态统计
        List<Object[]> statusCounts = processRepository.countByStatus();
        Map<String, Long> statusMap = new HashMap<>();
        for (Object[] row : statusCounts) {
            ProcessStatus processStatus = (ProcessStatus) row[0];
            Long count = (Long) row[1];
            statusMap.put(processStatus.name(), count);
        }
        
        status.setInProgressCount(statusMap.getOrDefault("IN_PROGRESS", 0L));
        status.setCompletedCount(statusMap.getOrDefault("COMPLETED", 0L));
        status.setCancelledCount(statusMap.getOrDefault("CANCELLED", 0L));
        
        // 今日新增进程数量
        status.setTodayNewCount(processRepository.countTodayProcesses());
        
        return status;
    }

    /**
     * 构建硬件连接状态
     */
    private HardwareStatusInfo buildHardwareStatus() {
        HardwareStatusInfo status = new HardwareStatusInfo();
        
        try {
            boolean connected = hardwareService.isHardwareConnected();
            status.setConnected(connected);
            status.setStatus(connected ? "已连接" : "未连接");
            status.setLastSyncTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        } catch (Exception e) {
            logger.warn("Failed to check hardware status: {}", e.getMessage());
            status.setConnected(false);
            status.setStatus("连接异常");
            status.setLastSyncTime("--:--:--");
        }
        
        return status;
    }

    /**
     * 构建最近活动
     */
    private List<RecentActivity> buildRecentActivities() {
        List<RecentActivity> activities = new ArrayList<>();
        
        // 最近的治疗进程（最多5个）
        var recentProcesses = processRepository.findTop5ByOrderByStartTimeDesc();
        for (var process : recentProcesses) {
            RecentActivity activity = new RecentActivity();
            activity.setType("PROCESS");
            activity.setDescription("治疗进程 #" + process.getId() + " " + getProcessStatusDescription(process.getStatus()));
            activity.setTime(process.getStartTime().format(DateTimeFormatter.ofPattern("MM-dd HH:mm")));
            activity.setStatus(process.getStatus().name());
            activities.add(activity);
        }
        
        return activities;
    }

    /**
     * 构建系统警告和提醒
     */
    private List<SystemAlert> buildSystemAlerts() {
        List<SystemAlert> alerts = new ArrayList<>();
        
        // 检查低电量治疗头
        long lowBatteryCount = treatmentHeadRepository.findLowBatteryHeads(20).size();
        if (lowBatteryCount > 0) {
            SystemAlert alert = new SystemAlert();
            alert.setType("WARNING");
            alert.setTitle("治疗头电量不足");
            alert.setMessage("有 " + lowBatteryCount + " 个治疗头电量低于20%，请及时充电");
            alert.setTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
            alerts.add(alert);
        }
        
        // 检查需要更换的治疗头
        long needReplacementCount = treatmentHeadRepository.findHeadsNeedingReplacement().size();
        if (needReplacementCount > 0) {
            SystemAlert alert = new SystemAlert();
            alert.setType("INFO");
            alert.setTitle("治疗头需要更换");
            alert.setMessage("有 " + needReplacementCount + " 个治疗头使用次数接近上限，建议更换");
            alert.setTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
            alerts.add(alert);
        }
        
        // 检查超时进程
        var timeoutProcesses = processRepository.findTimeoutProcesses(LocalDateTime.now().minusHours(24));
        if (!timeoutProcesses.isEmpty()) {
            SystemAlert alert = new SystemAlert();
            alert.setType("ERROR");
            alert.setTitle("治疗进程超时");
            alert.setMessage("有 " + timeoutProcesses.size() + " 个治疗进程运行超过24小时，请检查");
            alert.setTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
            alerts.add(alert);
        }
        
        return alerts;
    }

    /**
     * 计算系统运行时间
     */
    private String calculateUptime() {
        // 简单实现，实际应该记录系统启动时间
        return "运行中";
    }

    /**
     * 获取进程状态描述
     */
    private String getProcessStatusDescription(ProcessStatus status) {
        switch (status) {
            case IN_PROGRESS: return "进行中";
            case COMPLETED: return "已完成";
            case CANCELLED: return "已取消";
            default: return status.name();
        }
    }

    // DTO类定义
    @Data
    public static class DashboardResponse {
        private SystemInfo systemInfo;
        private DataOverview dataOverview;
        private TreatmentHeadStatusInfo treatmentHeadStatus;
        private ProcessStatusInfo processStatus;
        private HardwareStatusInfo hardwareStatus;
        private List<RecentActivity> recentActivities;
        private List<SystemAlert> systemAlerts;
    }

    @Data
    public static class SystemInfo {
        private String currentTime;
        private String systemName;
        private String version;
        private String uptime;
    }

    @Data
    public static class DataOverview {
        private Long totalPatients;
        private Long totalRecords;
        private Long totalProcesses;
        private Long todayProcesses;
    }

    @Data
    public static class TreatmentHeadStatusInfo {
        private Long chargingCount;
        private Long chargedCount;
        private Long treatingCount;
        private Long availableCount;
        private Long lowBatteryCount;
        private Long needReplacementCount;
    }

    @Data
    public static class ProcessStatusInfo {
        private Long inProgressCount;
        private Long completedCount;
        private Long cancelledCount;
        private Long todayNewCount;
    }

    @Data
    public static class HardwareStatusInfo {
        private Boolean connected;
        private String status;
        private String lastSyncTime;
    }

    @Data
    public static class RecentActivity {
        private String type;
        private String description;
        private String time;
        private String status;
    }

    @Data
    public static class SystemAlert {
        private String type;
        private String title;
        private String message;
        private String time;
    }

    @Data
    public static class MainDashboardResponse {
        private SystemInfo systemInfo;
        private DataOverview dataOverview;
        private int availableHeads;
        private int totalHeads;
    }
}
