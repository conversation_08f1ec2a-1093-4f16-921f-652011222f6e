import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 导入TDesign组件库
import TDesign from 'tdesign-vue-next'
// 导入TDesign样式
import 'tdesign-vue-next/es/style/index.css'

// 导入axios配置
import './utils/axios'

// 清除之前保存的登录状态，确保每次启动都需要重新登录
// 仅在开发阶段使用，实际部署时可以根据需要移除
localStorage.removeItem('isAuthenticated')

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(TDesign)

app.mount('#app')
