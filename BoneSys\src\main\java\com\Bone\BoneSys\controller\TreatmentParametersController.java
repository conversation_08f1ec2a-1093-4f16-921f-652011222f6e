package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.service.TreatmentHeadRecommendationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 治疗参数设置控制器
 * 专门用于参数设置页面的API接口
 */
@RestController
@RequestMapping("/api/treatment-parameters")
@CrossOrigin(origins = "*")
public class TreatmentParametersController {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentParametersController.class);
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private TreatmentHeadRecommendationService recommendationService;
    
    /**
     * 检查治疗头数量是否充足
     * POST /api/treatment-parameters/check-availability
     * 
     * 接收前端发送的治疗参数，检查所需的治疗头数量是否充足
     */
    @PostMapping("/check-availability")
    public ApiResponse<TreatmentParametersAvailabilityResponse> checkTreatmentHeadAvailability(
            @RequestBody TreatmentParametersCheckRequest request) {
        try {
            logger.info("API request: check treatment head availability for {} body parts, mode: {}", 
                       request.getBodyParts().size(), request.getTreatmentMode());
            
            // 计算所需的浅层和深层治疗头数量
            int shallowNeeded = 0;
            int deepNeeded = 0;
            
            for (TreatmentBodyPart bodyPart : request.getBodyParts()) {
                int count = bodyPart.getParameters().getCount();
                if ("浅部".equals(bodyPart.getParameters().getDepth())) {
                    shallowNeeded += count;
                } else if ("深部".equals(bodyPart.getParameters().getDepth())) {
                    deepNeeded += count;
                }
            }
            
            // 获取当前可用的治疗头数量
            List<TreatmentHeadInfo> allHeads = hardwareService.syncAllTreatmentHeads();
            
            // 分别统计浅层和深层可用数量
            long shallowAvailable = allHeads.stream()
                .filter(head -> "SHALLOW".equals(head.getCompartmentType()) 
                            && head.getBatteryLevel() >= 20) // 至少20%电量
                .count();
                
            long deepAvailable = allHeads.stream()
                .filter(head -> "DEEP".equals(head.getCompartmentType()) 
                            && head.getBatteryLevel() >= 20) // 至少20%电量
                .count();
            
            boolean shallowSufficient = shallowNeeded <= shallowAvailable;
            boolean deepSufficient = deepNeeded <= deepAvailable;
            boolean sufficient = shallowSufficient && deepSufficient;
            
            TreatmentParametersAvailabilityResponse response = new TreatmentParametersAvailabilityResponse();
            response.setSufficient(sufficient);
            response.setTotalNeeded(shallowNeeded + deepNeeded);
            response.setTotalAvailable((int)(shallowAvailable + deepAvailable));
            response.setShallowNeeded(shallowNeeded);
            response.setShallowAvailable((int)shallowAvailable);
            response.setShallowSufficient(shallowSufficient);
            response.setDeepNeeded(deepNeeded);
            response.setDeepAvailable((int)deepAvailable);
            response.setDeepSufficient(deepSufficient);
            
            logger.info("Treatment head availability check result: sufficient={}, shallow({}/{}), deep({}/{})",
                       sufficient, shallowNeeded, shallowAvailable, deepNeeded, deepAvailable);
            
            return ApiResponse.success("治疗头数量检查完成", response);
            
        } catch (Exception e) {
            logger.error("Failed to check treatment head availability", e);
            return ApiResponse.error(500, "检查治疗头数量失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成推荐治疗头配置
     * POST /api/treatment-parameters/generate-recommendations
     * 
     * 根据治疗参数生成推荐的治疗头配置
     */
    @PostMapping("/generate-recommendations")
    public ApiResponse<List<TreatmentHeadRecommendation>> generateRecommendations(
            @RequestBody TreatmentParametersCheckRequest request) {
        try {
            logger.info("API request: generate treatment head recommendations for {} body parts", 
                       request.getBodyParts().size());
            
            // 生成推荐配置
            List<TreatmentHeadRecommendation> recommendations = 
                recommendationService.generateRecommendations(request);
            
            logger.info("Generated {} treatment head recommendations", recommendations.size());
            
            return ApiResponse.success("推荐治疗头配置生成成功", recommendations);
            
        } catch (Exception e) {
            logger.error("Failed to generate treatment head recommendations", e);
            return ApiResponse.error(500, "生成推荐治疗头配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载治疗参数（取走治疗模式）
     * POST /api/treatment-parameters/download
     * 
     * 模拟参数下载过程，为取走治疗模式准备参数
     */
    @PostMapping("/download")
    public ApiResponse<Map<String, Object>> downloadTreatmentParameters(
            @RequestBody TreatmentParametersRequest request) {
        try {
            logger.info("API request: download treatment parameters for takeaway mode, patient: {}", 
                       request.getPatientId());
            
            // 验证参数
            if (request.getBodyParts() == null || request.getBodyParts().isEmpty()) {
                return ApiResponse.error(400, "请至少选择一个治疗部位");
            }
            
            // 模拟参数下载过程（3秒延迟）
            Thread.sleep(3000);
            
            // 生成推荐治疗头配置
            TreatmentParametersCheckRequest checkRequest = new TreatmentParametersCheckRequest();
            checkRequest.setPatientId(request.getPatientId());
            checkRequest.setTreatmentMode("takeaway");
            checkRequest.setBodyParts(request.getBodyParts());
            
            List<TreatmentHeadRecommendation> recommendations = 
                recommendationService.generateRecommendations(checkRequest);
            
            // 返回下载结果和推荐配置
            Map<String, Object> result = Map.of(
                "downloadComplete", true,
                "recommendations", recommendations,
                "downloadTime", System.currentTimeMillis(),
                "message", "治疗参数下载完成，请选择推荐的治疗头"
            );
            
            logger.info("Treatment parameters download completed for patient: {}", request.getPatientId());
            
            return ApiResponse.success("治疗参数下载完成", result);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Treatment parameters download was interrupted");
            return ApiResponse.error(500, "参数下载被中断");
        } catch (Exception e) {
            logger.error("Failed to download treatment parameters", e);
            return ApiResponse.error(500, "下载治疗参数失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动本地治疗
     * POST /api/treatment-parameters/start-local
     * 
     * 启动本地治疗模式，检查治疗头数量并准备治疗
     */
    @PostMapping("/start-local")
    public ApiResponse<Map<String, Object>> startLocalTreatment(
            @RequestBody TreatmentParametersRequest request) {
        try {
            logger.info("API request: start local treatment for patient: {}", request.getPatientId());
            
            // 验证参数
            if (request.getBodyParts() == null || request.getBodyParts().isEmpty()) {
                return ApiResponse.error(400, "请至少选择一个治疗部位");
            }
            
            // 检查治疗头数量
            TreatmentParametersCheckRequest checkRequest = new TreatmentParametersCheckRequest();
            checkRequest.setPatientId(request.getPatientId());
            checkRequest.setTreatmentMode("local");
            checkRequest.setBodyParts(request.getBodyParts());
            
            ApiResponse<TreatmentParametersAvailabilityResponse> availabilityResponse = 
                checkTreatmentHeadAvailability(checkRequest);
                
            if (!availabilityResponse.getCode().equals(200)) {
                return ApiResponse.error(500, "检查治疗头数量失败");
            }
            
            TreatmentParametersAvailabilityResponse availability = availabilityResponse.getData();
            if (!availability.isSufficient()) {
                // 治疗头数量不足
                Map<String, Object> result = Map.of(
                    "sufficient", false,
                    "availability", availability,
                    "message", "治疗头数量不足，无法启动治疗"
                );
                return ApiResponse.success("治疗头数量检查完成", result);
            }
            
            // 模拟参数下载过程
            Thread.sleep(3000);
            
            // 生成推荐治疗头配置
            List<TreatmentHeadRecommendation> recommendations = 
                recommendationService.generateRecommendations(checkRequest);
            
            // 返回成功结果
            Map<String, Object> result = Map.of(
                "sufficient", true,
                "downloadComplete", true,
                "recommendations", recommendations,
                "treatmentMode", "local",
                "message", "本地治疗准备完成，请选择推荐的治疗头"
            );
            
            logger.info("Local treatment preparation completed for patient: {}", request.getPatientId());
            
            return ApiResponse.success("本地治疗准备完成", result);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Local treatment preparation was interrupted");
            return ApiResponse.error(500, "治疗准备被中断");
        } catch (Exception e) {
            logger.error("Failed to start local treatment", e);
            return ApiResponse.error(500, "启动本地治疗失败: " + e.getMessage());
        }
    }
    
    /**
     * 确认治疗头选择并发送参数
     * POST /api/treatment-parameters/confirm-selection
     * 
     * 确认选择的治疗头并发送治疗参数
     */
    @PostMapping("/confirm-selection")
    public ApiResponse<Map<String, Object>> confirmTreatmentHeadSelection(
            @RequestBody TreatmentConfirmationRequest request) {
        try {
            logger.info("API request: confirm treatment head selection, mode: {}, heads: {}", 
                       request.getTreatmentMode(), request.getRecommendedHeads().size());
            
            // 发送治疗参数到推荐的治疗头
            boolean success = recommendationService.sendTreatmentParametersToRecommended(
                request.getRecommendedHeads(),
                request.getTreatmentParams(),
                request.getTreatmentMode()
            );
            
            if (success) {
                String message = "local".equals(request.getTreatmentMode()) ? 
                    "治疗参数发送成功，治疗已开始" : "治疗参数下载成功，请取走治疗头";
                    
                Map<String, Object> result = Map.of(
                    "success", true,
                    "treatmentMode", request.getTreatmentMode(),
                    "nextPage", "local".equals(request.getTreatmentMode()) ? 
                        "TreatmentProcessView1" : "TreatmentProcessView2",
                    "message", message
                );
                
                return ApiResponse.success(message, result);
            } else {
                return ApiResponse.error(500, "发送治疗参数失败");
            }
            
        } catch (SerialCommunicationException e) {
            logger.error("Serial communication failed during treatment confirmation", e);
            return ApiResponse.error(500, "硬件通信失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Failed to confirm treatment head selection", e);
            return ApiResponse.error(500, "确认治疗头选择失败: " + e.getMessage());
        }
    }
} 