# 新建档案页面数据显示修复设计文档

## 概述

本设计文档旨在解决新建档案页面显示数据与数据库实际数据不一致的问题。通过重构后端API查询逻辑和前端数据渲染逻辑，确保页面显示的患者基本信息、就诊时间、治疗部位和治疗次数与数据库中的真实数据完全一致。

## 架构

### 当前架构问题分析

1. **数据查询不完整**：当前`RecordController.getCandidates()`方法只查询了`patients`表，没有关联`records`和`body_part_stats`表
2. **模拟数据问题**：API返回了硬编码的模拟数据而不是真实的数据库数据
3. **数据映射错误**：前端显示的字段与数据库字段映射不正确
4. **缺少数据聚合**：没有正确计算治疗次数和部位信息

### 目标架构设计

```mermaid
graph TB
    A[前端新建档案页面] --> B[GET /api/records/candidates]
    B --> C[RecordController.getCandidates()]
    C --> D[多表关联查询服务]
    D --> E[PatientRepository]
    D --> F[RecordRepository] 
    D --> G[BodyPartStatRepository]
    E --> H[patients表]
    F --> I[records表]
    G --> J[body_part_stats表]
    D --> K[数据聚合处理]
    K --> L[CandidateItemVO]
    L --> A
```

## 组件和接口

### 1. 后端API重构

#### 1.1 新增数据传输对象

```java
// 增强的候选患者项目VO
public class EnhancedCandidateItem {
    private Long patientId;
    private String cardId;           // 来自patients.patient_card_id
    private String name;             // 来自patients.name
    private String age;              // 来自patients.age
    private String gender;           // 来自patients.gender
    private String visitTime;        // 来自records.created_at (最新档案的建档日期)
    private String treatmentParts;   // 来自body_part_stats.body_part (逗号分隔)
    private Integer totalSessions;   // 来自body_part_stats.total_usage_count (所有部位总和)
}
```

#### 1.2 多表关联查询服务

```java
@Service
public class PatientDataAggregationService {
    
    // 获取患者完整信息（包含档案和部位统计）
    public List<EnhancedCandidateItem> getPatientCandidatesWithStats(
        String search, Pageable pageable);
    
    // 聚合患者的治疗部位信息
    private String aggregateTreatmentParts(Long patientId);
    
    // 计算患者的总治疗次数
    private Integer calculateTotalSessions(Long patientId);
    
    // 获取患者最新就诊时间
    private String getLatestVisitTime(Long patientId);
}
```

#### 1.3 Repository查询方法增强

```java
// PatientRepository新增方法
@Query("SELECT DISTINCT p FROM Patient p " +
       "LEFT JOIN p.records r " +
       "LEFT JOIN r.bodyPartStats bps " +
       "WHERE (:search IS NULL OR p.name LIKE %:search% OR p.patientCardId LIKE %:search%) " +
       "ORDER BY p.createdAt DESC")
Page<Patient> findPatientsWithRecordsAndStats(@Param("search") String search, Pageable pageable);

// RecordRepository新增方法
@Query("SELECT r FROM Record r WHERE r.patient.id = :patientId ORDER BY r.createdAt DESC")
List<Record> findByPatientIdOrderByCreatedAtDesc(@Param("patientId") Long patientId);

// BodyPartStatRepository新增方法
@Query("SELECT bps.bodyPart, SUM(bps.totalUsageCount) FROM BodyPartStat bps " +
       "JOIN bps.record r WHERE r.patient.id = :patientId " +
       "GROUP BY bps.bodyPart")
List<Object[]> getPatientBodyPartSummary(@Param("patientId") Long patientId);
```

### 2. 前端数据处理优化

#### 2.1 API调用优化

```typescript
// 新的API接口定义
export interface EnhancedCandidateItem {
  patientId: number;
  cardId: string;
  name: string;
  age: string;
  gender: string;
  visitTime: string;      // 格式：YYYY.MM.DD
  treatmentParts: string; // 格式：肩颈部, 腰背部
  totalSessions: number;
}

// API调用函数
export const getEnhancedCandidates = (params: {
  page?: number;
  size?: number;
  search?: string;
}): Promise<{
  candidates: EnhancedCandidateItem[];
  pagination: PaginationInfo;
}> => {
  return http.get('/records/candidates', { params });
};
```

#### 2.2 数据渲染逻辑

```vue
<template>
  <div class="candidate-list">
    <div v-for="candidate in candidates" :key="candidate.patientId" class="candidate-item">
      <span class="card-id">{{ candidate.cardId }}</span>
      <span class="name">{{ candidate.name }}</span>
      <span class="age">{{ candidate.age }}</span>
      <span class="gender">{{ candidate.gender }}</span>
      <span class="visit-time">{{ candidate.visitTime }}</span>
      <span class="treatment-parts">{{ candidate.treatmentParts || '待确定' }}</span>
      <span class="sessions">{{ candidate.totalSessions }}</span>
    </div>
  </div>
</template>
```

## 数据模型

### 数据库表关系

```mermaid
erDiagram
    patients ||--o{ records : "has"
    records ||--o{ body_part_stats : "contains"
    
    patients {
        bigint id PK
        varchar patient_card_id UK
        varchar name
        varchar gender
        varchar age
        varchar contact_info
        timestamp created_at
    }
    
    records {
        bigint id PK
        varchar record_number UK
        bigint patient_id FK
        text diagnosis_description
        int sessions_completed_count
        date created_at
    }
    
    body_part_stats {
        bigint id PK
        bigint record_id FK
        varchar body_part
        int total_usage_count
        int total_duration_minutes
    }
```

### 数据聚合逻辑

1. **患者基本信息**：直接从`patients`表获取
2. **就诊时间**：从`records`表获取该患者最新的`created_at`
3. **治疗部位**：从`body_part_stats`表聚合该患者所有档案的`body_part`，去重后用逗号连接
4. **治疗次数**：从`body_part_stats`表汇总该患者所有档案的`total_usage_count`

## 错误处理

### 1. 数据缺失处理

```java
// 处理患者没有档案记录的情况
if (records.isEmpty()) {
    item.setVisitTime("无档案");
    item.setTreatmentParts("待确定");
    item.setTotalSessions(0);
}

// 处理档案没有部位统计的情况
if (bodyPartStats.isEmpty()) {
    item.setTreatmentParts("待确定");
    item.setTotalSessions(0);
}
```

### 2. 数据格式化处理

```java
// 日期格式化
private String formatVisitTime(LocalDate date) {
    if (date == null) return "无档案";
    return date.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
}

// 部位信息格式化
private String formatTreatmentParts(List<String> parts) {
    if (parts == null || parts.isEmpty()) return "待确定";
    return String.join(", ", parts);
}

// 年龄格式化
private String formatAge(String age) {
    if (age == null || age.trim().isEmpty()) return "未知";
    return age.endsWith("岁") ? age : age + "岁";
}
```

### 3. 前端错误处理

```typescript
// API调用错误处理
const loadCandidates = async () => {
  try {
    loading.value = true;
    const response = await getEnhancedCandidates(searchParams);
    candidates.value = response.candidates;
    pagination.value = response.pagination;
  } catch (error) {
    console.error('获取候选患者列表失败:', error);
    MessagePlugin.error('获取患者数据失败，请重试');
    candidates.value = [];
  } finally {
    loading.value = false;
  }
};

// 数据显示容错处理
const displayValue = (value: string | number | null | undefined, defaultValue: string = '-') => {
  return value !== null && value !== undefined && value !== '' ? value : defaultValue;
};
```

## 测试策略

### 1. 单元测试

```java
@Test
public void testGetPatientCandidatesWithStats() {
    // 测试正常情况：患者有完整的档案和部位统计
    // 测试边界情况：患者没有档案记录
    // 测试边界情况：患者有档案但没有部位统计
    // 测试数据聚合逻辑的正确性
}

@Test
public void testDataFormatting() {
    // 测试日期格式化
    // 测试部位信息聚合
    // 测试治疗次数计算
}
```

### 2. 集成测试

```java
@Test
public void testCandidatesApiIntegration() {
    // 测试API端到端调用
    // 验证返回数据的完整性和准确性
    // 测试分页功能
    // 测试搜索功能
}
```

### 3. 前端测试

```typescript
// 测试数据渲染
describe('CandidateList', () => {
  it('should display correct patient data', () => {
    // 测试正常数据显示
    // 测试空数据处理
    // 测试错误状态显示
  });
});
```

## 性能优化

### 1. 数据库查询优化

```sql
-- 添加复合索引优化查询性能
CREATE INDEX idx_records_patient_created ON records(patient_id, created_at DESC);
CREATE INDEX idx_body_part_stats_record ON body_part_stats(record_id, body_part);
CREATE INDEX idx_patients_search ON patients(name, patient_card_id);
```

### 2. 缓存策略

```java
@Cacheable(value = "patientCandidates", key = "#search + '_' + #pageable.pageNumber")
public List<EnhancedCandidateItem> getPatientCandidatesWithStats(String search, Pageable pageable) {
    // 缓存候选患者列表，减少数据库查询
}
```

### 3. 分页优化

```java
// 使用游标分页替代偏移分页，提高大数据量查询性能
public Page<EnhancedCandidateItem> getCandidatesWithCursor(String cursor, int size) {
    // 实现基于游标的分页查询
}
```

## 部署考虑

### 1. 数据库迁移

```sql
-- 确保现有数据的完整性
UPDATE patients SET name = COALESCE(name, '未知患者') WHERE name IS NULL OR name = '';
UPDATE patients SET age = COALESCE(age, '未知') WHERE age IS NULL OR age = '';
UPDATE patients SET gender = COALESCE(gender, '未知') WHERE gender IS NULL OR gender = '';
```

### 2. 向后兼容性

```java
// 保持原有API接口的兼容性，同时提供新的增强接口
@GetMapping("/candidates")
public ApiResponse<CandidateListResponse> getCandidates() {
    // 原有实现保持不变
}

@GetMapping("/candidates/enhanced")
public ApiResponse<EnhancedCandidateListResponse> getEnhancedCandidates() {
    // 新的增强实现
}
```

### 3. 监控和日志

```java
@Slf4j
public class PatientDataAggregationService {
    
    public List<EnhancedCandidateItem> getPatientCandidatesWithStats(String search, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        try {
            // 业务逻辑
            log.info("获取候选患者列表成功，耗时: {}ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("获取候选患者列表失败", e);
            throw e;
        }
    }
}
```