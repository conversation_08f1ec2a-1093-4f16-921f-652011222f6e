package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗参数可用性响应DTO
 * 专门用于参数设置页面的治疗头数量检查
 */
public class TreatmentParametersAvailabilityResponse {
    
    private boolean sufficient;           // 总体是否充足
    private int totalNeeded;             // 总共需要的数量
    private int totalAvailable;          // 总共可用的数量
    
    private int shallowNeeded;           // 浅层需要的数量
    private int shallowAvailable;        // 浅层可用的数量
    private boolean shallowSufficient;   // 浅层是否充足
    
    private int deepNeeded;              // 深层需要的数量
    private int deepAvailable;           // 深层可用的数量
    private boolean deepSufficient;      // 深层是否充足
    
    public TreatmentParametersAvailabilityResponse() {}
    
    // Getters and Setters
    public boolean isSufficient() {
        return sufficient;
    }
    
    public void setSufficient(boolean sufficient) {
        this.sufficient = sufficient;
    }
    
    public int getTotalNeeded() {
        return totalNeeded;
    }
    
    public void setTotalNeeded(int totalNeeded) {
        this.totalNeeded = totalNeeded;
    }
    
    public int getTotalAvailable() {
        return totalAvailable;
    }
    
    public void setTotalAvailable(int totalAvailable) {
        this.totalAvailable = totalAvailable;
    }
    
    public int getShallowNeeded() {
        return shallowNeeded;
    }
    
    public void setShallowNeeded(int shallowNeeded) {
        this.shallowNeeded = shallowNeeded;
    }
    
    public int getShallowAvailable() {
        return shallowAvailable;
    }
    
    public void setShallowAvailable(int shallowAvailable) {
        this.shallowAvailable = shallowAvailable;
    }
    
    public boolean isShallowSufficient() {
        return shallowSufficient;
    }
    
    public void setShallowSufficient(boolean shallowSufficient) {
        this.shallowSufficient = shallowSufficient;
    }
    
    public int getDeepNeeded() {
        return deepNeeded;
    }
    
    public void setDeepNeeded(int deepNeeded) {
        this.deepNeeded = deepNeeded;
    }
    
    public int getDeepAvailable() {
        return deepAvailable;
    }
    
    public void setDeepAvailable(int deepAvailable) {
        this.deepAvailable = deepAvailable;
    }
    
    public boolean isDeepSufficient() {
        return deepSufficient;
    }
    
    public void setDeepSufficient(boolean deepSufficient) {
        this.deepSufficient = deepSufficient;
    }
    
    @Override
    public String toString() {
        return "TreatmentParametersAvailabilityResponse{" +
                "sufficient=" + sufficient +
                ", totalNeeded=" + totalNeeded +
                ", totalAvailable=" + totalAvailable +
                ", shallowNeeded=" + shallowNeeded +
                ", shallowAvailable=" + shallowAvailable +
                ", shallowSufficient=" + shallowSufficient +
                ", deepNeeded=" + deepNeeded +
                ", deepAvailable=" + deepAvailable +
                ", deepSufficient=" + deepSufficient +
                '}';
    }
} 