import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { useRouter } from 'vue-router'
import http from '@/utils/axios'

// 治疗详情状态枚举
export enum TreatmentDetailStatus {
  TREATING = 'TREATING',
  COMPLETED = 'COMPLETED',
  AWAITING_RETURN = 'AWAITING_RETURN',
  RETURNED = 'RETURNED',
  TERMINATED = 'TERMINATED'
}

// 进程状态枚举
export enum ProcessStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 治疗模式枚举
export enum TreatmentMode {
  ON_SITE = 'ON_SITE',
  TAKE_AWAY = 'TAKE_AWAY'
}

// 通知类型
export enum NotificationType {
  TREATMENT_COMPLETED = 'TREATMENT_COMPLETED',
  PICKUP_REMINDER = 'PICKUP_REMINDER'
}

// 治疗详情接口
export interface TreatmentDetail {
  detailId: number
  bodyPart: string
  status: string
  processId: number
  patientName: string
  treatmentMode: string
  duration?: number // 治疗时长（分钟）
  startTime?: string // 开始时间
  remainingTimeSeconds?: number // 剩余时间（秒）
  elapsedTimeSeconds?: number // 已用时间（秒）
  totalDurationSeconds?: number // 总时长（秒）
}

// 进程信息接口
export interface ProcessInfo {
  processId: number
  patientName: string
  treatmentMode: string
  status: string
  details: TreatmentDetail[]
}

// 通知数据接口
export interface NotificationData {
  id: string
  type: NotificationType
  processId: number
  patientName: string
  message: string
  timestamp: string
}

export const useGlobalTreatmentMonitorStore = defineStore('globalTreatmentMonitor', () => {
  // 状态
  const isMonitoring = ref(false)
  const activeProcesses = ref<ProcessInfo[]>([])
  const notifications = ref<NotificationData[]>([])
  const lastCheckTime = ref<Date | null>(null)
  const monitorInterval = ref<number | null>(null)
  
  // 监听间隔（毫秒）
  const MONITOR_INTERVAL = 5000 // 5秒检查一次
  
  // 需要排除的页面路由
  const EXCLUDED_ROUTES = [
    '/treatment-process',
    '/treatment-process-takeaway'
  ]

  // 计算属性
  const hasActiveNotifications = computed(() => notifications.value.length > 0)
  
  // 检查当前路由是否应该显示通知
  const shouldShowNotifications = (currentRoute: string): boolean => {
    return !EXCLUDED_ROUTES.some(route => currentRoute.startsWith(route))
  }

  // 获取所有进行中的治疗进程
  const fetchActiveProcesses = async (): Promise<ProcessInfo[]> => {
    try {
      const response = await http.get('/processes/details')
      if (response.data && response.data.details) {
        // 按进程ID分组
        const processMap = new Map<number, ProcessInfo>()

        response.data.details.forEach((detail: any) => {
          const processId = detail.processId
          if (!processMap.has(processId)) {
            processMap.set(processId, {
              processId,
              patientName: detail.patientName,
              treatmentMode: detail.treatmentMode,
              status: detail.processStatus,
              details: []
            })
          }

          processMap.get(processId)!.details.push({
            detailId: detail.detailId,
            bodyPart: detail.bodyPart,
            status: detail.statusCode, // 使用英文枚举状态
            processId: detail.processId,
            patientName: detail.patientName,
            treatmentMode: detail.treatmentMode,
            startTime: detail.startTime
          })
        })

        console.log('API响应详情数量:', response.data.details.length)
        console.log('分组后进程数量:', processMap.size)
        console.log('进程状态详情:', Array.from(processMap.values()).map(p => ({
          processId: p.processId,
          patientName: p.patientName,
          status: p.status,
          detailsCount: p.details.length
        })))

        // 只返回进行中的进程
        const allProcesses = Array.from(processMap.values())
        console.log('所有进程状态:', allProcesses.map(p => ({ processId: p.processId, status: p.status })))
        console.log('期望的进行中状态:', ProcessStatus.IN_PROGRESS)

        const activeProcesses = allProcesses.filter(process =>
          process.status === ProcessStatus.IN_PROGRESS
        )

        console.log('过滤后的进行中进程数量:', activeProcesses.length)

        // 获取每个进程的实时数据来检查时间
        const processesWithRealtimeData = await Promise.all(
          activeProcesses.map(async (process) => {
            try {
              const realtimeResponse = await http.get(`/processes/${process.processId}/realtime`)
              if (realtimeResponse.data && realtimeResponse.data.bodyParts) {
                // 更新详情的时间信息
                process.details.forEach(detail => {
                  const realtimeBodyPart = realtimeResponse.data.bodyParts.find(
                    (bp: any) => bp.bodyPart === detail.bodyPart
                  )
                  if (realtimeBodyPart) {
                    detail.remainingTimeSeconds = realtimeBodyPart.remainingTimeSeconds
                    detail.elapsedTimeSeconds = realtimeBodyPart.elapsedTimeSeconds
                    detail.totalDurationSeconds = realtimeBodyPart.totalDurationSeconds
                  }
                })
              }
              return process
            } catch (error) {
              console.error(`获取进程 ${process.processId} 实时数据失败:`, error)
              return process
            }
          })
        )

        return processesWithRealtimeData
      }
      return []
    } catch (error) {
      console.error('获取活跃进程失败:', error)
      return []
    }
  }

  // 检查并更新到期的治疗详情状态
  const checkAndUpdateExpiredTreatments = async (processes: ProcessInfo[]): Promise<ProcessInfo[]> => {
    const updatedProcesses: ProcessInfo[] = []

    console.log(`开始检查 ${processes.length} 个进程的治疗时间到期情况`)

    for (const process of processes) {
      const updatedDetails: TreatmentDetail[] = []
      let hasStatusUpdates = false

      console.log(`检查进程 ${process.processId} (${process.patientName}) - 治疗模式: ${process.treatmentMode}`)

      for (const detail of process.details) {
        let updatedDetail = { ...detail }

        console.log(`  部位: ${detail.bodyPart}, 状态: ${detail.status}, 剩余时间: ${detail.remainingTimeSeconds}秒`)

        // 检查是否需要更新状态（时间到期且仍在治疗中）
        if (detail.status === TreatmentDetailStatus.TREATING &&
            detail.remainingTimeSeconds !== undefined &&
            detail.remainingTimeSeconds <= 0) {

          console.log(`检测到部位 ${detail.bodyPart} 治疗时间到期，准备更新状态`)
          console.log(`- 进程ID: ${process.processId}`)
          console.log(`- 患者姓名: ${process.patientName}`)
          console.log(`- 治疗模式: ${process.treatmentMode}`)
          console.log(`- 剩余时间: ${detail.remainingTimeSeconds}秒`)
          console.log(`- 当前状态: ${detail.status}`)

          // 根据治疗模式决定目标状态
          const targetStatus = process.treatmentMode === TreatmentMode.ON_SITE
            ? TreatmentDetailStatus.COMPLETED
            : TreatmentDetailStatus.AWAITING_RETURN

          console.log(`- 目标状态: ${targetStatus}`)

          try {
            // 调用后端API更新状态
            if (targetStatus === TreatmentDetailStatus.COMPLETED) {
              await http.post(`/treatment-process/detail/${detail.detailId}/complete`)
            } else {
              await http.post(`/treatment-process/detail/${detail.detailId}/awaiting-return`)
            }

            updatedDetail.status = targetStatus
            hasStatusUpdates = true
            console.log(`部位 ${detail.bodyPart} 状态已更新为 ${targetStatus}`)

          } catch (error) {
            console.error(`更新部位 ${detail.bodyPart} 状态失败:`, error)
          }
        }

        updatedDetails.push(updatedDetail)
      }

      updatedProcesses.push({
        ...process,
        details: updatedDetails
      })

      if (hasStatusUpdates) {
        console.log(`进程 ${process.processId} 有状态更新`)
      }
    }

    return updatedProcesses
  }

  // 检查进程状态变化
  const checkProcessStatusChanges = async (): Promise<void> => {
    try {
      let currentProcesses = await fetchActiveProcesses()
      const previousProcesses = activeProcesses.value

      // 首先检查并更新到期的治疗状态
      currentProcesses = await checkAndUpdateExpiredTreatments(currentProcesses)

      // 检查每个进程的状态变化
      for (const currentProcess of currentProcesses) {
        const previousProcess = previousProcesses.find(p => p.processId === currentProcess.processId)

        if (previousProcess) {
          // 检查是否所有部位都完成了
          const allCompleted = currentProcess.details.every(detail =>
            detail.status === TreatmentDetailStatus.COMPLETED
          )

          const allAwaitingReturn = currentProcess.details.every(detail =>
            detail.status === TreatmentDetailStatus.AWAITING_RETURN
          )

          // 检查之前是否已经触发过通知
          const wasAllCompleted = previousProcess.details.every(detail =>
            detail.status === TreatmentDetailStatus.COMPLETED
          )

          const wasAllAwaitingReturn = previousProcess.details.every(detail =>
            detail.status === TreatmentDetailStatus.AWAITING_RETURN
          )

          // 场景1：本地治疗完成（所有部位状态变为 completed）
          if (allCompleted && !wasAllCompleted) {
            await triggerTreatmentCompletedNotification(currentProcess)
          }

          // 场景2：取走治疗待回收（所有部位状态变为 awaiting_return）
          if (allAwaitingReturn && !wasAllAwaitingReturn) {
            await triggerPickupReminderNotification(currentProcess)
          }
        }
      }

      // 更新活跃进程列表
      activeProcesses.value = currentProcesses
      lastCheckTime.value = new Date()

    } catch (error) {
      console.error('检查进程状态变化失败:', error)
    }
  }

  // 触发治疗完成通知
  const triggerTreatmentCompletedNotification = async (process: ProcessInfo): Promise<void> => {
    const notification: NotificationData = {
      id: `completed_${process.processId}_${Date.now()}`,
      type: NotificationType.TREATMENT_COMPLETED,
      processId: process.processId,
      patientName: process.patientName,
      message: `该患者${process.patientName}治疗已完成`,
      timestamp: new Date().toISOString()
    }

    notifications.value.push(notification)
    console.log('触发治疗完成通知:', notification)

    // 注意：不需要手动调用完成进程API，因为后端在完成治疗详情时会自动完成进程
    // 当所有治疗详情状态更新为COMPLETED时，后端的checkAndCompleteProcess会自动触发
    console.log('进程状态应该已由后端自动更新为COMPLETED')
  }

  // 触发取走治疗待回收通知
  const triggerPickupReminderNotification = async (process: ProcessInfo): Promise<void> => {
    const notification: NotificationData = {
      id: `pickup_${process.processId}_${Date.now()}`,
      type: NotificationType.PICKUP_REMINDER,
      processId: process.processId,
      patientName: process.patientName,
      message: `该患者治疗头待取回`,
      timestamp: new Date().toISOString()
    }

    notifications.value.push(notification)
    console.log('触发取走治疗待回收通知:', notification)

    // 注意：不需要手动调用终止进程API，因为后端在设置待回收状态时会自动更新进程状态
    // 当所有治疗详情状态更新为AWAITING_RETURN时，后端的checkAndUpdateProcessForAwaitingReturn会自动触发
    console.log('进程状态应该已由后端自动更新为CANCELLED（待回收）')
  }

  // 注意：移除了updateProcessStatus函数，因为后端会自动处理进程状态更新
  // 当治疗详情状态更新时，后端的checkAndCompleteProcess和checkAndUpdateProcessForAwaitingReturn
  // 会自动检查并更新进程状态，无需前端手动调用

  // 移除通知
  const removeNotification = (notificationId: string): void => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清除所有通知
  const clearAllNotifications = (): void => {
    notifications.value = []
  }

  // 开始监听
  const startMonitoring = (): void => {
    if (isMonitoring.value) {
      return
    }
    
    isMonitoring.value = true
    console.log('开始全局治疗状态监听')
    
    // 立即执行一次检查
    checkProcessStatusChanges()
    
    // 设置定时检查
    monitorInterval.value = window.setInterval(() => {
      checkProcessStatusChanges()
    }, MONITOR_INTERVAL)
  }

  // 停止监听
  const stopMonitoring = (): void => {
    if (!isMonitoring.value) {
      return
    }
    
    isMonitoring.value = false
    console.log('停止全局治疗状态监听')
    
    if (monitorInterval.value) {
      clearInterval(monitorInterval.value)
      monitorInterval.value = null
    }
  }

  // 重置状态
  const reset = (): void => {
    stopMonitoring()
    activeProcesses.value = []
    notifications.value = []
    lastCheckTime.value = null
  }

  return {
    // 状态
    isMonitoring,
    activeProcesses,
    notifications,
    lastCheckTime,

    // 计算属性
    hasActiveNotifications,

    // 方法
    shouldShowNotifications,
    fetchActiveProcesses,
    checkProcessStatusChanges,
    triggerTreatmentCompletedNotification,
    triggerPickupReminderNotification,
    removeNotification,
    clearAllNotifications,
    startMonitoring,
    stopMonitoring,
    reset
  }
})
