.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.box_1 {
  height: 56.25vw;
  background: url(./img/79feb2122d04465638aba851729e875a.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 100vw;
}

.group_1 {
  width: 68.7vw;
  height: 40.58vw;
  margin: 6.04vw 0 0 15.67vw;
}

.block_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  height: 40.58vw;
  border: 1px solid rgba(96, 96, 96, 1);
  width: 68.7vw;
  position: relative;
}

.image-wrapper_1 {
  width: 3.13vw;
  height: 3.13vw;
  margin: 1.61vw 0 0 63.33vw;
}

.image_1 {
  width: 3.13vw;
  height: 3.13vw;
}

.text-wrapper_1 {
  width: 6.83vw;
  height: 2.5vw;
  margin-left: 31.67vw;
}

.text_1 {
  width: 6.83vw;
  height: 2.5vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.group_2 {
  width: 37.3vw;
  height: 3.18vw;
  margin: 4.68vw 0 0 11.51vw;
}

.text_2 {
  width: 10.89vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.13vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.47vw;
}

.text-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 10.21vw;
  margin: -0.1vw 0 0 3.75vw;
}

.text_3 {
  width: 7.97vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 1.04vw;
}

.text-wrapper_3 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 10.21vw;
  margin: -0.1vw -0.1vw 0 2.34vw;
}

.text_4 {
  width: 7.92vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.83vw 0 0 1.04vw;
}

.group_3 {
  width: 35.89vw;
  height: 2.4vw;
  margin: 2.13vw 0 0 11.56vw;
}

.text_5 {
  width: 10.84vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.13vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.image_2 {
  width: 2.61vw;
  height: 2.35vw;
  margin-left: 6.05vw;
}

.image_3 {
  width: 11.46vw;
  height: 0.16vw;
  margin: 1.14vw 0 0 1.35vw;
}

.image_4 {
  width: 2.61vw;
  height: 2.35vw;
  margin: 0.05vw 0 0 0.98vw;
}

.group_4 {
  width: 32.19vw;
  height: 3.18vw;
  margin: 2.34vw 0 0 11.51vw;
}

.text_6 {
  width: 10.89vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.13vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.58vw;
}

.text-wrapper_4 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 9.02vw;
  margin: -0.1vw 0 0 3.75vw;
}

.text_7 {
  width: 6.41vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 1.35vw;
}

.image-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 15px;
  height: 2.14vw;
  width: 5.06vw;
  margin: 0.57vw 0 0 3.48vw;
}

.label_1 {
  width: 1.72vw;
  height: 1.72vw;
  margin: 0.26vw 0 0 2.76vw;
}

.group_5 {
  width: 35vw;
  height: 3.18vw;
  margin: 1.66vw 0 0 11.56vw;
}

.text_8 {
  width: 10.84vw;
  height: 2.19vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.13vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.53vw;
}

.text-wrapper_5 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 9.02vw;
  margin: -0.1vw 0 0 3.75vw;
}

.text_9 {
  width: 3.7vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.72vw 0 0 2.76vw;
}

.text-wrapper_6 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 9.02vw;
  margin: -0.1vw -0.1vw 0 2.5vw;
}

.text_10 {
  width: 6.62vw;
  height: 0.99vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.19vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.52vw;
  margin: 1.19vw 0 0 1.3vw;
}

.group_6 {
  width: 46.67vw;
  height: 3.18vw;
  margin: 1.77vw 0 0 11.56vw;
}

.text_11 {
  width: 10.84vw;
  height: 2.14vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.13vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.16vw;
}

.text-wrapper_7 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 9.02vw;
  margin: -0.1vw 0 0 3.75vw;
}

.text_12 {
  width: 6.31vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 1.45vw;
}

.text-wrapper_8 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 9.02vw;
  margin: -0.1vw 0 0 2.39vw;
}

.text_13 {
  width: 6.31vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 1.45vw;
}

.text-wrapper_9 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 3.39vw;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 9.02vw;
  margin: -0.1vw -0.1vw 0 2.76vw;
}

.text_14 {
  width: 6.41vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 1.35vw;
}

.image-wrapper_3 {
  width: 8.34vw;
  height: 1.31vw;
  margin: 1.71vw 0 2.6vw 31.19vw;
}

.image_5 {
  width: 8.34vw;
  height: 1.31vw;
}

.group_7 {
  background-color: rgba(239, 128, 41, 1);
  border-radius: 7px;
  position: absolute;
  left: 34.22vw;
  top: 17.5vw;
  width: 1.15vw;
  height: 2.24vw;
  border: 3px solid rgba(135, 132, 130, 1);
}

.image-wrapper_4 {
  width: 7.19vw;
  height: 2.61vw;
  margin: 4.73vw 0 2.29vw 87.6vw;
}

.image_6 {
  width: 7.19vw;
  height: 2.61vw;
}
