package com.Bone.BoneSys.exception;

/**
 * 硬件相关异常的基类
 */
public class HardwareException extends Exception {
    
    private final String errorCode;
    private final HardwareErrorType errorType;
    private final boolean recoverable;
    
    public HardwareException(String message) {
        super(message);
        this.errorCode = "HARDWARE_ERROR";
        this.errorType = HardwareErrorType.UNKNOWN;
        this.recoverable = false;
    }
    
    public HardwareException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "HARDWARE_ERROR";
        this.errorType = HardwareErrorType.UNKNOWN;
        this.recoverable = false;
    }
    
    public HardwareException(String errorCode, String message, HardwareErrorType errorType, boolean recoverable) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.recoverable = recoverable;
    }
    
    public HardwareException(String errorCode, String message, Throwable cause, HardwareErrorType errorType, boolean recoverable) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.recoverable = recoverable;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public HardwareErrorType getErrorType() {
        return errorType;
    }
    
    public boolean isRecoverable() {
        return recoverable;
    }
    
    /**
     * 硬件错误类型枚举
     */
    public enum HardwareErrorType {
        CONNECTION_LOST("连接丢失", true),
        TIMEOUT("通信超时", true),
        INVALID_RESPONSE("无效响应", false),
        DEVICE_NOT_FOUND("设备未找到", true),
        PERMISSION_DENIED("权限拒绝", false),
        DEVICE_BUSY("设备忙碌", true),
        COMMAND_FAILED("命令执行失败", false),
        UNKNOWN("未知错误", false);
        
        private final String description;
        private final boolean defaultRecoverable;
        
        HardwareErrorType(String description, boolean defaultRecoverable) {
            this.description = description;
            this.defaultRecoverable = defaultRecoverable;
        }
        
        public String getDescription() {
            return description;
        }
        
        public boolean isDefaultRecoverable() {
            return defaultRecoverable;
        }
    }
}