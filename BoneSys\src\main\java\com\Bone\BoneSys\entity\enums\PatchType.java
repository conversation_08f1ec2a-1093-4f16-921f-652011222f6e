package com.Bone.BoneSys.entity.enums;

/**
 * 贴片类型枚举
 */
public enum PatchType {
    /**
     * 浅层贴片
     */
    SHALLOW("浅层"),
    
    /**
     * 深部贴片
     */
    DEEP("深部");
    
    private final String description;
    
    PatchType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据字符串获取枚举值
     */
    public static PatchType fromString(String value) {
        if (value == null) {
            return null;
        }
        
        for (PatchType type : PatchType.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("无效的贴片类型: " + value);
    }
}