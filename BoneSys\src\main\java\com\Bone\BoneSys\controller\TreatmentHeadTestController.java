package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.enums.LightColor;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import com.Bone.BoneSys.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 治疗头测试控制器
 * 用于测试更新后的治疗头功能
 */
@RestController
@RequestMapping("/api/treatment-heads/test")
public class TreatmentHeadTestController {
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    /**
     * 获取所有治疗头信息
     */
    @GetMapping("/all")
    public ApiResponse<List<TreatmentHead>> getAllTreatmentHeads() {
        List<TreatmentHead> heads = treatmentHeadRepository.findAll();
        return ApiResponse.success("获取治疗头列表成功", heads);
    }
    
    /**
     * 根据仓位类型获取治疗头
     */
    @GetMapping("/compartment/{type}")
    public ApiResponse<List<TreatmentHead>> getTreatmentHeadsByCompartment(@PathVariable String type) {
        List<TreatmentHead> heads;
        
        if ("shallow".equalsIgnoreCase(type)) {
            heads = treatmentHeadRepository.findShallowCompartmentHeads();
        } else if ("deep".equalsIgnoreCase(type)) {
            heads = treatmentHeadRepository.findDeepCompartmentHeads();
        } else {
            return ApiResponse.badRequest("无效的仓位类型，请使用 'shallow' 或 'deep'");
        }
        
        return ApiResponse.success("获取" + type + "仓位治疗头成功", heads);
    }
    
    /**
     * 设置治疗头指示灯颜色
     */
    @PostMapping("/{headNumber}/light/{color}")
    public ApiResponse<TreatmentHead> setLightColor(@PathVariable Integer headNumber, @PathVariable Integer color) {
        if (headNumber < 1 || headNumber > Constants.Compartment.TOTAL_HEADS) {
            return ApiResponse.badRequest("治疗头编号必须在1-20之间");
        }
        
        if (color < 0 || color > 3) {
            return ApiResponse.badRequest("指示灯颜色代码必须在0-3之间");
        }
        
        return treatmentHeadRepository.findByHeadNumber(headNumber)
                .map(head -> {
                    head.setLightColor(color);
                    TreatmentHead savedHead = treatmentHeadRepository.save(head);
                    return ApiResponse.success("设置治疗头" + headNumber + "指示灯颜色为" + 
                            LightColor.fromCode(color).getDescription() + "成功", savedHead);
                })
                .orElse(ApiResponse.notFound("治疗头编号" + headNumber + "不存在"));
    }
    
    /**
     * 获取治疗头统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getTreatmentHeadStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总数统计
        long totalHeads = treatmentHeadRepository.count();
        List<TreatmentHead> shallowHeads = treatmentHeadRepository.findShallowCompartmentHeads();
        List<TreatmentHead> deepHeads = treatmentHeadRepository.findDeepCompartmentHeads();
        List<TreatmentHead> lightsOn = treatmentHeadRepository.findHeadsWithLightOn();
        
        // 按状态统计
        List<TreatmentHead> chargingHeads = treatmentHeadRepository.findChargingHeads();
        List<TreatmentHead> chargedHeads = treatmentHeadRepository.findChargedHeads();
        List<TreatmentHead> treatingHeads = treatmentHeadRepository.findTreatingHeads();
        
        stats.put("totalHeads", totalHeads);
        stats.put("shallowCompartmentCount", shallowHeads.size());
        stats.put("deepCompartmentCount", deepHeads.size());
        stats.put("lightsOnCount", lightsOn.size());
        stats.put("chargingCount", chargingHeads.size());
        stats.put("chargedCount", chargedHeads.size());
        stats.put("treatingCount", treatingHeads.size());
        
        // 状态统计
        List<Object[]> statusStats = treatmentHeadRepository.countByStatus();
        Map<String, Object> statusMap = new HashMap<>();
        for (Object[] stat : statusStats) {
            String status = stat[0].toString();
            Long count = (Long) stat[1];
            String description;
            switch (status) {
                case "CHARGING": description = "充电中"; break;
                case "CHARGED": description = "充电完成"; break;
                case "TREATING": description = "治疗中"; break;
                default: description = "未知状态"; break;
            }
            Map<String, Object> statusInfo = new HashMap<>();
            statusInfo.put("count", count);
            statusInfo.put("description", description);
            statusMap.put(status, statusInfo);
        }
        stats.put("statusStatistics", statusMap);
        
        // 指示灯颜色统计
        Map<String, Long> lightColorStats = new HashMap<>();
        for (int i = 0; i <= 3; i++) {
            long count = treatmentHeadRepository.findByLightColor(i).size();
            lightColorStats.put(LightColor.fromCode(i).getDescription(), count);
        }
        stats.put("lightColorStatistics", lightColorStats);
        
        return ApiResponse.success("获取治疗头统计信息成功", stats);
    }
    
    /**
     * 测试治疗头业务方法
     */
    @GetMapping("/{headNumber}/info")
    public ApiResponse<Map<String, Object>> getTreatmentHeadInfo(@PathVariable Integer headNumber) {
        return treatmentHeadRepository.findByHeadNumber(headNumber)
                .map(head -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("headNumber", head.getHeadNumber());
                    info.put("slotNumber", head.getSlotNumber());
                    info.put("compartmentType", head.getCompartmentType());
                    info.put("lightColor", head.getLightColor());
                    info.put("lightColorDescription", head.getLightColorDescription());
                    info.put("isLightOn", head.isLightOn());
                    info.put("realtimeStatus", head.getRealtimeStatus());
                    info.put("batteryLevel", head.getBatteryLevel());
                    info.put("isBatteryLow", head.isBatteryLow());
                    info.put("usagePercentage", head.getUsagePercentage());
                    info.put("needsReplacement", head.needsReplacement());
                    info.put("isShallowCompartment", head.isShallowCompartment());
                    info.put("isDeepCompartment", head.isDeepCompartment());
                    
                    return ApiResponse.success("获取治疗头" + headNumber + "详细信息成功", info);
                })
                .orElse(ApiResponse.notFound("治疗头编号" + headNumber + "不存在"));
    }
    
    /**
     * 更改治疗头状态
     */
    @PostMapping("/{headNumber}/status/{status}")
    public ApiResponse<TreatmentHead> changeStatus(@PathVariable Integer headNumber, @PathVariable String status) {
        if (headNumber < 1 || headNumber > Constants.Compartment.TOTAL_HEADS) {
            return ApiResponse.badRequest("治疗头编号必须在1-20之间");
        }
        
        // 验证状态值
        if (!status.equals("CHARGING") && !status.equals("CHARGED") && !status.equals("TREATING")) {
            return ApiResponse.badRequest("状态必须是 CHARGING(充电中)、CHARGED(充电完成) 或 TREATING(治疗中)");
        }
        
        Optional<TreatmentHead> headOptional = treatmentHeadRepository.findByHeadNumber(headNumber);
        if (!headOptional.isPresent()) {
            return ApiResponse.notFound("治疗头编号" + headNumber + "不存在");
        }
        
        TreatmentHead head = headOptional.get();
        com.Bone.BoneSys.entity.enums.TreatmentHeadStatus newStatus;
        
        switch (status) {
            case "CHARGING": 
                newStatus = com.Bone.BoneSys.entity.enums.TreatmentHeadStatus.CHARGING; 
                break;
            case "CHARGED": 
                newStatus = com.Bone.BoneSys.entity.enums.TreatmentHeadStatus.CHARGED; 
                break;
            case "TREATING": 
                newStatus = com.Bone.BoneSys.entity.enums.TreatmentHeadStatus.TREATING; 
                break;
            default: 
                return ApiResponse.badRequest("无效的状态值");
        }
        
        head.setRealtimeStatus(newStatus);
        
        // 根据状态调整电量
        if (newStatus == com.Bone.BoneSys.entity.enums.TreatmentHeadStatus.CHARGED) {
            head.setBatteryLevel(100); // 充电完成时电量为100%
        } else if (newStatus == com.Bone.BoneSys.entity.enums.TreatmentHeadStatus.TREATING) {
            // 治疗中时电量可能会下降
            if (head.getBatteryLevel() != null && head.getBatteryLevel() > 20) {
                head.setBatteryLevel(head.getBatteryLevel() - 5);
            }
        }
        
        TreatmentHead savedHead = treatmentHeadRepository.save(head);
        return ApiResponse.success("治疗头" + headNumber + "状态已更改为" + 
                savedHead.getStatusDescription(), savedHead);
    }
    
    /**
     * 获取各状态的治疗头列表
     */
    @GetMapping("/status/{status}")
    public ApiResponse<List<TreatmentHead>> getTreatmentHeadsByStatus(@PathVariable String status) {
        List<TreatmentHead> heads;
        String description;
        
        switch (status.toLowerCase()) {
            case "charging":
                heads = treatmentHeadRepository.findChargingHeads();
                description = "充电中";
                break;
            case "charged":
                heads = treatmentHeadRepository.findChargedHeads();
                description = "充电完成";
                break;
            case "treating":
                heads = treatmentHeadRepository.findTreatingHeads();
                description = "治疗中";
                break;
            default:
                return ApiResponse.badRequest("无效的状态，请使用 charging、charged 或 treating");
        }
        
        return ApiResponse.success("获取" + description + "的治疗头列表成功", heads);
    }
}