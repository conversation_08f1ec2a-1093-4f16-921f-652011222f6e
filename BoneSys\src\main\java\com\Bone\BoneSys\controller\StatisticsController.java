package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.StatisticsService;
import com.Bone.BoneSys.service.StatisticsService.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 统计数据控制器
 * 提供各种治疗数据的统计分析接口
 */
@RestController
@RequestMapping("/api/statistics")
@CrossOrigin(origins = "*")
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 获取患者治疗统计数据
     * GET /api/statistics/patient/{patientId}
     */
    @GetMapping("/patient/{patientId}")
    public ApiResponse<PatientTreatmentStatistics> getPatientTreatmentStatistics(@PathVariable Long patientId) {
        try {
            logger.info("API request: get patient treatment statistics for patient {}", patientId);
            
            PatientTreatmentStatistics statistics = statisticsService.getPatientTreatmentStatistics(patientId);
            
            if (statistics.getPatientName() == null) {
                return ApiResponse.notFound("患者不存在");
            }
            
            logger.info("Successfully retrieved patient treatment statistics for patient {}", patientId);
            return ApiResponse.success("患者治疗统计数据获取成功", statistics);
            
        } catch (Exception e) {
            logger.error("Error retrieving patient treatment statistics for patient {}", patientId, e);
            return ApiResponse.error(500, "获取患者治疗统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取全局治疗统计数据
     * GET /api/statistics/global
     */
    @GetMapping("/global")
    public ApiResponse<GlobalTreatmentStatistics> getGlobalTreatmentStatistics() {
        try {
            logger.info("API request: get global treatment statistics");
            
            GlobalTreatmentStatistics statistics = statisticsService.getGlobalTreatmentStatistics();
            
            logger.info("Successfully retrieved global treatment statistics");
            return ApiResponse.success("全局治疗统计数据获取成功", statistics);
            
        } catch (Exception e) {
            logger.error("Error retrieving global treatment statistics", e);
            return ApiResponse.error(500, "获取全局治疗统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取部位治疗统计数据
     * GET /api/statistics/body-part/{bodyPart}
     */
    @GetMapping("/body-part/{bodyPart}")
    public ApiResponse<BodyPartTreatmentStatistics> getBodyPartTreatmentStatistics(@PathVariable String bodyPart) {
        try {
            logger.info("API request: get body part treatment statistics for {}", bodyPart);
            
            BodyPartTreatmentStatistics statistics = statisticsService.getBodyPartTreatmentStatistics(bodyPart);
            
            logger.info("Successfully retrieved body part treatment statistics for {}", bodyPart);
            return ApiResponse.success("部位治疗统计数据获取成功", statistics);
            
        } catch (Exception e) {
            logger.error("Error retrieving body part treatment statistics for {}", bodyPart, e);
            return ApiResponse.error(500, "获取部位治疗统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取时间段内的治疗统计
     * GET /api/statistics/time-period
     */
    @GetMapping("/time-period")
    public ApiResponse<TimePeriodStatistics> getTimePeriodStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            logger.info("API request: get time period statistics from {} to {}", startDate, endDate);
            
            // 验证日期范围
            if (startDate.isAfter(endDate)) {
                return ApiResponse.badRequest("开始日期不能晚于结束日期");
            }
            
            // 限制查询范围不超过1年
            if (startDate.plusYears(1).isBefore(endDate)) {
                return ApiResponse.badRequest("查询时间范围不能超过1年");
            }
            
            TimePeriodStatistics statistics = statisticsService.getTimePeriodStatistics(startDate, endDate);
            
            logger.info("Successfully retrieved time period statistics from {} to {}", startDate, endDate);
            return ApiResponse.success("时间段治疗统计数据获取成功", statistics);
            
        } catch (Exception e) {
            logger.error("Error retrieving time period statistics from {} to {}", startDate, endDate, e);
            return ApiResponse.error(500, "获取时间段治疗统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗统计概览
     * GET /api/statistics/overview
     */
    @GetMapping("/overview")
    public ApiResponse<TreatmentStatisticsOverview> getTreatmentStatisticsOverview() {
        try {
            logger.info("API request: get treatment statistics overview");
            
            TreatmentStatisticsOverview overview = new TreatmentStatisticsOverview();
            
            // 获取全局统计
            GlobalTreatmentStatistics globalStats = statisticsService.getGlobalTreatmentStatistics();
            overview.setGlobalStatistics(globalStats);
            
            // 获取最近30天的统计
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(30);
            TimePeriodStatistics recentStats = statisticsService.getTimePeriodStatistics(startDate, endDate);
            overview.setRecentStatistics(recentStats);
            
            // 获取最受欢迎的治疗部位（前5个）
            overview.setTopBodyParts(globalStats.getBodyPartUsageStatistics().stream()
                .sorted((a, b) -> Long.compare(b.getUsageCount(), a.getUsageCount()))
                .limit(5)
                .collect(java.util.stream.Collectors.toList()));
            
            // 获取最常用的治疗头（前5个）
            overview.setTopTreatmentHeads(globalStats.getTreatmentHeadUsageStatistics().stream()
                .sorted((a, b) -> Long.compare(b.getUsageCount(), a.getUsageCount()))
                .limit(5)
                .collect(java.util.stream.Collectors.toList()));
            
            logger.info("Successfully retrieved treatment statistics overview");
            return ApiResponse.success("治疗统计概览获取成功", overview);
            
        } catch (Exception e) {
            logger.error("Error retrieving treatment statistics overview", e);
            return ApiResponse.error(500, "获取治疗统计概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取患者治疗统计概览（简化版）
     * GET /api/statistics/patient/{patientId}/summary
     */
    @GetMapping("/patient/{patientId}/summary")
    public ApiResponse<PatientStatisticsSummary> getPatientStatisticsSummary(@PathVariable Long patientId) {
        try {
            logger.info("API request: get patient statistics summary for patient {}", patientId);
            
            PatientTreatmentStatistics fullStats = statisticsService.getPatientTreatmentStatistics(patientId);
            
            if (fullStats.getPatientName() == null) {
                return ApiResponse.notFound("患者不存在");
            }
            
            // 构建简化的统计摘要
            PatientStatisticsSummary summary = new PatientStatisticsSummary();
            summary.setPatientId(patientId);
            summary.setPatientName(fullStats.getPatientName());
            summary.setPatientCardId(fullStats.getPatientCardId());
            summary.setTotalProcesses(fullStats.getTotalProcesses());
            summary.setCompletedProcesses(fullStats.getCompletedProcesses());
            summary.setTotalTreatmentDuration(fullStats.getTotalTreatmentDuration());
            summary.setMostUsedBodyPart(fullStats.getMostUsedBodyPart());
            
            // 计算完成率
            if (fullStats.getTotalProcesses() > 0) {
                double completionRate = (double) fullStats.getCompletedProcesses() / fullStats.getTotalProcesses() * 100;
                summary.setCompletionRate(Math.round(completionRate * 100.0) / 100.0);
            } else {
                summary.setCompletionRate(0.0);
            }
            
            // 获取最常用的3个部位
            summary.setTopBodyParts(fullStats.getBodyPartStatistics().stream()
                .sorted((a, b) -> Long.compare(b.getTotalUsageCount(), a.getTotalUsageCount()))
                .limit(3)
                .map(BodyPartStatistics::getBodyPart)
                .collect(java.util.stream.Collectors.toList()));
            
            logger.info("Successfully retrieved patient statistics summary for patient {}", patientId);
            return ApiResponse.success("患者治疗统计摘要获取成功", summary);
            
        } catch (Exception e) {
            logger.error("Error retrieving patient statistics summary for patient {}", patientId, e);
            return ApiResponse.error(500, "获取患者治疗统计摘要失败: " + e.getMessage());
        }
    }

    // 内部DTO类
    public static class TreatmentStatisticsOverview {
        private GlobalTreatmentStatistics globalStatistics;
        private TimePeriodStatistics recentStatistics;
        private java.util.List<BodyPartUsageStatistics> topBodyParts;
        private java.util.List<TreatmentHeadUsageStatistics> topTreatmentHeads;

        // Getters and Setters
        public GlobalTreatmentStatistics getGlobalStatistics() { return globalStatistics; }
        public void setGlobalStatistics(GlobalTreatmentStatistics globalStatistics) { this.globalStatistics = globalStatistics; }
        
        public TimePeriodStatistics getRecentStatistics() { return recentStatistics; }
        public void setRecentStatistics(TimePeriodStatistics recentStatistics) { this.recentStatistics = recentStatistics; }
        
        public java.util.List<BodyPartUsageStatistics> getTopBodyParts() { return topBodyParts; }
        public void setTopBodyParts(java.util.List<BodyPartUsageStatistics> topBodyParts) { this.topBodyParts = topBodyParts; }
        
        public java.util.List<TreatmentHeadUsageStatistics> getTopTreatmentHeads() { return topTreatmentHeads; }
        public void setTopTreatmentHeads(java.util.List<TreatmentHeadUsageStatistics> topTreatmentHeads) { this.topTreatmentHeads = topTreatmentHeads; }
    }

    public static class PatientStatisticsSummary {
        private Long patientId;
        private String patientName;
        private String patientCardId;
        private Integer totalProcesses;
        private Integer completedProcesses;
        private Integer totalTreatmentDuration;
        private String mostUsedBodyPart;
        private Double completionRate;
        private java.util.List<String> topBodyParts;

        // Getters and Setters
        public Long getPatientId() { return patientId; }
        public void setPatientId(Long patientId) { this.patientId = patientId; }
        
        public String getPatientName() { return patientName; }
        public void setPatientName(String patientName) { this.patientName = patientName; }
        
        public String getPatientCardId() { return patientCardId; }
        public void setPatientCardId(String patientCardId) { this.patientCardId = patientCardId; }
        
        public Integer getTotalProcesses() { return totalProcesses; }
        public void setTotalProcesses(Integer totalProcesses) { this.totalProcesses = totalProcesses; }
        
        public Integer getCompletedProcesses() { return completedProcesses; }
        public void setCompletedProcesses(Integer completedProcesses) { this.completedProcesses = completedProcesses; }
        
        public Integer getTotalTreatmentDuration() { return totalTreatmentDuration; }
        public void setTotalTreatmentDuration(Integer totalTreatmentDuration) { this.totalTreatmentDuration = totalTreatmentDuration; }
        
        public String getMostUsedBodyPart() { return mostUsedBodyPart; }
        public void setMostUsedBodyPart(String mostUsedBodyPart) { this.mostUsedBodyPart = mostUsedBodyPart; }
        
        public Double getCompletionRate() { return completionRate; }
        public void setCompletionRate(Double completionRate) { this.completionRate = completionRate; }
        
        public java.util.List<String> getTopBodyParts() { return topBodyParts; }
        public void setTopBodyParts(java.util.List<String> topBodyParts) { this.topBodyParts = topBodyParts; }
    }
}
