.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.block_1 {
  height: 1080px;
  background: url(./img/51da9ff83e9a4afabde39995a337d4f7.png)
    0px 0px no-repeat;
  background-size: 1920px 1080px;
  margin-left: 2px;
  width: 1918px;
}

.box_1 {
  width: 1918px;
  height: 1080px;
  background: url(./img/5ef5f27141c70c1e7e5a0f61a5b734f0.png)
    0px 0px no-repeat;
  background-size: 1952px 1094px;
}

.group_9 {
  width: 1003px;
  height: 70px;
  margin: 20px 0 0 103px;
}

.image_1 {
  width: 169px;
  height: 70px;
}

.text_1 {
  width: 294px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
}

.group_10 {
  width: 1585px;
  height: 91px;
  margin: 50px 0 0 142px;
}

.group_1 {
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 474px;
  height: 66px;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin-top: 13px;
}

.text_2 {
  width: 153px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 36px;
}

.image_2 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 30px;
}

.text_3 {
  width: 95px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 75px 0 84px;
}

.group_2 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 346px;
  height: 66px;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 13px 0 0 58px;
}

.text_4 {
  width: 70px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 37px;
}

.image_3 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 27px;
}

.text_5 {
  width: 95px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 59px 0 57px;
}

.text-wrapper_1 {
  height: 91px;
  background: url(./img/e6ee0182dc4d4797b0875b0a7f9e836c.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 480px;
  width: 227px;
}

.text_6 {
  width: 80px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 0 0 71px;
}

.section_3 {
  width: 1689px;
  height: 783px;
  background: url(./img/f7ef7ec148e0f2b11bc0f46f49c5c8a0.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 25px 0 41px 109px;
}

.text-wrapper_10 {
  width: 1368px;
  height: 34px;
  margin: 47px 0 0 168px;
}

.text_7 {
  width: 156px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 1px;
}

.text_8 {
  width: 71px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 1px 0 0 112px;
}

.text_9 {
  width: 74px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 94px;
}

.text_10 {
  width: 73px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 1px 0 0 61px;
}

.text_11 {
  width: 157px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 1px 0 0 99px;
}

.text_12 {
  width: 155px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 1px 0 0 75px;
}

.text_13 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 1px 0 0 166px;
}

.image_4 {
  width: 1476px;
  height: 1px;
  margin: 17px 0 0 116px;
}

.box_4 {
  width: 1397px;
  height: 47px;
  margin: 11px 0 0 188px;
}

.text_14 {
  width: 114px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 15px;
}

.text_15 {
  width: 115px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 14px 0 0 113px;
}

.text_16 {
  width: 34px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 15px 0 0 90px;
}

.text_17 {
  width: 24px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 15px 0 0 110px;
}

.text_18 {
  width: 45px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 14px 0 0 176px;
}

.text_19 {
  width: 159px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 0 0 110px;
}

.text-wrapper_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 115px;
  width: 89px;
}

.text_20 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 14px;
}

.text-wrapper_4 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 14px;
  width: 89px;
}

.text_21 {
  width: 61px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 14px;
}

.image_5 {
  width: 1476px;
  height: 1px;
  margin: 12px 0 0 116px;
}

.box_5 {
  width: 1397px;
  height: 47px;
  margin: 11px 0 0 188px;
}

.text_22 {
  width: 114px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 18px;
}

.text_23 {
  width: 115px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 17px 0 0 113px;
}

.text_24 {
  width: 34px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 18px 0 0 90px;
}

.text_25 {
  width: 24px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 18px 0 0 110px;
}

.text_26 {
  width: 46px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 175px;
}

.text_27 {
  width: 159px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 17px 0 0 110px;
}

.text-wrapper_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 115px;
  width: 89px;
}

.text_28 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 14px;
}

.text-wrapper_6 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 14px;
  width: 89px;
}

.text_29 {
  width: 61px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 14px;
}

.image-wrapper_1 {
  height: 1px;
  background: url(./img/86bf3c543f63e690373da9f224fe1a88.png)
    0px -1px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 12px 0 0 116px;
}

.image_6 {
  width: 1476px;
  height: 1px;
}

.box_6 {
  width: 1397px;
  height: 47px;
  margin: 11px 0 0 188px;
}

.text_30 {
  width: 114px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 17px;
}

.text_31 {
  width: 115px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 113px;
}

.text_32 {
  width: 34px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 17px 0 0 90px;
}

.text_33 {
  width: 24px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 17px 0 0 110px;
}

.text_34 {
  width: 45px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 176px;
}

.text-wrapper_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 384px;
  width: 89px;
}

.text_35 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 14px;
}

.text-wrapper_8 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 14px;
  width: 89px;
}

.text_36 {
  width: 61px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 14px;
}

.image-wrapper_2 {
  height: 1px;
  background: url(./img/69de334a917d2b69b72c6daeca0f5a57.png)
    0px -1px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 12px 0 0 116px;
}

.image_7 {
  width: 1476px;
  height: 1px;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 57px;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 260px;
  margin: 307px 0 107px 1366px;
}

.image_8 {
  width: 235px;
  height: 38px;
  margin: 9px 0 0 10px;
}
