package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.dto.sync.SyncResult;
import com.Bone.BoneSys.dto.sync.SyncStatus;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 治疗头自动同步服务
 * 
 * 功能：
 * 1. 应用启动时初始化治疗头数据
 * 2. 定期同步治疗头状态到数据库
 * 3. 提供同步状态监控和手动触发功能
 * 
 * 配置参数：
 * - treatment-head.sync.enabled: 是否启用定时同步（默认true）
 * - treatment-head.sync.interval: 同步间隔毫秒数（默认10000ms）
 * - treatment-head.sync.startup-delay: 启动延迟毫秒数（默认5000ms）
 */
@Service
public class TreatmentHeadSyncService {
    
    private static final Logger logger = LoggerFactory.getLogger(TreatmentHeadSyncService.class);
    
    // 配置参数
    @Value("${treatment-head.sync.enabled:true}")
    private boolean syncEnabled;
    
    @Value("${treatment-head.sync.interval:10000}")
    private long syncInterval;
    
    @Value("${treatment-head.sync.startup-delay:5000}")
    private long startupDelay;
    
    // 依赖服务
    @Autowired
    private HardwareService hardwareService;
    
    // 同步状态跟踪
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong totalSyncCount = new AtomicLong(0);
    private final AtomicLong successfulSyncCount = new AtomicLong(0);
    private final AtomicLong failedSyncCount = new AtomicLong(0);
    
    private volatile LocalDateTime lastSyncTime;
    private volatile boolean lastSyncSuccess = false;
    private volatile String lastErrorMessage;
    private volatile long lastExecutionTimeMs = 0;
    
    /**
     * 应用启动时初始化治疗头数据
     */
    @PostConstruct
    public void initializeOnStartup() {
        if (!syncEnabled) {
            logger.info("Treatment head sync is disabled, skipping startup initialization");
            return;
        }
        
        logger.info("Initializing treatment head data on application startup...");
        
        try {
            // 延迟启动，确保其他服务已经初始化完成
            Thread.sleep(startupDelay);
            
            // 执行初始同步
            SyncResult result = performSync();
            
            if (result.isSuccess()) {
                logger.info("Treatment head initialization completed successfully: {} heads processed", 
                           result.getTotalHeads());
            } else {
                logger.warn("Treatment head initialization failed: {}", result.getErrorMessage());
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Treatment head initialization was interrupted");
        } catch (Exception e) {
            logger.error("Unexpected error during treatment head initialization", e);
        }
    }
    
    /**
     * 定时同步任务
     * 每隔配置的间隔时间执行一次同步
     */
    @Scheduled(fixedDelayString = "${treatment-head.sync.interval:10000}")
    public void scheduledSync() {
        if (!syncEnabled) {
            return;
        }
        
        if (isRunning.get()) {
            logger.debug("Previous sync is still running, skipping this cycle");
            return;
        }
        
        logger.debug("Starting scheduled treatment head sync...");
        
        try {
            SyncResult result = performSync();
            
            if (result.isSuccess()) {
                logger.debug("Scheduled sync completed successfully: {} heads processed in {}ms", 
                           result.getTotalHeads(), result.getExecutionTimeMs());
            } else {
                logger.warn("Scheduled sync failed: {}", result.getErrorMessage());
            }
            
        } catch (Exception e) {
            logger.error("Unexpected error during scheduled sync", e);
            updateSyncStatus(false, "Unexpected error: " + e.getMessage(), 0, 0, 0);
        }
    }
    
    /**
     * 执行同步操作
     * 
     * @return 同步结果
     */
    public SyncResult performSync() {
        if (!isRunning.compareAndSet(false, true)) {
            return SyncResult.failure("Sync is already running");
        }
        
        long startTime = System.currentTimeMillis();
        totalSyncCount.incrementAndGet();
        
        try {
            logger.debug("Starting treatment head sync...");
            
            // 调用硬件服务同步治疗头数据
            List<TreatmentHeadInfo> syncedHeads = hardwareService.syncAllTreatmentHeads();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 更新同步状态
            updateSyncStatus(true, null, syncedHeads.size(), syncedHeads.size(), 0);
            successfulSyncCount.incrementAndGet();
            
            logger.debug("Treatment head sync completed successfully: {} heads in {}ms", 
                        syncedHeads.size(), executionTime);
            
            return SyncResult.success(syncedHeads, executionTime);
            
        } catch (SerialCommunicationException e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMessage = "Hardware communication failed: " + e.getMessage();
            
            updateSyncStatus(false, errorMessage, 0, 0, 0);
            failedSyncCount.incrementAndGet();
            
            logger.warn("Treatment head sync failed: {}", errorMessage);
            return SyncResult.failure(errorMessage);
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMessage = "Unexpected error: " + e.getMessage();
            
            updateSyncStatus(false, errorMessage, 0, 0, 0);
            failedSyncCount.incrementAndGet();
            
            logger.error("Treatment head sync failed with unexpected error", e);
            return SyncResult.failure(errorMessage);
            
        } finally {
            isRunning.set(false);
        }
    }
    
    /**
     * 获取同步状态
     * 
     * @return 当前同步状态
     */
    public SyncStatus getSyncStatus() {
        return new SyncStatus(
            lastSyncTime,
            lastSyncSuccess,
            totalSyncCount.get(),
            successfulSyncCount.get(),
            failedSyncCount.get(),
            calculateAverageExecutionTime(),
            getCurrentStatus(),
            lastErrorMessage
        );
    }
    
    /**
     * 手动触发同步
     * 
     * @return 同步结果
     */
    public SyncResult triggerManualSync() {
        logger.info("Manual sync triggered");
        return performSync();
    }
    
    /**
     * 应用关闭时的清理工作
     */
    @PreDestroy
    public void shutdown() {
        logger.info("Treatment head sync service is shutting down...");
        
        // 等待当前同步完成
        int maxWaitSeconds = 10;
        int waitCount = 0;
        
        while (isRunning.get() && waitCount < maxWaitSeconds) {
            try {
                Thread.sleep(1000);
                waitCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        if (isRunning.get()) {
            logger.warn("Sync service shutdown timeout, forcing stop");
        } else {
            logger.info("Treatment head sync service shutdown completed");
        }
    }
    
    /**
     * 更新同步状态
     */
    private void updateSyncStatus(boolean success, String errorMessage, 
                                 int totalHeads, int updatedRecords, int createdRecords) {
        this.lastSyncTime = LocalDateTime.now();
        this.lastSyncSuccess = success;
        this.lastErrorMessage = errorMessage;
        this.lastExecutionTimeMs = System.currentTimeMillis();
    }
    
    /**
     * 计算平均执行时间
     */
    private double calculateAverageExecutionTime() {
        // 简化实现，后续可以维护执行时间历史记录
        return lastExecutionTimeMs;
    }
    
    /**
     * 获取当前状态
     */
    private String getCurrentStatus() {
        if (isRunning.get()) {
            return "RUNNING";
        } else if (!syncEnabled) {
            return "DISABLED";
        } else if (lastSyncSuccess) {
            return "IDLE";
        } else {
            return "ERROR";
        }
    }
    
    // Getter方法用于配置查询
    public boolean isSyncEnabled() {
        return syncEnabled;
    }
    
    public long getSyncInterval() {
        return syncInterval;
    }
    
    public long getStartupDelay() {
        return startupDelay;
    }
}