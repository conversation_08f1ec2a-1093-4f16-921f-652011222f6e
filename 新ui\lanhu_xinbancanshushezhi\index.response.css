.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.block_1 {
  height: 56.25vw;
  background: url(./img/3d029cda48755aa691231fbb520c6188.png)
    0vw -0.06vw no-repeat;
  background-size: 99.94vw 56.3vw;
  margin-left: 0.06vw;
  width: 99.95vw;
}

.group_1 {
  width: 52.24vw;
  height: 3.65vw;
  margin: 0.98vw 0 0 5.36vw;
}

.image_1 {
  width: 8.81vw;
  height: 3.65vw;
}

.text_1 {
  width: 15.32vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.53vw;
}

.group_2 {
  width: 100vw;
  height: 47.14vw;
  margin: 4.42vw 0 0.05vw -0.05vw;
}

.group_3 {
  width: 23.44vw;
  height: 46.52vw;
  background: url(./img/15c8a1c74e2a4fbc4a43617cc6e49eae.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.63vw;
}

.image-wrapper_1 {
  width: 9.54vw;
  height: 32.77vw;
  background: url(./img/8f83464412e369b3a9420e2ac275dc75.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 4.11vw 0 0 2.55vw;
}

.label_1 {
  width: 2.19vw;
  height: 2.19vw;
  margin: 5.31vw 0 0 3.69vw;
}

.label_2 {
  width: 2.19vw;
  height: 2.19vw;
  margin: 2.55vw 0 0 3.69vw;
}

.label_3 {
  width: 2.19vw;
  height: 2.19vw;
  margin: 4.47vw 0 13.85vw 2.03vw;
}

.text_2 {
  width: 2.97vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 21.71vw 3.75vw 0 4.63vw;
}

.group_4 {
  width: 53.55vw;
  height: 44.48vw;
  margin-left: 0.27vw;
}

.group_5 {
  height: 39.38vw;
  background: url(./img/a3a10ec9dc5a8ce5937f0394d3819db4.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 53.55vw;
}

.text-wrapper_1 {
  width: 45.42vw;
  height: 1.31vw;
  margin: 3.48vw 0 0 3.64vw;
}

.text_3 {
  width: 2.66vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
}

.text_4 {
  width: 2.71vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 3.91vw;
}

.text_5 {
  width: 2.56vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 5.32vw;
}

.text_6 {
  width: 2.61vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 4.33vw;
}

.text_7 {
  width: 5.42vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 5.42vw;
}

.text_8 {
  width: 5.42vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 5.11vw;
}

.block_2 {
  width: 43.75vw;
  height: 2.71vw;
  margin: 3.07vw 0 0 4.01vw;
}

.image_2 {
  width: 2.71vw;
  height: 2.71vw;
}

.text_9 {
  width: 4.22vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.67vw 0 0 2.7vw;
}

.text_10 {
  width: 3.86vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.88vw 0 0 3.8vw;
}

.text_11 {
  width: 5.32vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.98vw 0 0 2.5vw;
}

.text_12 {
  width: 4.74vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.98vw 0 0 4.58vw;
}

.text-wrapper_2 {
  width: 3.65vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.93vw 0 0 5.67vw;
}

.text_13 {
  width: 3.65vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.35vw;
}

.text_14 {
  width: 3.65vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: TimesNewRomanPSMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.35vw;
}

.block_3 {
  width: 46.83vw;
  height: 17.14vw;
  margin: 2.39vw 0 0 4.01vw;
}

.image-wrapper_2 {
  width: 2.77vw;
  height: 17.14vw;
}

.image_3 {
  width: 2.77vw;
  height: 2.77vw;
}

.image_4 {
  width: 2.71vw;
  height: 2.71vw;
  margin-top: 1.93vw;
}

.image_5 {
  width: 2.71vw;
  height: 2.71vw;
  margin-top: 2.19vw;
}

.image_6 {
  width: 2.71vw;
  height: 2.71vw;
  margin-top: 2.14vw;
}

.text-wrapper_3 {
  width: 4.22vw;
  height: 15.79vw;
  margin: 0.72vw 0 0 2.65vw;
}

.text_15 {
  width: 4.22vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
}

.text_16 {
  width: 2.77vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 3.43vw 0 0 0.72vw;
}

.text_17 {
  width: 2.77vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 3.54vw 0 0 0.72vw;
}

.text_18 {
  width: 2.77vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 3.59vw 0 0 0.72vw;
}

.text_19 {
  width: 3.86vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.72vw 0 0 3.8vw;
}

.section_1 {
  width: 8.13vw;
  height: 10vw;
  margin: 0.83vw 0 0 0.93vw;
}

.text_20 {
  width: 5.32vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin-left: 1.57vw;
}

.box_1 {
  width: 8.13vw;
  height: 8.23vw;
  background: url(./img/2e95215ed0f0444f301ee0b62616fd5b.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.53vw;
}

.text-wrapper_4 {
  height: 1.98vw;
  background: url(./img/ae050ead14bd1bf8b5d31ccccfada9a3.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 5.99vw;
  margin: 0.52vw 0 0 1.71vw;
}

.text_21 {
  width: 5.32vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.46vw 0 0 -0.15vw;
}

.text_22 {
  width: 5.42vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.78vw 0 0 1.45vw;
}

.text_23 {
  width: 5.37vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.98vw 0 1.51vw 1.51vw;
}

.section_2 {
  width: 8.29vw;
  height: 7.71vw;
  margin: 0.83vw 0 0 1.3vw;
}

.text_24 {
  width: 4.74vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin-left: 2.04vw;
}

.group_6 {
  width: 8.29vw;
  height: 5.99vw;
  background: url(./img/ba72f0579c9ab501b8a3f49b8cf492bb.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.63vw;
}

.image-text_1 {
  width: 5.99vw;
  height: 3.91vw;
  margin: 0.57vw 0 0 1.14vw;
}

.text-wrapper_5 {
  height: 1.98vw;
  background: url(./img/4f0df8afb90136f25779fb6cba455acc.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 5.99vw;
}

.text_25 {
  width: 3.96vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.46vw 0 0 1.3vw;
}

.text-group_1 {
  width: 4.74vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin: 0.83vw 0 0 0.88vw;
}

.section_3 {
  width: 10vw;
  height: 14.02vw;
  margin: 0.78vw 0 0 0.88vw;
}

.text-wrapper_6 {
  width: 3.65vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.35vw;
  margin-left: 3.29vw;
}

.text_26 {
  width: 3.65vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.35vw;
}

.text_27 {
  width: 3.65vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: TimesNewRomanPSMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.35vw;
}

.box_2 {
  height: 12.24vw;
  background: url(./img/4524106bc6ce83a638427fc2024cec09.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.53vw;
  width: 10vw;
}

.text-wrapper_7 {
  width: 6.83vw;
  height: 1.25vw;
  margin: 0.93vw 0 0 1.61vw;
}

.text_28 {
  width: 2.56vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
}

.text_29 {
  width: 2.56vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
}

.group_7 {
  width: 6.93vw;
  height: 1.2vw;
  margin: 1.19vw 0 0 1.25vw;
}

.image-text_2 {
  width: 2.97vw;
  height: 1.2vw;
}

.label_4 {
  width: 1.1vw;
  height: 1.1vw;
}

.text-group_2 {
  width: 1.25vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
}

.label_5 {
  width: 1.1vw;
  height: 1.1vw;
  margin-left: 1.72vw;
}

.text_30 {
  width: 0.42vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
  margin: 0.05vw 0 0 0.72vw;
}

.group_8 {
  width: 2.4vw;
  height: 1.1vw;
  margin: 0.67vw 0 0 5.93vw;
}

.label_6 {
  width: 1.1vw;
  height: 1.1vw;
}

.text_31 {
  width: 0.63vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
  margin-top: 0.06vw;
}

.group_9 {
  width: 7.09vw;
  height: 1.62vw;
  margin: 0.31vw 0 0 1.25vw;
}

.image-text_3 {
  width: 2.97vw;
  height: 1.25vw;
}

.label_7 {
  width: 1.1vw;
  height: 1.1vw;
  margin-top: 0.11vw;
}

.text-group_3 {
  width: 1.2vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
}

.label_8 {
  width: 1.1vw;
  height: 1.1vw;
  margin: 0.52vw 0 0 1.71vw;
}

.text_32 {
  width: 0.63vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.3vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
  margin: 0.57vw 0 0 0.67vw;
}

.group_10 {
  width: 2.45vw;
  height: 1.1vw;
  margin: 0.88vw 0 1.97vw 5.93vw;
}

.label_9 {
  width: 1.1vw;
  height: 1.1vw;
}

.text_33 {
  width: 0.73vw;
  height: 0.99vw;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 1.25vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 3.44vw;
  margin-top: 0.06vw;
}

.block_4 {
  width: 10.37vw;
  height: 2.71vw;
  margin: 2.55vw 0 4.01vw 4.01vw;
}

.image_7 {
  width: 2.71vw;
  height: 2.71vw;
}

.text_34 {
  width: 5.68vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.89vw;
}

.group_11 {
  width: 20vw;
  height: 5.11vw;
  margin-left: 16.36vw;
}

.text-wrapper_8 {
  height: 5.11vw;
  background: url(./img/67fb60a08c6e6c8a7895e55b353be4ee.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 14.48vw;
}

.text_35 {
  width: 10.42vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.66vw 0 0 1.92vw;
}

.image_8 {
  width: 5.53vw;
  height: 5.11vw;
}

.group_12 {
  width: 23.29vw;
  height: 46.52vw;
  background: url(./img/b77a209b7c30be85df4005a3907944c6.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 0.62vw 0 0 -0.52vw;
}

.text_36 {
  width: 2.97vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 21.66vw 0 0 3.9vw;
}

.image_9 {
  width: 9.54vw;
  height: 32.77vw;
  margin: 4.11vw 0.72vw 0 6.14vw;
}
