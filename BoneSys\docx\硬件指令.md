主板和控制板之间使用串口通信，波特率115200，停止位1，数据位8，校验位None
发送接收数据类型为 char
1.主板查询所有在盒子里面的治疗头数据
    主板串口发送：TRZI\r\n
    控制板返回：TRZI+治疗头数量(2 char)+((治疗头编号(2char)+治疗头电量(2char)+治疗次数(3char)+槽编号（2char))*治疗头数量\r\n

选择
2.主板点亮推荐治疗头指示灯
    主板串口发送：TWSC+治疗头数量(2 char)+(治疗头编号+颜色 3char)*治疗头数量\r\n
     控制板返回：  TWSC+治疗头数量(2 char)+(治疗头编号+颜色+槽编号)(5char)*治疗头数量\r\n

3.主板关闭推荐治疗头指示灯
      主板串口发送：TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n
       控制板返回：  TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n  

 4.主板向某个治疗头发送治疗参数（串口）
    主板串口发送： TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n     //重复周期标记 0---100Hz  1---1000Hz
     控制板返回：    TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n

 5.主板向某个治疗头发送治疗参数并工作
    主板串口发送： TWS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)\r\n  //重复周期标记 0---100Hz  1---1000Hz
     控制板返回：   TWS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)\r\n
    
6.关闭治疗头指令：
    串口发送：TWZO+治疗头编号(2char)+\r\n  返回数据：TWZO+治疗头编号(2char)+\r\n