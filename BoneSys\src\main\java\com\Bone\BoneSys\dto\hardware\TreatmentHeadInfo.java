package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗头信息DTO
 * 用于封装从硬件查询到的治疗头数据
 */
public class TreatmentHeadInfo {
    
    private int headNumber;      // 治疗头编号
    private int batteryLevel;    // 电量百分比
    private int usageCount;      // 使用次数
    private int slotNumber;      // 槽编号
    private String status;       // 状态
    private String compartmentType; // 仓位类型
    
    public TreatmentHeadInfo() {}
    
    public TreatmentHeadInfo(int headNumber, int batteryLevel, String status, String compartmentType) {
        this.headNumber = headNumber;
        this.batteryLevel = batteryLevel;
        this.status = status;
        this.compartmentType = compartmentType;
        this.slotNumber = headNumber; // 默认槽编号等于治疗头编号
        this.usageCount = 0; // 默认使用次数为0
    }
    
    public TreatmentHeadInfo(int headNumber, int batteryLevel, int usageCount, int slotNumber) {
        this.headNumber = headNumber;
        this.batteryLevel = batteryLevel;
        this.usageCount = usageCount;
        this.slotNumber = slotNumber;
        // 根据电量设置状态
        if (batteryLevel == 100) {
            this.status = "CHARGED";
        } else if (batteryLevel >= 60) {
            this.status = "CHARGING";
        } else {
            this.status = "LOW_BATTERY";
        }
        this.compartmentType = headNumber <= 10 ? "SHALLOW" : "DEEP";
    }
    
    // Getters and Setters
    public int getHeadNumber() {
        return headNumber;
    }
    
    public void setHeadNumber(int headNumber) {
        this.headNumber = headNumber;
    }
    
    public int getBatteryLevel() {
        return batteryLevel;
    }
    
    public void setBatteryLevel(int batteryLevel) {
        this.batteryLevel = batteryLevel;
    }
    
    public int getUsageCount() {
        return usageCount;
    }
    
    public void setUsageCount(int usageCount) {
        this.usageCount = usageCount;
    }
    
    public int getSlotNumber() {
        return slotNumber;
    }
    
    public void setSlotNumber(int slotNumber) {
        this.slotNumber = slotNumber;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getCompartmentType() {
        return compartmentType;
    }
    
    public void setCompartmentType(String compartmentType) {
        this.compartmentType = compartmentType;
    }
    
    @Override
    public String toString() {
        return String.format("TreatmentHeadInfo{headNumber=%d, batteryLevel=%d%%, usageCount=%d, slotNumber=%d, status='%s', compartmentType='%s'}", 
                           headNumber, batteryLevel, usageCount, slotNumber, status, compartmentType);
    }
}