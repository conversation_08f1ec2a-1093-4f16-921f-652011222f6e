.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.group_1 {
  width: 100vw;
  height: 56.25vw;
  background: url(./img/729506f7b0e9c8ed38af1c322c6e9cba.png) -0.06vw
    0vw no-repeat;
  background-size: 100.05vw 56.25vw;
}

.section_1 {
  width: 51.98vw;
  height: 3.65vw;
  margin: 0.93vw 0 0 5.41vw;
}

.image_1 {
  width: 8.81vw;
  height: 3.65vw;
}

.text_1 {
  width: 15.32vw;
  height: 2.56vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.42vw;
}

.text-wrapper_1 {
  height: 4.95vw;
  background: url(./img/f76b271dd10ae46f4ad862f0468cb5e2.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 41.15vw;
  margin: 2.39vw 0 0 6.77vw;
}

.text_2 {
  width: 4.59vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.78vw 0 0 34.11vw;
}

.block_2 {
  position: relative;
  width: 87.97vw;
  height: 43.6vw;
  background: url(./img/b7f4af218e44f0d3b8bc57a569417bba.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 0 0 0.72vw 5.83vw;
}

.text-wrapper_6 {
  width: 71.25vw;
  height: 1.78vw;
  margin: 3.02vw 0 0 8.75vw;
}

.text_3 {
  width: 8.13vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_4 {
  width: 3.7vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 5.84vw;
}

.text_5 {
  width: 3.86vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 4.9vw;
}

.text_6 {
  width: 3.81vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 3.18vw;
}

.text_7 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 9.28vw;
}

.text_8 {
  width: 8.18vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 4.22vw;
}

.text_9 {
  width: 3.91vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 4.28vw;
}

.box_1 {
  position: relative;
  width: 76.88vw;
  height: 25.94vw;
  margin: 0.83vw 0 0 6.04vw;
}

.text_10 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 19.63vw 0 0 2.86vw;
}

.text_11 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 15.93vw 0 0 -7.91vw;
}

.text_12 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 12.18vw 0 0 -7.91vw;
}

.text_13 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 8.59vw 0 0 -7.91vw;
}

.text_14 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 4.94vw 0 0 -7.91vw;
}

.text_15 {
  width: 7.92vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.09vw 0 0 -7.91vw;
}

.text_16 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 19.53vw 0 0 3.8vw;
}

.text_17 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 15.83vw 0 0 -8.02vw;
}

.text_18 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 12.08vw 0 0 -8.02vw;
}

.text_19 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 8.48vw 0 0 -8.02vw;
}

.text_20 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 4.84vw 0 0 -8.02vw;
}

.text_21 {
  width: 8.03vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 -8.02vw;
}

.text_22 {
  width: 2.3vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 19.63vw 0 0 3.43vw;
}

.text_23 {
  width: 2.3vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 15.93vw 0 0 -2.29vw;
}

.text_24 {
  width: 2.3vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 12.18vw 0 0 -2.29vw;
}

.text_25 {
  width: 2.3vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 8.59vw 0 0 -2.29vw;
}

.text_26 {
  width: 2.3vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 4.94vw 0 0 -2.29vw;
}

.text_27 {
  width: 2.3vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.09vw 0 0 -2.29vw;
}

.text_28 {
  width: 1.67vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 19.63vw 0 0 5.26vw;
}

.text_29 {
  width: 1.67vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 15.93vw 0 0 -1.66vw;
}

.text_30 {
  width: 1.67vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 12.18vw 0 0 -1.66vw;
}

.text_31 {
  width: 1.67vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 8.59vw 0 0 -1.66vw;
}

.text_32 {
  width: 1.67vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 4.94vw 0 0 -1.66vw;
}

.text_33 {
  width: 1.67vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.09vw 0 0 -1.66vw;
}

.text_34 {
  width: 10.99vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 19.63vw 0 0 8.64vw;
}

.text_35 {
  width: 10.99vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 15.93vw 0 0 -10.98vw;
}

.text_36 {
  width: 10.99vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 12.18vw 0 0 -10.98vw;
}

.text_37 {
  width: 10.99vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 8.59vw 0 0 -10.98vw;
}

.text_38 {
  width: 10.99vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 4.94vw 0 0 -10.98vw;
}

.text_39 {
  width: 10.99vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.09vw 0 0 -10.98vw;
}

.text_40 {
  width: 8.18vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 19.53vw 0 0 2.7vw;
}

.text_41 {
  width: 5.99vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 4.79vw 0 0 -7.08vw;
}

.text_42 {
  width: 5.99vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.98vw 0 0 -5.98vw;
}

.text_43 {
  width: 3.86vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 15.83vw 0 0 -4.89vw;
}

.text_44 {
  width: 3.86vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 12.03vw 0 0 -3.85vw;
}

.text_45 {
  width: 3.86vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 8.43vw 0 0 -3.85vw;
}

.text_46 {
  width: 0.79vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 19.63vw 0 0 7.96vw;
}

.text_47 {
  width: 0.79vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 15.93vw 0 0 -0.78vw;
}

.text_48 {
  width: 0.79vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 12.18vw 0 0 -0.78vw;
}

.text_49 {
  width: 0.79vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 8.59vw 0 0 -0.78vw;
}

.text_50 {
  width: 0.79vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 4.94vw 0 0 -0.78vw;
}

.text_51 {
  width: 0.79vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.09vw 4.47vw 0 -0.78vw;
}

.image_2 {
  position: absolute;
  left: 0;
  top: 0;
  width: 76.88vw;
  height: 25.94vw;
}

.box_2 {
  width: 62.19vw;
  height: 5.37vw;
  margin: 1.19vw 0 5.46vw 22.5vw;
}

.image_3 {
  width: 10.94vw;
  height: 3.7vw;
  margin-top: 1.67vw;
}

.image_4 {
  width: 10.94vw;
  height: 3.7vw;
  margin: 1.66vw 0 0 21.3vw;
}

.image-wrapper_1 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 2.97vw;
  border: 1px solid rgba(238, 238, 238, 0.63);
  margin-left: 5.47vw;
  width: 13.55vw;
}

.image_5 {
  width: 12.24vw;
  height: 1.98vw;
  margin: 0.46vw 0 0 0.52vw;
}

.text-wrapper_3 {
  height: 4.02vw;
  background: url(./img/ee2226d2bc022e95a4140f84b614e115.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 11.25vw;
  position: absolute;
  left: 22.35vw;
  top: 34.28vw;
}

.text_52 {
  width: 4.59vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.09vw 0 0 3.43vw;
}

.text-wrapper_4 {
  height: 4.02vw;
  background: url(./img/1c8e5eddae0e5143f83eda86a9623880.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 11.25vw;
  position: absolute;
  left: 54.59vw;
  top: 34.28vw;
}

.text_53 {
  width: 9.02vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 0 0 1.14vw;
}
