package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.AuthService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {
    private final AuthService authService;

    @Autowired
    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request) {
        try {
            AuthService.LoginResult result = authService.login(request.getPassword());
            return ApiResponse.success("登录成功", new LoginResponse(result.getToken(), result.getMatchedType()));
        } catch (Exception e) {
            return ApiResponse.error(401, e.getMessage());
        }
    }

    @PostMapping("/change-password")
    public ApiResponse<Void> changePassword(@RequestBody ChangePasswordRequest request) {
        try {
            authService.changeUserPassword(request.getOldPassword(), request.getNewPassword());
            return ApiResponse.success("密码修改成功");
        } catch (Exception e) {
            return ApiResponse.error(401, e.getMessage());
        }
    }

    @Data
    public static class LoginRequest {
        private String password;
    }

    @Data
    public static class LoginResponse {
        private final String token;
        private final String matchedType;
        
        public LoginResponse(String token, String matchedType) {
            this.token = token;
            this.matchedType = matchedType;
        }
        
        public String getToken() { return token; }
        public String getMatchedType() { return matchedType; }
    }

    @Data
    public static class ChangePasswordRequest {
        private String oldPassword;
        private String newPassword;
    }
}