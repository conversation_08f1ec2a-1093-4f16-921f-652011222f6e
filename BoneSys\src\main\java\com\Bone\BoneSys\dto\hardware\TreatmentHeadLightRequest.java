package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗头指示灯请求DTO
 * 用于请求点亮特定治疗头的指示灯
 */
public class TreatmentHeadLightRequest {
    
    private int headNumber;  // 治疗头编号
    private int colorCode;   // 颜色代码：0=关闭, 1=橙色, 2=蓝色, 3=绿色
    
    public TreatmentHeadLightRequest() {}
    
    public TreatmentHeadLightRequest(int headNumber, int colorCode) {
        this.headNumber = headNumber;
        this.colorCode = colorCode;
    }
    
    // Getters and Setters
    public int getHeadNumber() {
        return headNumber;
    }
    
    public void setHeadNumber(int headNumber) {
        this.headNumber = headNumber;
    }
    
    public int getColorCode() {
        return colorCode;
    }
    
    public void setColorCode(int colorCode) {
        this.colorCode = colorCode;
    }
    
    /**
     * 获取颜色描述
     */
    public String getColorDescription() {
        switch (colorCode) {
            case 0: return "关闭";
            case 1: return "橙色";
            case 2: return "蓝色";
            case 3: return "绿色";
            default: return "未知";
        }
    }
    
    @Override
    public String toString() {
        return String.format("TreatmentHeadLightRequest{headNumber=%d, colorCode=%d(%s)}", 
                           headNumber, colorCode, getColorDescription());
    }
}