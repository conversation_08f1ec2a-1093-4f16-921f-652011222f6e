package com.Bone.BoneSys.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * 数据库索引管理服务
 * 负责在应用启动时检查和创建必要的数据库索引
 */
@Service
@Slf4j
public class DatabaseIndexService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 应用启动完成后检查和创建索引
     */
    @EventListener(ApplicationReadyEvent.class)
    public void ensureIndexesExist() {
        log.info("开始检查和创建数据库索引...");
        
        try {
            createPatientIndexes();
            createRecordIndexes();
            createBodyPartStatIndexes();
            
            log.info("数据库索引检查和创建完成");
        } catch (Exception e) {
            log.error("创建数据库索引时发生错误", e);
        }
    }
    
    /**
     * 创建患者表相关索引
     */
    private void createPatientIndexes() {
        try {
            // 患者姓名索引
            createIndexIfNotExists("idx_patients_search_name", 
                "CREATE INDEX idx_patients_search_name ON patients(name)");
            
            // 患者就诊卡号索引
            createIndexIfNotExists("idx_patients_search_card_id", 
                "CREATE INDEX idx_patients_search_card_id ON patients(patient_card_id)");
            
            // 患者创建时间索引
            createIndexIfNotExists("idx_patients_created_at", 
                "CREATE INDEX idx_patients_created_at ON patients(created_at DESC)");
            
            log.info("患者表索引创建完成");
        } catch (Exception e) {
            log.error("创建患者表索引失败", e);
        }
    }
    
    /**
     * 创建档案表相关索引
     */
    private void createRecordIndexes() {
        try {
            // 档案患者ID和创建时间复合索引
            createIndexIfNotExists("idx_records_patient_created", 
                "CREATE INDEX idx_records_patient_created ON records(patient_id, created_at DESC)");
            
            // 档案编号索引
            createIndexIfNotExists("idx_records_record_number", 
                "CREATE INDEX idx_records_record_number ON records(record_number)");
            
            log.info("档案表索引创建完成");
        } catch (Exception e) {
            log.error("创建档案表索引失败", e);
        }
    }
    
    /**
     * 创建部位统计表相关索引
     */
    private void createBodyPartStatIndexes() {
        try {
            // 部位统计档案ID和部位复合索引
            createIndexIfNotExists("idx_body_part_stats_record", 
                "CREATE INDEX idx_body_part_stats_record ON body_part_stats(record_id, body_part)");
            
            // 部位统计使用次数索引
            createIndexIfNotExists("idx_body_part_stats_usage_count", 
                "CREATE INDEX idx_body_part_stats_usage_count ON body_part_stats(total_usage_count DESC)");
            
            // 部位名称索引
            createIndexIfNotExists("idx_body_part_stats_body_part", 
                "CREATE INDEX idx_body_part_stats_body_part ON body_part_stats(body_part)");
            
            // 患者查询优化复合索引
            createIndexIfNotExists("idx_body_part_stats_patient_lookup", 
                "CREATE INDEX idx_body_part_stats_patient_lookup ON body_part_stats(record_id, total_usage_count DESC)");
            
            log.info("部位统计表索引创建完成");
        } catch (Exception e) {
            log.error("创建部位统计表索引失败", e);
        }
    }
    
    /**
     * 检查索引是否存在，如果不存在则创建
     */
    private void createIndexIfNotExists(String indexName, String createSql) {
        try {
            // 检查索引是否存在（MySQL语法）
            String checkSql = "SELECT COUNT(*) FROM information_schema.statistics " +
                             "WHERE table_schema = DATABASE() AND index_name = ?";
            
            Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, indexName);
            
            if (count == null || count == 0) {
                jdbcTemplate.execute(createSql);
                log.info("索引 {} 创建成功", indexName);
            } else {
                log.debug("索引 {} 已存在，跳过创建", indexName);
            }
        } catch (Exception e) {
            log.warn("处理索引 {} 时发生错误: {}", indexName, e.getMessage());
        }
    }
    
    /**
     * 获取数据库索引统计信息
     */
    public void logIndexStatistics() {
        try {
            String sql = "SELECT table_name, index_name, non_unique, column_name " +
                        "FROM information_schema.statistics " +
                        "WHERE table_schema = DATABASE() " +
                        "AND table_name IN ('patients', 'records', 'body_part_stats') " +
                        "ORDER BY table_name, index_name, seq_in_index";
            
            jdbcTemplate.query(sql, (rs) -> {
                log.info("索引信息: 表={}, 索引={}, 列={}, 唯一={}", 
                        rs.getString("table_name"),
                        rs.getString("index_name"),
                        rs.getString("column_name"),
                        rs.getInt("non_unique") == 0 ? "是" : "否");
            });
        } catch (Exception e) {
            log.error("获取索引统计信息失败", e);
        }
    }
}