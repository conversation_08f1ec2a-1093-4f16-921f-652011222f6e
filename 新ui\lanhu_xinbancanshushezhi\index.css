.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.block_1 {
  height: 1080px;
  background: url(./img/3d029cda48755aa691231fbb520c6188.png)
    0px -1px no-repeat;
  background-size: 1919px 1081px;
  margin-left: 1px;
  width: 1919px;
}

.group_1 {
  width: 1003px;
  height: 70px;
  margin: 19px 0 0 103px;
}

.image_1 {
  width: 169px;
  height: 70px;
}

.text_1 {
  width: 294px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
}

.group_2 {
  width: 1920px;
  height: 905px;
  margin: 85px 0 1px -1px;
}

.group_3 {
  width: 450px;
  height: 893px;
  background: url(./img/15c8a1c74e2a4fbc4a43617cc6e49eae.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 12px;
}

.image-wrapper_1 {
  width: 183px;
  height: 629px;
  background: url(./img/8f83464412e369b3a9420e2ac275dc75.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 79px 0 0 49px;
}

.label_1 {
  width: 42px;
  height: 42px;
  margin: 102px 0 0 71px;
}

.label_2 {
  width: 42px;
  height: 42px;
  margin: 49px 0 0 71px;
}

.label_3 {
  width: 42px;
  height: 42px;
  margin: 86px 0 266px 39px;
}

.text_2 {
  width: 57px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin: 417px 72px 0 89px;
}

.group_4 {
  width: 1028px;
  height: 854px;
  margin-left: 5px;
}

.group_5 {
  height: 756px;
  background: url(./img/a3a10ec9dc5a8ce5937f0394d3819db4.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1028px;
}

.text-wrapper_1 {
  width: 872px;
  height: 25px;
  margin: 67px 0 0 70px;
}

.text_3 {
  width: 51px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
}

.text_4 {
  width: 52px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 75px;
}

.text_5 {
  width: 49px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 102px;
}

.text_6 {
  width: 50px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 83px;
}

.text_7 {
  width: 104px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 104px;
}

.text_8 {
  width: 104px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 98px;
}

.block_2 {
  width: 840px;
  height: 52px;
  margin: 59px 0 0 77px;
}

.image_2 {
  width: 52px;
  height: 52px;
}

.text_9 {
  width: 81px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 13px 0 0 52px;
}

.text_10 {
  width: 74px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 17px 0 0 73px;
}

.text_11 {
  width: 102px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 19px 0 0 48px;
}

.text_12 {
  width: 91px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 19px 0 0 88px;
}

.text-wrapper_2 {
  width: 70px;
  height: 24px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 18px 0 0 109px;
}

.text_13 {
  width: 70px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 45px;
}

.text_14 {
  width: 70px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: TimesNewRomanPSMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 45px;
}

.block_3 {
  width: 899px;
  height: 329px;
  margin: 46px 0 0 77px;
}

.image-wrapper_2 {
  width: 53px;
  height: 329px;
}

.image_3 {
  width: 53px;
  height: 53px;
}

.image_4 {
  width: 52px;
  height: 52px;
  margin-top: 37px;
}

.image_5 {
  width: 52px;
  height: 52px;
  margin-top: 42px;
}

.image_6 {
  width: 52px;
  height: 52px;
  margin-top: 41px;
}

.text-wrapper_3 {
  width: 81px;
  height: 303px;
  margin: 14px 0 0 51px;
}

.text_15 {
  width: 81px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
}

.text_16 {
  width: 53px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 66px 0 0 14px;
}

.text_17 {
  width: 53px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 68px 0 0 14px;
}

.text_18 {
  width: 53px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 69px 0 0 14px;
}

.text_19 {
  width: 74px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 14px 0 0 73px;
}

.section_1 {
  width: 156px;
  height: 192px;
  margin: 16px 0 0 18px;
}

.text_20 {
  width: 102px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin-left: 30px;
}

.box_1 {
  width: 156px;
  height: 158px;
  background: url(./img/2e95215ed0f0444f301ee0b62616fd5b.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 10px;
}

.text-wrapper_4 {
  height: 38px;
  background: url(./img/ae050ead14bd1bf8b5d31ccccfada9a3.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 115px;
  margin: 10px 0 0 33px;
}

.text_21 {
  width: 102px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 9px 0 0 -3px;
}

.text_22 {
  width: 104px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 15px 0 0 28px;
}

.text_23 {
  width: 103px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 19px 0 29px 29px;
}

.section_2 {
  width: 159px;
  height: 148px;
  margin: 16px 0 0 25px;
}

.text_24 {
  width: 91px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin-left: 39px;
}

.group_6 {
  width: 159px;
  height: 115px;
  background: url(./img/ba72f0579c9ab501b8a3f49b8cf492bb.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 12px;
}

.image-text_1 {
  width: 115px;
  height: 75px;
  margin: 11px 0 0 22px;
}

.text-wrapper_5 {
  height: 38px;
  background: url(./img/4f0df8afb90136f25779fb6cba455acc.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 115px;
}

.text_25 {
  width: 76px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 9px 0 0 25px;
}

.text-group_1 {
  width: 91px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin: 16px 0 0 17px;
}

.section_3 {
  width: 192px;
  height: 269px;
  margin: 15px 0 0 17px;
}

.text-wrapper_6 {
  width: 70px;
  height: 24px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 45px;
  margin-left: 63px;
}

.text_26 {
  width: 70px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 45px;
}

.text_27 {
  width: 70px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: TimesNewRomanPSMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 45px;
}

.box_2 {
  height: 235px;
  background: url(./img/4524106bc6ce83a638427fc2024cec09.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 10px;
  width: 192px;
}

.text-wrapper_7 {
  width: 131px;
  height: 24px;
  margin: 18px 0 0 31px;
}

.text_28 {
  width: 49px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
}

.text_29 {
  width: 49px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
}

.group_7 {
  width: 133px;
  height: 23px;
  margin: 23px 0 0 24px;
}

.image-text_2 {
  width: 57px;
  height: 23px;
}

.label_4 {
  width: 21px;
  height: 21px;
}

.text-group_2 {
  width: 24px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
}

.label_5 {
  width: 21px;
  height: 21px;
  margin-left: 33px;
}

.text_30 {
  width: 8px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
  margin: 1px 0 0 14px;
}

.group_8 {
  width: 46px;
  height: 21px;
  margin: 13px 0 0 114px;
}

.label_6 {
  width: 21px;
  height: 21px;
}

.text_31 {
  width: 12px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
  margin-top: 1px;
}

.group_9 {
  width: 136px;
  height: 31px;
  margin: 6px 0 0 24px;
}

.image-text_3 {
  width: 57px;
  height: 24px;
}

.label_7 {
  width: 21px;
  height: 21px;
  margin-top: 2px;
}

.text-group_3 {
  width: 23px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
}

.label_8 {
  width: 21px;
  height: 21px;
  margin: 10px 0 0 33px;
}

.text_32 {
  width: 12px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 25px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
  margin: 11px 0 0 13px;
}

.group_10 {
  width: 47px;
  height: 21px;
  margin: 17px 0 38px 114px;
}

.label_9 {
  width: 21px;
  height: 21px;
}

.text_33 {
  width: 14px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 24px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 66px;
  margin-top: 1px;
}

.block_4 {
  width: 199px;
  height: 52px;
  margin: 49px 0 77px 77px;
}

.image_7 {
  width: 52px;
  height: 52px;
}

.text_34 {
  width: 109px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 17px;
}

.group_11 {
  width: 384px;
  height: 98px;
  margin-left: 314px;
}

.text-wrapper_8 {
  height: 98px;
  background: url(./img/67fb60a08c6e6c8a7895e55b353be4ee.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 278px;
}

.text_35 {
  width: 200px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 32px 0 0 37px;
}

.image_8 {
  width: 106px;
  height: 98px;
}

.group_12 {
  width: 447px;
  height: 893px;
  background: url(./img/b77a209b7c30be85df4005a3907944c6.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 12px 0 0 -10px;
}

.text_36 {
  width: 57px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin: 416px 0 0 75px;
}

.image_9 {
  width: 183px;
  height: 629px;
  margin: 79px 14px 0 118px;
}
