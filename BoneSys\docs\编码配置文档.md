# 项目编码配置文档

## 概述

本文档详细说明了BoneSys项目中的字符编码配置，确保中文字符在整个系统中能够正确存储、传输和显示。

## 编码问题的根本原因

在修复过程中发现，中文字符显示为"??"的根本原因是：
1. **数据库连接URL缺少编码参数**
2. **HTTP请求/响应缺少编码声明**
3. **JPA/Hibernate缺少编码配置**

## 修复方案

### 1. 数据库连接编码配置

**文件位置**: `src/main/resources/application.properties`

**修复前**:
```properties
spring.datasource.url=************************************************************************************************
```

**修复后**:
```properties
spring.datasource.url=*******************************************************************************************************************************************************************************
```

**关键参数说明**:
- `useUnicode=true`: 启用Unicode支持
- `characterEncoding=UTF-8`: 设置字符编码为UTF-8
- `connectionCollation=utf8mb4_unicode_ci`: 设置连接排序规则

### 2. JPA/Hibernate编码配置

**文件位置**: `src/main/resources/application.properties`

**新增配置**:
```properties
spring.jpa.properties.hibernate.connection.characterEncoding=UTF-8
spring.jpa.properties.hibernate.connection.useUnicode=true
spring.jpa.properties.hibernate.connection.CharSet=UTF-8
```

### 3. Spring Boot Web编码配置

**文件位置**: `src/main/java/com/Bone/BoneSys/config/EncodingConfig.java`

**新增配置类**:
```java
@Configuration
public class EncodingConfig implements WebMvcConfigurer {

    @Bean
    public CharacterEncodingFilter characterEncodingFilter() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        filter.setEncoding("UTF-8");
        filter.setForceEncoding(true);
        filter.setForceRequestEncoding(true);
        filter.setForceResponseEncoding(true);
        return filter;
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setWriteAcceptCharset(false);
        converters.add(0, stringConverter);
    }
}
```

### 4. 前端编码配置

**文件位置**: `Bone_Vue/src/utils/axios.ts`

**修复前**:
```javascript
headers: {
  'Content-Type': 'application/json'
}
```

**修复后**:
```javascript
headers: {
  'Content-Type': 'application/json; charset=UTF-8',
  'Accept': 'application/json; charset=UTF-8'
}
```

**HTML页面编码**: `Bone_Vue/index.html`
```html
<meta charset="UTF-8">
```

## 数据库编码要求

### MySQL数据库配置
- **数据库字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **连接字符集**: `UTF-8`

### 验证数据库编码
```sql
-- 查看数据库字符集
SHOW VARIABLES LIKE 'character_set%';

-- 查看表字符集
SHOW TABLE STATUS FROM bonesys;

-- 查看列字符集
SHOW FULL COLUMNS FROM patients;
```

## 测试验证

### 1. 创建包含中文的测试数据
```bash
curl -X POST http://localhost:8080/api/patients \
  -H "Content-Type: application/json; charset=UTF-8" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "测试患者",
    "gender": "男",
    "age": "30",
    "phone": "13800138000",
    "cardId": "TEST001"
  }'
```

### 2. 验证数据正确性
- 检查数据库中的实际存储内容
- 验证API响应中的中文字符
- 确认前端页面显示正确

## 修复效果

### ✅ 已解决的问题
1. **新创建的患者姓名正确显示** - 如"赵六"能正确保存和显示
2. **HTTP响应编码正确** - 响应头包含`charset=UTF-8`
3. **就诊卡号正确显示** - 不再为空

### ⚠️ 遗留问题
1. **历史数据** - 修复前创建的数据仍可能显示为"??"
2. **数据聚合问题** - 治疗部位、次数等信息需要进一步修复（非编码问题）

## 最佳实践

### 1. 开发规范
- 所有文本文件使用UTF-8编码保存
- 数据库操作始终指定字符编码
- HTTP请求/响应明确声明编码

### 2. 测试规范
- 新功能必须包含中文字符测试
- 数据库迁移后验证编码配置
- API测试包含多语言字符

### 3. 部署规范
- 生产环境数据库编码配置检查
- 应用服务器编码环境变量设置
- 负载均衡器编码透传配置

## 故障排查

### 常见问题
1. **中文显示为"??"** - 检查数据库连接URL编码参数
2. **前端显示乱码** - 检查HTTP响应头编码声明
3. **数据保存失败** - 检查数据库表字符集配置

### 调试命令
```bash
# 检查数据库连接编码
mysql -u root -p -e "SHOW VARIABLES LIKE 'character_set%';"

# 检查HTTP响应编码
curl -I http://localhost:8080/api/patients

# 检查应用日志中的编码错误
tail -f logs/application.log | grep -i encoding
```

## 更新记录

- **2025-07-29**: 初始版本，修复中文字符编码问题
- **修复人员**: AI Assistant
- **测试状态**: 通过基本功能测试

---

**注意**: 本配置适用于MySQL 8.0+和Spring Boot 2.7+环境。其他版本可能需要调整配置参数。
