html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.group_1 {
  height: 28.747rem;
  background: url(./img/4da74a82a76211bac6221a1e0edabddc.png) -0.08rem -0.054rem
    no-repeat;
  background-size: 51.2rem 28.8rem;
  width: 51.12rem;
}

.box_1 {
  width: 51.2rem;
  height: 28.8rem;
  background: url(./img/c30b12fbf3037e890c790dcd36bdf001.png) -0.08rem -0.054rem
    no-repeat;
  background-size: 51.28rem 28.854rem;
}

.block_1 {
  width: 26.774rem;
  height: 1.867rem;
  margin: 0.48rem 0 0 2.667rem;
}

.image_1 {
  width: 4.507rem;
  height: 1.867rem;
}

.text_1 {
  width: 7.867rem;
  height: 1.307rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.267rem;
}

.block_2 {
  width: 43.387rem;
  height: 7.334rem;
  margin: 1.867rem 0 0 3.627rem;
}

.box_2 {
  width: 25.094rem;
  height: 7.28rem;
  background: url(./img/e974f01faef346da53cab0c398e50bea.png)
    0rem -0.027rem no-repeat;
  background-size: 25.12rem 7.307rem;
}

.block_3 {
  background-color: rgba(239, 239, 239, 1);
  border-radius: 50%;
  width: 4.134rem;
  height: 4.134rem;
  border: 1px solid rgba(203, 203, 203, 1);
  margin: 1.147rem 0 0 0.694rem;
}

.block_4 {
  width: 8.56rem;
  height: 4.16rem;
  margin: 0.614rem 0 0 0.64rem;
}

.box_3 {
  width: 8.56rem;
  height: 1.307rem;
}

.text_2 {
  width: 1.707rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-top: 0.374rem;
}

.box_4 {
  border-radius: 10px;
  width: 0.587rem;
  height: 1.307rem;
  margin-left: 0.027rem;
}

.text_3 {
  width: 1.707rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-top: 0.374rem;
}

.text_4 {
  width: 1.814rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.374rem 0 0 2.72rem;
}

.text-wrapper_1 {
  width: 8.454rem;
  height: 0.667rem;
  margin-top: 0.56rem;
}

.text_5 {
  width: 1.707rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
}

.text_6 {
  width: 1.254rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.027rem 0 0 0.587rem;
}

.text_7 {
  width: 1.68rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-left: 3.227rem;
}

.text-wrapper_2 {
  width: 6.854rem;
  height: 0.667rem;
  margin-top: 0.96rem;
}

.text_8 {
  width: 3.387rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
}

.text_9 {
  width: 2.88rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-top: 0.027rem;
}

.block_5 {
  width: 9.814rem;
  height: 5.867rem;
  margin: 0.987rem 0.72rem 0 0.534rem;
}

.text-wrapper_3 {
  width: 7.334rem;
  height: 0.667rem;
}

.text_10 {
  width: 0.614rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-top: 0.027rem;
}

.text_11 {
  width: 1.814rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin-left: 3.44rem;
}

.text_12 {
  width: 0.88rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.08rem 0 0 0.587rem;
}

.text_13 {
  width: 4.4rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.854rem 0 0 0.027rem;
}

.section_1 {
  height: 1.84rem;
  background: url(./img/8868f2acf37f3079fab139b3ff5da01f.png) -0.267rem
    0rem no-repeat;
  background-size: 6.72rem 2.454rem;
  width: 6.187rem;
  margin: 1.947rem 0 0 3.627rem;
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 1.707rem;
  width: 6.027rem;
  margin: 0.08rem 0 0 0.08rem;
}

.text_14 {
  width: 4.56rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.427rem 0 0 0.694rem;
}

.box_5 {
  height: 7.334rem;
  background: url(./img/e19c135acf78908ffc6870560b6ac03d.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 16.96rem;
}

.text-wrapper_5 {
  width: 12.48rem;
  height: 0.694rem;
  margin: 1.014rem 0 0 1.814rem;
}

.text_15 {
  width: 2.454rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.027rem;
}

.text_16 {
  width: 2.667rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.054rem 0 0 0.454rem;
}

.text_17 {
  width: 1.707rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 2.027rem;
}

.text_18 {
  width: 2.667rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.054rem 0 0 0.507rem;
}

.text-wrapper_6 {
  width: 12.48rem;
  height: 0.667rem;
  margin: 0.8rem 0 0 1.814rem;
}

.text_19 {
  width: 2.454rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
}

.text_20 {
  width: 2.667rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.027rem 0 0 0.454rem;
}

.text_21 {
  width: 1.707rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 2.027rem;
}

.text_22 {
  width: 2.667rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.027rem 0 0 0.507rem;
}

.text-wrapper_7 {
  width: 12.48rem;
  height: 0.774rem;
  margin: 0.8rem 0 0 1.814rem;
}

.text_23 {
  width: 3.2rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.107rem;
}

.text_24 {
  width: 2.214rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.187rem 0 0 0.614rem;
}

.text_25 {
  width: 1.707rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 1.6rem;
}

.text_26 {
  width: 2.667rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.027rem 0 0 0.48rem;
}

.group_2 {
  width: 6.187rem;
  height: 1.227rem;
  margin: 0.827rem 0 0.534rem 10.054rem;
}

.text-wrapper_8 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  height: 1.334rem;
  border: 2px solid rgba(245, 245, 245, 0.1);
  width: 6.294rem;
  margin: -0.054rem 0 0 -0.054rem;
}

.text_27 {
  width: 5.2rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.32rem 0 0 0.667rem;
}

.block_6 {
  position: relative;
  width: 45.04rem;
  height: 15.307rem;
  background: url(./img/89849cff18b90a27d9b139bda364e8ce.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 0.8rem 0 1.147rem 2.907rem;
}

.text-wrapper_9 {
  width: 38.507rem;
  height: 0.88rem;
  margin: 1.947rem 0 0 3.654rem;
}

.text_28 {
  width: 3.814rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_29 {
  width: 1.76rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 3.254rem;
}

.text_30 {
  width: 1.68rem;
  height: 0.854rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.027rem 0 0 3.147rem;
}

.text_31 {
  width: 3.814rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.48rem;
}

.text_32 {
  width: 3.814rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.374rem;
}

.text_33 {
  width: 4.774rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 1.76rem;
}

.text_34 {
  width: 3.787rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.054rem;
}

.image_2 {
  width: 39.36rem;
  height: 0.027rem;
  margin: 0.454rem 0 0 3.014rem;
}

.block_7 {
  width: 36.907rem;
  height: 1.094rem;
  margin: 0.427rem 0 0 4.4rem;
}

.text_35 {
  width: 2.4rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.32rem;
}

.text_36 {
  width: 2.854rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.294rem 0 0 3.414rem;
}

.text_37 {
  width: 2.8rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.32rem 0 0 1.947rem;
}

.text_38 {
  width: 2.134rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.294rem 0 0 2.8rem;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 1.094rem;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 2.56rem;
  width: 4.48rem;
}

.box_6 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  width: 3.12rem;
  height: 1.094rem;
  border: 0.5px solid rgba(191, 191, 191, 1);
  margin-left: 1.787rem;
}

.text_39 {
  width: 0.614rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.32rem 0 0 3.814rem;
}

.text_40 {
  width: 2.08rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.32rem 0 0 5.014rem;
}

.image_3 {
  width: 39.36rem;
  height: 0.027rem;
  margin: 0.347rem 0 0 3.014rem;
}

.block_8 {
  width: 36.907rem;
  height: 1.094rem;
  margin: 0.374rem 0 0 4.4rem;
}

.text_41 {
  width: 2.4rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.294rem;
}

.text_42 {
  width: 2.854rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 3.414rem;
}

.text_43 {
  width: 2.8rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.294rem 0 0 1.947rem;
}

.text_44 {
  width: 2.134rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 2.8rem;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 1.094rem;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 2.534rem;
  width: 4.48rem;
  position: relative;
}

.text_45 {
  width: 1.12rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 3.387rem;
  margin: 0.24rem 0 0 0.374rem;
}

.box_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  height: 1.094rem;
  border: 0.5px solid rgba(191, 191, 191, 1);
  width: 3.12rem;
  position: absolute;
  left: 1.787rem;
  top: 0;
}

.text-wrapper_10 {
  width: 2.72rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.72rem;
  margin: 0.214rem 0 0 0.214rem;
}

.text_46 {
  width: 2.72rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.72rem;
}

.text_47 {
  width: 2.72rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.72rem;
}

.text_48 {
  width: 0.614rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.294rem 0 0 3.84rem;
}

.text_49 {
  width: 2.08rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.294rem 0 0 5.014rem;
}

.image-wrapper_1 {
  height: 0.027rem;
  background: url(./img/3d94914d453f437746263c64a7c4d7d9.png)
    0rem -0.027rem no-repeat;
  background-size: 39.36rem 0.054rem;
  width: 39.36rem;
  margin: 0.4rem 0 0 3.014rem;
}

.image_4 {
  width: 39.36rem;
  height: 0.027rem;
}

.image-wrapper_2 {
  height: 0.027rem;
  background: url(./img/efc88efd533cf9bdf65716324b2cf8b9.png)
    0rem -0.027rem no-repeat;
  background-size: 39.36rem 0.054rem;
  width: 39.36rem;
  margin: 1.867rem 0 0 3.014rem;
}

.image_5 {
  width: 39.36rem;
  height: 0.027rem;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 1.52rem;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 6.934rem;
  margin: 1.947rem 0 2.854rem 36.347rem;
}

.image_6 {
  width: 6.267rem;
  height: 1.014rem;
  margin: 0.24rem 0 0 0.267rem;
}

.block_9 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 1.094rem;
  border: 0.5px solid rgba(159, 159, 160, 1);
  width: 4.48rem;
  position: absolute;
  left: 25.28rem;
  top: 3.76rem;
}

.text_50 {
  width: 1.12rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 3.387rem;
  margin: 0.24rem 0 0 0.374rem;
}

.section_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  height: 1.094rem;
  border: 0.5px solid rgba(191, 191, 191, 1);
  width: 3.12rem;
  position: absolute;
  left: 1.787rem;
  top: 0;
}

.text-wrapper_11 {
  width: 2.72rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.72rem;
  margin: 0.214rem 0 0 0.214rem;
}

.text_51 {
  width: 2.72rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.72rem;
}

.text_52 {
  width: 2.72rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 2.72rem;
}
