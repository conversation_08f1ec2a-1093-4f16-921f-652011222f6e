package com.Bone.BoneSys;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.service.HardwareSimulatorService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
    "hardware.simulator.enabled=true"
})
public class HardwareSimulatorTest {
    
    @Autowired(required = false)
    private HardwareSimulatorService hardwareSimulatorService;
    
    @Autowired
    private HardwareService hardwareService;
    
    @Test
    public void testHardwareSimulatorIsEnabled() {
        assertNotNull(hardwareSimulatorService, "Hardware simulator should be enabled");
        System.out.println("✅ Hardware simulator is properly enabled");
    }
    
    @Test
    public void testSimulatorGeneratesTRZIResponse() {
        assertNotNull(hardwareSimulatorService, "Hardware simulator should be enabled");
        
        String response = hardwareSimulatorService.simulateTRZIResponse();
        assertNotNull(response);
        assertTrue(response.startsWith("TRZI20"), "Response should start with TRZI20");
        System.out.println("✅ TRZI response generated: " + response.substring(0, Math.min(50, response.length())) + "...");
    }
    
    @Test
    public void testHardwareServiceSyncTreatmentHeads() throws Exception {
        List<TreatmentHeadInfo> heads = hardwareService.syncAllTreatmentHeads();
        
        assertNotNull(heads, "Treatment heads list should not be null");
        assertEquals(20, heads.size(), "Should have 20 treatment heads");
        
        System.out.println("✅ Synced " + heads.size() + " treatment heads");
        
        // 检查前几个治疗头的详细信息
        for (int i = 0; i < Math.min(5, heads.size()); i++) {
            TreatmentHeadInfo head = heads.get(i);
            System.out.println("Head " + (i+1) + ": " + head);
            
            assertTrue(head.getBatteryLevel() >= 70, "Battery level should be >= 70%");
            assertNotNull(head.getStatus(), "Status should not be null");
            assertTrue(head.getStatus().equals("CHARGED") || head.getStatus().equals("CHARGING"), 
                      "Status should be CHARGED or CHARGING, but was: " + head.getStatus());
        }
    }
}