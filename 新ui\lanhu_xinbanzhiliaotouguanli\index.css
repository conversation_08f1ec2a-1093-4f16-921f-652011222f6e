.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.image-wrapper_1 {
  height: 1080px;
  background: url(./img/a05eb3d383cf70e0da6d25beca0e335d.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 3px;
  width: 1917px;
}

.image_1 {
  width: 1910px;
  height: 993px;
  margin: 87px 0 0 7px;
}

.box_1 {
  height: 1080px;
  background: url(./img/a64bc209b8c81865ebcc45621fe19bbe.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1919px;
  position: absolute;
  left: 1px;
  top: 0;
}

.group_1 {
  width: 1044px;
  height: 70px;
  margin: 20px 0 0 105px;
}

.image_2 {
  width: 169px;
  height: 70px;
}

.text_1 {
  width: 376px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: Microsoft<PERSON>a<PERSON>ei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
}

.group_2 {
  position: relative;
  width: 1844px;
  height: 812px;
  margin: 98px 0 0 33px;
}

.box_2 {
  width: 1532px;
  height: 812px;
  background: url(./img/7165f76fa0b053e05201a568aa4fc31f.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 157px;
}

.text-wrapper_1 {
  width: 1350px;
  height: 33px;
  margin: 116px 0 0 129px;
}

.text_2 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text_3 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 163px;
}

.text_4 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 171px;
}

.text_5 {
  width: 121px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 96px;
}

.text_6 {
  width: 155px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 110px;
}

.text_7 {
  width: 197px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 112px;
}

.image_3 {
  width: 1382px;
  height: 1px;
  margin: 17px 0 0 84px;
}

.box_3 {
  width: 1258px;
  height: 42px;
  margin: 12px 0 0 144px;
}

.text_8 {
  width: 39px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 8px;
}

.image_4 {
  width: 58px;
  height: 33px;
  margin: 7px 0 0 84px;
}

.text_9 {
  width: 157px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 40px;
}

.text_10 {
  width: 16px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 122px;
}

.text_11 {
  width: 15px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 178px;
}

.text_12 {
  width: 107px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 187px;
}

.label_1 {
  width: 42px;
  height: 42px;
  margin-left: 213px;
}

.image_5 {
  width: 1382px;
  height: 1px;
  margin: 16px 0 0 84px;
}

.box_4 {
  width: 1258px;
  height: 45px;
  margin: 16px 0 0 144px;
}

.text_13 {
  width: 45px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 7px;
}

.image_6 {
  width: 57px;
  height: 45px;
  margin-left: 78px;
}

.text_14 {
  width: 113px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 5px 0 0 62px;
}

.text_15 {
  width: 19px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 143px;
}

.text_16 {
  width: 19px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 175px;
}

.text_17 {
  width: 107px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 185px;
}

.label_2 {
  width: 42px;
  height: 42px;
  margin-left: 213px;
}

.image-wrapper_2 {
  height: 1px;
  background: url(./img/5ff61b1d884414dd419a6d37f974583e.png)
    0px -1px no-repeat;
  background-size: 1382px 2px;
  width: 1382px;
  margin: 9px 0 0 84px;
}

.image_7 {
  width: 1382px;
  height: 1px;
}

.box_5 {
  width: 1258px;
  height: 42px;
  margin: 16px 0 0 144px;
}

.text_18 {
  width: 44px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 6px;
}

.label_3 {
  width: 44px;
  height: 40px;
  margin-left: 79px;
}

.text_19 {
  width: 113px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 4px 0 0 75px;
}

.text_20 {
  width: 10px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 5px 0 0 146px;
}

.text_21 {
  width: 10px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 5px 0 0 184px;
}

.text_22 {
  width: 107px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 191px;
}

.label_4 {
  width: 42px;
  height: 42px;
  margin-left: 213px;
}

.image-wrapper_3 {
  height: 1px;
  background: url(./img/372e95f51139b9c892d32fe3b3ae2b6e.png)
    0px -1px no-repeat;
  background-size: 1382px 2px;
  width: 1382px;
  margin: 12px 0 432px 84px;
}

.image_8 {
  width: 1382px;
  height: 1px;
}

.image_9 {
  width: 169px;
  height: 172px;
  margin: 285px 0 0 -14px;
}

.image_10 {
  position: absolute;
  left: 0;
  top: 285px;
  width: 169px;
  height: 172px;
}

.text-wrapper_2 {
  width: 61px;
  height: 26px;
  margin: 2px 0 52px 942px;
}

.text_23 {
  width: 61px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 27px;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 49px;
}
