package com.Bone.BoneSys.dto.hardware;

import java.util.List;

/**
 * 治疗参数检查请求DTO
 * 用于检查治疗头数量是否充足
 */
public class TreatmentParametersCheckRequest {
    
    private String patientId;
    private String treatmentMode; // "local" 或 "takeaway"
    private List<TreatmentBodyPart> bodyParts;
    
    public TreatmentParametersCheckRequest() {}
    
    public TreatmentParametersCheckRequest(String patientId, String treatmentMode, List<TreatmentBodyPart> bodyParts) {
        this.patientId = patientId;
        this.treatmentMode = treatmentMode;
        this.bodyParts = bodyParts;
    }
    
    // Getters and Setters
    public String getPatientId() {
        return patientId;
    }
    
    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }
    
    public String getTreatmentMode() {
        return treatmentMode;
    }
    
    public void setTreatmentMode(String treatmentMode) {
        this.treatmentMode = treatmentMode;
    }
    
    public List<TreatmentBodyPart> getBodyParts() {
        return bodyParts;
    }
    
    public void setBodyParts(List<TreatmentBodyPart> bodyParts) {
        this.bodyParts = bodyParts;
    }
    
    @Override
    public String toString() {
        return "TreatmentParametersCheckRequest{" +
                "patientId='" + patientId + '\'' +
                ", treatmentMode='" + treatmentMode + '\'' +
                ", bodyParts=" + bodyParts +
                '}';
    }
} 