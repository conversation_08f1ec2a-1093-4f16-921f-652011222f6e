.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.section_1 {
  height: 1080px;
  background: url(./img/7bdfa098a7611ed21e1cb7e88d68b916.png) -4px
    0px no-repeat;
  background-size: 1924px 1080px;
  width: 1920px;
}

.box_1 {
  height: 1080px;
  background: url(./img/2aff7163dfbe3a6e9f4de8fca2691414.png)
    0px -2px no-repeat;
  background-size: 1920px 1082px;
  width: 1920px;
}

.section_2 {
  box-shadow: 4px 7px 52px 1px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 16px;
  height: 949px;
  border: 3px solid rgba(92, 212, 200, 1);
  width: 1208px;
  position: relative;
  margin: 62px 0 0 331px;
}

.section_3 {
  width: 879px;
  height: 60px;
  margin: 33px 0 0 287px;
}

.text_1 {
  width: 87px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 23px;
}

.image_1 {
  width: 60px;
  height: 60px;
}

.text-wrapper_1 {
  width: 87px;
  height: 33px;
  margin: 745px 0 78px 287px;
}

.text_2 {
  width: 87px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
}

.section_4 {
  position: absolute;
  left: 139px;
  top: 90px;
  width: 942px;
  height: 752px;
}

.group_1 {
  width: 408px;
  height: 752px;
}

.box_2 {
  height: 367px;
  background: url(./img/5215423f6d2b18ec9a5b07b788a8728e.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 408px;
  position: relative;
}

.image-wrapper_1 {
  width: 273px;
  height: 18px;
  margin: 62px 0 0 55px;
}

.image_2 {
  width: 122px;
  height: 18px;
}

.image_3 {
  width: 122px;
  height: 18px;
}

.image-wrapper_2 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_4 {
  width: 122px;
  height: 18px;
}

.image_5 {
  width: 122px;
  height: 18px;
}

.image-wrapper_3 {
  width: 273px;
  height: 21px;
  margin: 32px 0 0 55px;
}

.image_6 {
  width: 122px;
  height: 18px;
  margin-top: 3px;
}

.image_7 {
  width: 122px;
  height: 18px;
}

.image-wrapper_4 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_8 {
  width: 122px;
  height: 18px;
}

.image_9 {
  width: 122px;
  height: 18px;
}

.image-wrapper_5 {
  width: 273px;
  height: 18px;
  margin: 32px 0 98px 55px;
}

.image_10 {
  width: 122px;
  height: 18px;
}

.image_11 {
  width: 122px;
  height: 18px;
}

.image-wrapper_6 {
  height: 50px;
  background: url(./img/81e3246c2326cb1d438edc9578f98305.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 122px;
  position: absolute;
  left: 207px;
  top: 29px;
}

.image_12 {
  width: 122px;
  height: 50px;
}

.image_13 {
  position: absolute;
  left: 207px;
  top: 123px;
  width: 122px;
  height: 50px;
}

.image-wrapper_7 {
  position: absolute;
  left: 56px;
  top: 30px;
  width: 122px;
  height: 50px;
  background: url(./img/4ae426b7f4961cc81bff173bfbbcd6da.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_14 {
  position: absolute;
  left: 0;
  top: 92px;
  width: 122px;
  height: 50px;
}

.image_15 {
  position: absolute;
  left: 1px;
  top: 43px;
  width: 122px;
  height: 50px;
}

.box_3 {
  height: 367px;
  background: url(./img/6abb35bb99c90ed63e3ed1dbe45b5ded.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 18px;
  width: 408px;
  position: relative;
}

.image-wrapper_8 {
  width: 273px;
  height: 18px;
  margin: 62px 0 0 55px;
}

.image_16 {
  width: 122px;
  height: 18px;
}

.image_17 {
  width: 122px;
  height: 18px;
}

.image-wrapper_9 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_18 {
  width: 122px;
  height: 18px;
}

.image_19 {
  width: 122px;
  height: 18px;
}

.image-wrapper_10 {
  width: 273px;
  height: 21px;
  margin: 32px 0 0 55px;
}

.image_20 {
  width: 122px;
  height: 18px;
  margin-top: 3px;
}

.image_21 {
  width: 122px;
  height: 18px;
}

.image-wrapper_11 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_22 {
  width: 122px;
  height: 18px;
}

.image_23 {
  width: 122px;
  height: 18px;
}

.image-wrapper_12 {
  width: 273px;
  height: 18px;
  margin: 32px 0 98px 55px;
}

.image_24 {
  width: 122px;
  height: 18px;
}

.image_25 {
  width: 122px;
  height: 18px;
}

.image-wrapper_13 {
  height: 50px;
  background: url(./img/3e814a658e50b0a482cf6c5bbc6420a8.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 122px;
  position: absolute;
  left: 207px;
  top: 29px;
}

.image_26 {
  width: 122px;
  height: 50px;
}

.image_27 {
  position: absolute;
  left: 207px;
  top: 123px;
  width: 122px;
  height: 50px;
}

.image-wrapper_14 {
  position: absolute;
  left: 56px;
  top: 30px;
  width: 122px;
  height: 50px;
  background: url(./img/b35026dfc0f1284d325209cd120f8037.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_28 {
  position: absolute;
  left: 0;
  top: 92px;
  width: 122px;
  height: 50px;
}

.image_29 {
  position: absolute;
  left: 1px;
  top: 43px;
  width: 122px;
  height: 50px;
}

.group_2 {
  width: 441px;
  height: 678px;
  margin-top: 10px;
}

.group_3 {
  width: 441px;
  height: 542px;
  background: url(./img/f96a7089da9677bf525fa05378d1e53b.png) -9px
    0px no-repeat;
  background-size: 460px 564px;
}

.block_1 {
  width: 298px;
  height: 63px;
  margin: 90px 0 0 70px;
}

.image_30 {
  width: 63px;
  height: 63px;
}

.text_3 {
  width: 205px;
  height: 41px;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 254px;
  margin-top: 11px;
}

.paragraph_1 {
  width: 387px;
  height: 151px;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 59px;
  margin: 157px 0 81px 33px;
}

.group_4 {
  height: 69px;
  background: url(./img/67906e3512ebfc34db8cc9692fcf7db6.png) -10px
    0px no-repeat;
  background-size: 253px 92px;
  width: 232px;
  margin: 67px 0 0 110px;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 3px 0 0 2px;
}

.text_4 {
  width: 93px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin: 16px 0 0 72px;
}
