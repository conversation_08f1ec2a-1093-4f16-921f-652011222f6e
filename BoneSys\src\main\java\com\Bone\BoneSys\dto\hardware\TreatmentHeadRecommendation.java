package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗头推荐信息
 */
public class TreatmentHeadRecommendation {
    
    private int headNumber; // 治疗头编号
    private int slotNumber; // 槽位编号
    private int batteryLevel; // 电量百分比
    private int usageCount; // 使用次数
    private String status; // 状态
    private int lightColor; // 指示灯颜色：1-橙色，2-蓝色，3-绿色
    private String lightColorName; // 指示灯颜色名称
    private int priority; // 推荐优先级（1最高）
    private String recommendationReason; // 推荐理由
    private String compartmentType; // 仓位类型
    private String targetBodyPart; // 目标身体部位
    
    // 治疗参数
    private int durationMinutes; // 治疗时长（分钟）
    private int intensity; // 治疗强度
    private int frequency; // 治疗频率
    
    public TreatmentHeadRecommendation() {}
    
    public TreatmentHeadRecommendation(int headNumber, int slotNumber, int batteryLevel, int usageCount, 
                                     String status, int lightColor, String lightColorName, int priority, 
                                     String recommendationReason, String compartmentType) {
        this.headNumber = headNumber;
        this.slotNumber = slotNumber;
        this.batteryLevel = batteryLevel;
        this.usageCount = usageCount;
        this.status = status;
        this.lightColor = lightColor;
        this.lightColorName = lightColorName;
        this.priority = priority;
        this.recommendationReason = recommendationReason;
        this.compartmentType = compartmentType;
    }
    
    public TreatmentHeadRecommendation(int headNumber, int slotNumber, int batteryLevel, int usageCount, 
                                     String status, int lightColor, String lightColorName, int priority, 
                                     String recommendationReason, String compartmentType, String targetBodyPart) {
        this.headNumber = headNumber;
        this.slotNumber = slotNumber;
        this.batteryLevel = batteryLevel;
        this.usageCount = usageCount;
        this.status = status;
        this.lightColor = lightColor;
        this.lightColorName = lightColorName;
        this.priority = priority;
        this.recommendationReason = recommendationReason;
        this.compartmentType = compartmentType;
        this.targetBodyPart = targetBodyPart;
    }
    
    // Getters and Setters
    public int getHeadNumber() {
        return headNumber;
    }
    
    public void setHeadNumber(int headNumber) {
        this.headNumber = headNumber;
    }
    
    public int getSlotNumber() {
        return slotNumber;
    }
    
    public void setSlotNumber(int slotNumber) {
        this.slotNumber = slotNumber;
    }
    
    public int getBatteryLevel() {
        return batteryLevel;
    }
    
    public void setBatteryLevel(int batteryLevel) {
        this.batteryLevel = batteryLevel;
    }
    
    public int getUsageCount() {
        return usageCount;
    }
    
    public void setUsageCount(int usageCount) {
        this.usageCount = usageCount;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public int getLightColor() {
        return lightColor;
    }
    
    public void setLightColor(int lightColor) {
        this.lightColor = lightColor;
    }
    
    public String getLightColorName() {
        return lightColorName;
    }
    
    public void setLightColorName(String lightColorName) {
        this.lightColorName = lightColorName;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    public String getRecommendationReason() {
        return recommendationReason;
    }
    
    public void setRecommendationReason(String recommendationReason) {
        this.recommendationReason = recommendationReason;
    }
    
    public String getCompartmentType() {
        return compartmentType;
    }
    
    public void setCompartmentType(String compartmentType) {
        this.compartmentType = compartmentType;
    }
    
    public String getTargetBodyPart() {
        return targetBodyPart;
    }
    
    public void setTargetBodyPart(String targetBodyPart) {
        this.targetBodyPart = targetBodyPart;
    }
    
    public int getDurationMinutes() {
        return durationMinutes;
    }
    
    public void setDurationMinutes(int durationMinutes) {
        this.durationMinutes = durationMinutes;
    }
    
    public int getIntensity() {
        return intensity;
    }
    
    public void setIntensity(int intensity) {
        this.intensity = intensity;
    }
    
    public int getFrequency() {
        return frequency;
    }
    
    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }
    
    @Override
    public String toString() {
        return String.format("TreatmentHeadRecommendation{headNumber=%d, slotNumber=%d, batteryLevel=%d, " +
                           "usageCount=%d, status='%s', lightColor=%d, lightColorName='%s', priority=%d, " +
                           "recommendationReason='%s', compartmentType='%s', targetBodyPart='%s', " +
                           "durationMinutes=%d, intensity=%d, frequency=%d}", 
                           headNumber, slotNumber, batteryLevel, usageCount, status, lightColor, 
                           lightColorName, priority, recommendationReason, compartmentType, targetBodyPart,
                           durationMinutes, intensity, frequency);
    }
}