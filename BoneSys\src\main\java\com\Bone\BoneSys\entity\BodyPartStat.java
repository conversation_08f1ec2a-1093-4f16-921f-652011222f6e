package com.Bone.BoneSys.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 部位统计实体类
 * 对应数据库表：body_part_stats
 */
@Entity
@Table(name = "body_part_stats", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"record_id", "body_part"}))
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = {"record"})
@ToString(exclude = {"record"})
public class BodyPartStat {
    
    /**
     * 统计ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    /**
     * 关联的档案
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "record_id", nullable = false)
    private Record record;
    
    /**
     * 治疗部位
     */
    @Column(name = "body_part", nullable = false, length = 50)
    private String bodyPart;
    
    /**
     * 该部位累计使用次数
     */
    @Column(name = "total_usage_count", nullable = false)
    private Integer totalUsageCount = 0;
    
    /**
     * 该部位累计使用时长（分钟）
     */
    @Column(name = "total_duration_minutes", nullable = false)
    private Integer totalDurationMinutes = 0;
    
    /**
     * 增加使用次数和时长
     */
    public void addUsage(Integer duration) {
        this.totalUsageCount++;
        this.totalDurationMinutes += duration;
    }
    
    /**
     * 计算平均每次治疗时长
     */
    @Transient
    public Double getAverageDuration() {
        if (totalUsageCount == null || totalUsageCount == 0) {
            return 0.0;
        }
        return (double) totalDurationMinutes / totalUsageCount;
    }
}