package com.Bone.BoneSys.dto.sync;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 同步结果数据传输对象
 * 包含单次同步操作的执行结果和统计信息
 */
@Data
@NoArgsConstructor
public class SyncResult {
    
    /**
     * 同步是否成功
     */
    private boolean success;
    
    /**
     * 同步执行时间
     */
    private LocalDateTime syncTime;
    
    /**
     * 处理的治疗头总数
     */
    private int totalHeads;
    
    /**
     * 更新的数据库记录数
     */
    private int updatedRecords;
    
    /**
     * 创建的数据库记录数
     */
    private int createdRecords;
    
    /**
     * 执行耗时（毫秒）
     */
    private long executionTimeMs;
    
    /**
     * 错误消息（失败时）
     */
    private String errorMessage;
    
    /**
     * 同步的治疗头信息列表
     */
    private List<TreatmentHeadInfo> syncedHeads;
    
    /**
     * 创建成功结果
     * 
     * @param syncedHeads 同步的治疗头列表
     * @param executionTimeMs 执行时间
     * @return 成功结果对象
     */
    public static SyncResult success(List<TreatmentHeadInfo> syncedHeads, long executionTimeMs) {
        SyncResult result = new SyncResult();
        result.success = true;
        result.syncTime = LocalDateTime.now();
        result.totalHeads = syncedHeads != null ? syncedHeads.size() : 0;
        result.updatedRecords = result.totalHeads; // 简化实现，后续可以区分更新和创建
        result.createdRecords = 0;
        result.executionTimeMs = executionTimeMs;
        result.syncedHeads = syncedHeads;
        return result;
    }
    
    /**
     * 创建成功结果（详细统计）
     * 
     * @param syncedHeads 同步的治疗头列表
     * @param updatedRecords 更新的记录数
     * @param createdRecords 创建的记录数
     * @param executionTimeMs 执行时间
     * @return 成功结果对象
     */
    public static SyncResult success(List<TreatmentHeadInfo> syncedHeads, 
                                   int updatedRecords, int createdRecords, long executionTimeMs) {
        SyncResult result = new SyncResult();
        result.success = true;
        result.syncTime = LocalDateTime.now();
        result.totalHeads = syncedHeads != null ? syncedHeads.size() : 0;
        result.updatedRecords = updatedRecords;
        result.createdRecords = createdRecords;
        result.executionTimeMs = executionTimeMs;
        result.syncedHeads = syncedHeads;
        return result;
    }
    
    /**
     * 创建失败结果
     * 
     * @param errorMessage 错误消息
     * @return 失败结果对象
     */
    public static SyncResult failure(String errorMessage) {
        SyncResult result = new SyncResult();
        result.success = false;
        result.syncTime = LocalDateTime.now();
        result.totalHeads = 0;
        result.updatedRecords = 0;
        result.createdRecords = 0;
        result.executionTimeMs = 0;
        result.errorMessage = errorMessage;
        return result;
    }
    
    /**
     * 创建失败结果（带执行时间）
     * 
     * @param errorMessage 错误消息
     * @param executionTimeMs 执行时间
     * @return 失败结果对象
     */
    public static SyncResult failure(String errorMessage, long executionTimeMs) {
        SyncResult result = failure(errorMessage);
        result.executionTimeMs = executionTimeMs;
        return result;
    }
    
    /**
     * 获取成功率描述
     * 
     * @return 成功率描述字符串
     */
    public String getSuccessRateDescription() {
        if (success) {
            return String.format("Success: %d heads processed in %dms", totalHeads, executionTimeMs);
        } else {
            return String.format("Failed: %s (took %dms)", errorMessage, executionTimeMs);
        }
    }
    
    /**
     * 检查是否为部分成功
     * 
     * @return 如果有部分治疗头处理成功但整体失败，返回true
     */
    public boolean isPartialSuccess() {
        return !success && totalHeads > 0;
    }
    
    /**
     * 获取处理效率（头/秒）
     * 
     * @return 每秒处理的治疗头数量
     */
    public double getProcessingRate() {
        if (executionTimeMs <= 0 || totalHeads <= 0) {
            return 0.0;
        }
        return (double) totalHeads / (executionTimeMs / 1000.0);
    }
}