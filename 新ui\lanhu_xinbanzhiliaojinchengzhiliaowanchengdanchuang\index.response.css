.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.block_1 {
  width: 99.95vw;
  height: 56.25vw;
  background: url(./img/dfbbbd175fc7977960a30f12940c43df.png)
    0vw 0vw no-repeat;
  background-size: 101.66vw 56.97vw;
  margin-left: 0.06vw;
}

.image_1 {
  width: 8.81vw;
  height: 3.65vw;
  margin: 1.04vw 0 0 5.36vw;
}

.text_1 {
  width: 15.32vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.56vw 42.34vw 0 28.12vw;
}

.section_1 {
  height: 56.25vw;
  background: url(./img/6184e356ba89d2d0bf272d625fb1e50e.png) -2.97vw -826.52vw
    no-repeat;
  background-size: 206.66vw 1176.25vw;
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
}

.block_2 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 16px;
  height: 29.43vw;
  border: 3px solid rgba(145, 201, 194, 1);
  width: 39.8vw;
  margin: 18.69vw 0 0 30.05vw;
}

.image-wrapper_3 {
  width: 27.56vw;
  height: 19.54vw;
  margin: 1.51vw 0 0 10.83vw;
}

.image_2 {
  width: 18.29vw;
  height: 18.29vw;
  margin-top: 1.25vw;
}

.image_3 {
  width: 3.13vw;
  height: 3.13vw;
}

.text-wrapper_2 {
  width: 17.61vw;
  height: 1.78vw;
  margin: 1.35vw 0 5.26vw 10.52vw;
}

.text_2 {
  width: 17.61vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}
