.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1073px;
  background: url(./img/6fb3e10f3e96c7288259592a9ab58684.png)
    0px -7px no-repeat;
  background-size: 1916px 1080px;
  margin-left: 4px;
  width: 1916px;
}

.section_1 {
  height: 1080px;
  background: url(./img/b111809ee101ba601c84c4c92f0294ca.png)
    0px -7px no-repeat;
  background-size: 1916px 1087px;
  width: 1916px;
}

.block_1 {
  width: 1920px;
  height: 1080px;
  background: url(./img/86810806097762d56f754980fac2b438.png)
    0px -6px no-repeat;
  background-size: 1920px 1086px;
  margin-left: -4px;
}

.section_2 {
  width: 1003px;
  height: 70px;
  margin: 13px 0 0 107px;
}

.image_1 {
  width: 169px;
  height: 70px;
}

.text_1 {
  width: 294px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 9px;
}

.section_3 {
  width: 1585px;
  height: 91px;
  margin: 53px 0 0 146px;
}

.group_1 {
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 474px;
  height: 66px;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin-top: 9px;
}

.text_2 {
  width: 153px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 36px;
}

.image_2 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 30px;
}

.text_3 {
  width: 95px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 75px 0 84px;
}

.group_2 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 346px;
  height: 66px;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 9px 0 0 58px;
}

.text_4 {
  width: 70px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 37px;
}

.image_3 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 27px;
}

.text_5 {
  width: 95px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 59px 0 57px;
}

.group_3 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 346px;
  height: 66px;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 10px 0 0 72px;
}

.text_6 {
  width: 74px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 16px 0 0 24px;
}

.image_4 {
  width: 1px;
  height: 43px;
  margin: 13px 0 0 27px;
}

.text_7 {
  width: 95px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 74px 0 51px;
}

.text-wrapper_1 {
  height: 91px;
  background: url(./img/a4bebd0b7c83d15be147f3b345ef791d.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 62px;
  width: 227px;
}

.text_8 {
  width: 80px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 19px 0 0 71px;
}

.section_4 {
  width: 1689px;
  height: 783px;
  background: url(./img/79c3ec967a296bb1f063e3795e500ab9.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 22px 0 48px 113px;
}

.text-wrapper_2 {
  width: 1368px;
  height: 34px;
  margin: 47px 0 0 168px;
}

.text_9 {
  width: 156px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text_10 {
  width: 71px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 161px;
}

.text_11 {
  width: 157px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 230px;
}

.text_12 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 238px;
}

.text_13 {
  width: 75px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 205px;
}

.image_5 {
  width: 1476px;
  height: 1px;
  margin: 16px 0 0 116px;
}

.block_2 {
  width: 1414px;
  height: 47px;
  margin: 12px 0 0 171px;
}

.text_14 {
  width: 152px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 8px;
}

.text_15 {
  width: 154px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 122px;
}

.text_16 {
  width: 115px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 6px 0 0 209px;
}

.image-text_1 {
  width: 210px;
  height: 38px;
  margin: 6px 0 0 165px;
}

.label_1 {
  width: 39px;
  height: 37px;
  margin-top: 1px;
}

.text-group_1 {
  width: 157px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text-wrapper_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 95px;
  width: 89px;
}

.text_17 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 14px;
}

.text-wrapper_4 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 14px;
  width: 89px;
}

.text_18 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 14px;
}

.image_6 {
  width: 1476px;
  height: 1px;
  margin: 11px 0 0 116px;
}

.block_3 {
  width: 1414px;
  height: 48px;
  margin: 12px 0 0 171px;
}

.text_19 {
  width: 152px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 11px;
}

.text_20 {
  width: 154px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 122px;
}

.text_21 {
  width: 115px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 209px;
}

.image-text_2 {
  width: 208px;
  height: 39px;
  margin: 9px 0 0 167px;
}

.label_2 {
  width: 38px;
  height: 38px;
  margin-top: 1px;
}

.text-group_2 {
  width: 157px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text-wrapper_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 95px;
  width: 89px;
}

.text_22 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 14px;
}

.text-wrapper_6 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 14px;
  width: 89px;
}

.text_23 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 14px;
}

.image-wrapper_1 {
  height: 1px;
  background: url(./img/982c824b98c17706cb7e17a9100be65c.png)
    0px 0px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 10px 0 0 116px;
}

.image_7 {
  width: 1476px;
  height: 1px;
}

.block_4 {
  width: 1414px;
  height: 47px;
  margin: 12px 0 0 171px;
}

.text_24 {
  width: 152px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
}

.text_25 {
  width: 154px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 122px;
}

.text_26 {
  width: 74px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 7px 0 0 230px;
}

.label_3 {
  width: 46px;
  height: 46px;
  margin: 1px 0 0 185px;
}

.text_27 {
  width: 113px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 8px 0 0 28px;
}

.text-wrapper_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 118px;
  width: 89px;
}

.text_28 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 14px;
}

.text-wrapper_8 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 47px;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 14px;
  width: 89px;
}

.text_29 {
  width: 60px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 9px 0 0 14px;
}

.image-wrapper_2 {
  height: 1px;
  background: url(./img/3c5d899b5feda3dc325b832e42bab2a8.png)
    0px 0px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 11px 0 0 116px;
}

.image_8 {
  width: 1476px;
  height: 1px;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 57px;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 260px;
  margin: 308px 0 107px 1366px;
}

.image_9 {
  width: 235px;
  height: 38px;
  margin: 9px 0 0 10px;
}
