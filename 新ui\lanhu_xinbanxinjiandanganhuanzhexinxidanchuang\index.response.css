.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.image_1 {
  width: 10.94vw;
  height: 3.7vw;
  margin: 47.44vw 0 0 47.6vw;
}

.text-wrapper_1 {
  height: 4.02vw;
  background: url(./img/27de145c7e2db8f2cb4b4ceff7ea6b1f.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 11.25vw;
  position: absolute;
  left: 47.45vw;
  top: 47.3vw;
}

.text_1 {
  width: 9.02vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 0 0 1.14vw;
}

.block_1 {
  height: 56.25vw;
  background: url(./img/44e5c9121fe18c932e5368005de9617e.png) -0.06vw -281.62vw
    no-repeat;
  background-size: 206.71vw 376.25vw;
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
}

.label_1 {
  width: 2.56vw;
  height: 2.56vw;
  margin: 5.41vw 0 0 76.35vw;
}

.group_1 {
  height: 50.27vw;
  background: url(./img/ab702eaeed1b324761de28e170dc71bf.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 65.27vw;
  position: absolute;
  left: 16.05vw;
  top: 3.29vw;
}

.group_11 {
  width: 35.47vw;
  height: 3.13vw;
  margin: 1.82vw 0 0 26.3vw;
}

.text_2 {
  width: 15.37vw;
  height: 2.56vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.32vw;
}

.label_2 {
  width: 3.13vw;
  height: 3.13vw;
}

.group_12 {
  width: 27.56vw;
  height: 2.45vw;
  margin: 4.42vw 0 0 8.38vw;
}

.text_3 {
  width: 4.07vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.47vw;
}

.section_1 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 22.09vw;
  height: 2.45vw;
}

.group_13 {
  width: 46.57vw;
  height: 2.5vw;
  margin: 2.23vw 0 0 8.33vw;
}

.text_4 {
  width: 4.8vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.37vw;
}

.group_2 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 11.05vw;
  height: 2.45vw;
  margin: 0.05vw 0 0 0.72vw;
}

.text_5 {
  width: 8.34vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.36vw 0 0 8.33vw;
}

.group_3 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 12.4vw;
  height: 2.4vw;
  margin-left: 0.94vw;
}

.group_14 {
  width: 45.47vw;
  height: 2.4vw;
  margin: 2.18vw 0 0 8.38vw;
}

.text_6 {
  width: 4.07vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.37vw;
}

.group_4 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 11.05vw;
  height: 2.4vw;
  margin-left: 1.41vw;
}

.text_7 {
  width: 4.02vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.26vw 0 0 12.65vw;
}

.thumbnail_1 {
  width: 0.89vw;
  height: 0.89vw;
  margin: 0.78vw 0 0 2.23vw;
}

.text_8 {
  width: 1.57vw;
  height: 1.57vw;
  overflow-wrap: break-word;
  color: rgba(64, 65, 65, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.36vw 0 0 1.3vw;
}

.thumbnail_2 {
  width: 0.89vw;
  height: 0.89vw;
  margin: 0.78vw 0 0 2.5vw;
}

.text_9 {
  width: 1.67vw;
  height: 1.67vw;
  overflow-wrap: break-word;
  color: rgba(64, 65, 65, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.26vw 0 0 1.25vw;
}

.group_15 {
  width: 19.28vw;
  height: 2.4vw;
  margin: 2.96vw 0 0 8.54vw;
}

.text_10 {
  width: 3.96vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.37vw;
}

.box_1 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 13.96vw;
  height: 2.4vw;
}

.group_16 {
  width: 46.52vw;
  height: 12.09vw;
  margin: 1.87vw 0 0 8.38vw;
}

.text_11 {
  width: 4.07vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.32vw;
}

.box_2 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 41.05vw;
  height: 12.09vw;
}

.group_17 {
  width: 11.83vw;
  height: 4.74vw;
  margin: 2.44vw 0 2.6vw 28.33vw;
}

.text-wrapper_2 {
  height: 4.74vw;
  background: url(./img/4849a1de40a4c4579615db3ce298d8cd.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 11.83vw;
}

.text_12 {
  width: 9.02vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.04vw 0 0 1.14vw;
}
