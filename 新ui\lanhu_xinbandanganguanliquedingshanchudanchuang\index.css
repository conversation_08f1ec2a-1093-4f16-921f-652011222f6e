.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1080px;
  background: url(./img/9a0fa162fbeb26d0c5a76c5dbc9c75e0.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.group_1 {
  height: 1080px;
  background: url(./img/097a6cea1309bbe92414d776a59468f7.png) -28px -17198px
    no-repeat;
  background-size: 1948px 18278px;
  width: 1920px;
}

.group_2 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 16px;
  width: 545px;
  height: 404px;
  border: 3px solid rgba(145, 201, 194, 1);
  margin: 493px 0 0 677px;
}

.image_1 {
  width: 60px;
  height: 60px;
  margin: 20px 0 0 451px;
}

.group_3 {
  width: 416px;
  height: 69px;
  background: url(./img/e98c37e1911abd0aa73f37747e79843d.png) -9px
    0px no-repeat;
  background-size: 435px 91px;
  margin: 40px 0 0 64px;
}

.label_1 {
  width: 43px;
  height: 42px;
  margin: 9px 0 0 37px;
}

.box_2 {
  width: 221px;
  height: 31px;
  margin: 20px 0 0 31px;
}

.text_1 {
  width: 180px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(116, 116, 116, 1);
  font-size: 26px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 7px;
  margin-left: 26px;
}

.image_2 {
  width: 221px;
  height: 1px;
  margin-top: 3px;
}

.label_2 {
  width: 30px;
  height: 26px;
  margin: 22px 29px 0 25px;
}

.group_4 {
  height: 69px;
  background: url(./img/933debd2590e08c413b5a6efa676f37e.png) -10px
    0px no-repeat;
  background-size: 252px 92px;
  width: 232px;
  margin: 76px 0 70px 157px;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 3px 0 0 2px;
}

.text_2 {
  width: 93px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin: 16px 0 0 66px;
}
