html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.box_1 {
  height: 28.8rem;
  background: url(./img/9a0fa162fbeb26d0c5a76c5dbc9c75e0.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 51.2rem;
}

.group_1 {
  height: 28.8rem;
  background: url(./img/097a6cea1309bbe92414d776a59468f7.png) -0.747rem -458.614rem
    no-repeat;
  background-size: 51.947rem 487.414rem;
  width: 51.2rem;
}

.group_2 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 16px;
  width: 14.534rem;
  height: 10.774rem;
  border: 3px solid rgba(145, 201, 194, 1);
  margin: 13.147rem 0 0 18.054rem;
}

.image_1 {
  width: 1.6rem;
  height: 1.6rem;
  margin: 0.534rem 0 0 12.027rem;
}

.group_3 {
  width: 11.094rem;
  height: 1.84rem;
  background: url(./img/e98c37e1911abd0aa73f37747e79843d.png) -0.24rem
    0rem no-repeat;
  background-size: 11.6rem 2.427rem;
  margin: 1.067rem 0 0 1.707rem;
}

.label_1 {
  width: 1.147rem;
  height: 1.12rem;
  margin: 0.24rem 0 0 0.987rem;
}

.box_2 {
  width: 5.894rem;
  height: 0.827rem;
  margin: 0.534rem 0 0 0.827rem;
}

.text_1 {
  width: 4.8rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(116, 116, 116, 1);
  font-size: 0.693rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 0.187rem;
  margin-left: 0.694rem;
}

.image_2 {
  width: 5.894rem;
  height: 0.027rem;
  margin-top: 0.08rem;
}

.label_2 {
  width: 0.8rem;
  height: 0.694rem;
  margin: 0.587rem 0.774rem 0 0.667rem;
}

.group_4 {
  height: 1.84rem;
  background: url(./img/933debd2590e08c413b5a6efa676f37e.png) -0.267rem
    0rem no-repeat;
  background-size: 6.72rem 2.454rem;
  width: 6.187rem;
  margin: 2.027rem 0 1.867rem 4.187rem;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 1.707rem;
  width: 6.027rem;
  margin: 0.08rem 0 0 0.054rem;
}

.text_2 {
  width: 2.48rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.427rem 0 0 1.76rem;
}
