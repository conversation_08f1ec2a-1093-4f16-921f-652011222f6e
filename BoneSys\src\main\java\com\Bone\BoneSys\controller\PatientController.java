package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.PaginationInfo;
import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.repository.*;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 患者信息控制器
 * 对应UI页面：新版-个人信息.png, 新版-个人信息-诊断详细弹窗.png, 新版-新建档案-患者信息弹窗.png
 */
@RestController
@RequestMapping("/api/patients")
@CrossOrigin(origins = "*")
public class PatientController {

    private static final Logger logger = LoggerFactory.getLogger(PatientController.class);

    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;

    /**
     * 获取患者列表
     * GET /api/patients
     */
    @GetMapping
    public ApiResponse<PatientListResponse> getPatientList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        try {
            logger.info("Fetching patient list - page: {}, size: {}, search: {}", page, size, search);

            List<Patient> allPatients = patientRepository.findAll();
            List<PatientListItem> patients = allPatients.stream()
                .map(this::convertToListItem)
                .collect(Collectors.toList());

            PatientListResponse response = new PatientListResponse();
            response.setPatients(patients);
            
            // 创建简单的分页信息
            PaginationInfo pagination = new PaginationInfo();
            pagination.setCurrentPage(1);
            pagination.setPageSize(patients.size());
            pagination.setTotalPages(1);
            pagination.setTotalRecords((long) patients.size());
            pagination.setHasNext(false);
            pagination.setHasPrevious(false);
            response.setPagination(pagination);

            return ApiResponse.success("患者列表获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching patient list", e);
            return ApiResponse.error(500, "获取患者列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取患者个人信息页面数据
     * GET /api/patients/{patientId}
     */
    @GetMapping("/{patientId}")
    public ApiResponse<PatientDetailResponse> getPatientDetail(
            @PathVariable Long patientId,
            @RequestParam(defaultValue = "1") int historyPage,
            @RequestParam(defaultValue = "10") int historySize) {
        
        try {
            logger.info("Fetching patient detail: {}", patientId);

            Optional<Patient> patientOpt = patientRepository.findById(patientId);
            if (!patientOpt.isPresent()) {
                return ApiResponse.error(404, "患者不存在");
            }

            Patient patient = patientOpt.get();
            PatientDetailResponse response = new PatientDetailResponse();

            // 1. 基本信息
            response.setBasicInfo(buildBasicInfo(patient));

            // 2. 治疗统计
            response.setTreatmentStats(buildTreatmentStats(patientId));

            // 3. 治疗历史记录
            response.setTreatmentHistory(buildTreatmentHistory(patientId, historyPage, historySize));

            return ApiResponse.success("患者信息获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching patient detail: {}", patientId, e);
            return ApiResponse.error(500, "获取患者信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新患者诊断信息
     * PUT /api/patients/{patientId}
     */
    @PutMapping("/{patientId}")
    public ApiResponse<Void> updatePatientDiagnosis(
            @PathVariable Long patientId,
            @RequestBody UpdatePatientDiagnosisRequest request) {
        try {
            logger.info("Updating patient diagnosis: {}", patientId);

            // 验证患者是否存在
            Optional<Patient> patientOpt = patientRepository.findById(patientId);
            if (!patientOpt.isPresent()) {
                return ApiResponse.error(404, "患者不存在");
            }

            // 获取患者的最新档案记录
            List<com.Bone.BoneSys.entity.Record> records = recordRepository.findByPatientId(patientId);
            if (records.isEmpty()) {
                return ApiResponse.error(404, "患者没有档案记录");
            }

            // 更新最新档案的诊断信息
            com.Bone.BoneSys.entity.Record latestRecord = records.get(records.size() - 1);
            latestRecord.setDiagnosisDescription(request.getDiagnosis());
            recordRepository.save(latestRecord);

            logger.info("Patient {} diagnosis updated successfully", patientId);
            return ApiResponse.success("诊断信息更新成功");

        } catch (Exception e) {
            logger.error("Error updating patient diagnosis: {}", patientId, e);
            return ApiResponse.error(500, "更新诊断信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取患者诊断详情
     * GET /api/patients/{patientId}/diagnoses
     */
    @GetMapping("/{patientId}/diagnoses")
    public ApiResponse<DiagnosisListResponse> getPatientDiagnoses(@PathVariable Long patientId) {
        try {
            logger.info("Fetching patient diagnoses: {}", patientId);

            // 查询患者的所有档案记录
            List<com.Bone.BoneSys.entity.Record> records = recordRepository.findByPatientId(patientId);
            
            List<DiagnosisItem> diagnoses = records.stream()
                .filter(record -> record.getDiagnosisDescription() != null)
                .map(this::convertToDiagnosisItem)
                .collect(Collectors.toList());

            DiagnosisListResponse response = new DiagnosisListResponse();
            response.setDiagnoses(diagnoses);

            return ApiResponse.success("诊断信息获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching patient diagnoses: {}", patientId, e);
            return ApiResponse.error(500, "获取诊断信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取下一个患者编号
     * GET /api/patients/next-number
     */
    @GetMapping("/next-number")
    public ApiResponse<NextNumberResponse> getNextPatientNumber() {
        try {
            logger.info("Generating next patient number");

            // 查询当前最大的患者编号
            Long maxId = patientRepository.findMaxId();
            String nextNumber = "P" + String.format("%03d", (maxId != null ? maxId : 0) + 1);

            NextNumberResponse response = new NextNumberResponse();
            response.setPatientNumber(nextNumber);

            return ApiResponse.success("患者编号生成成功", response);

        } catch (Exception e) {
            logger.error("Error generating next patient number", e);
            return ApiResponse.error(500, "生成患者编号失败: " + e.getMessage());
        }
    }

    /**
     * 创建新患者
     * POST /api/patients
     */
    @PostMapping
    public ApiResponse<CreatePatientResponse> createPatient(@RequestBody CreatePatientRequest request) {
        try {
            logger.info("Creating new patient: {}", request.getName());

            // 验证必填字段
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return ApiResponse.error(400, "患者姓名不能为空");
            }
            if (request.getCardId() == null || request.getCardId().trim().isEmpty()) {
                return ApiResponse.error(400, "就诊卡号不能为空");
            }

            // 检查卡号是否已存在
            if (patientRepository.existsByPatientCardId(request.getCardId())) {
                return ApiResponse.error(400, "就诊卡号已存在");
            }

            // 创建患者
            Patient patient = new Patient();
            patient.setName(request.getName().trim());
            patient.setAge(request.getAge());
            patient.setGender(request.getGender());
            patient.setContactInfo(request.getPhone());
            patient.setPatientCardId(request.getCardId().trim());
            patient.setCreatedAt(LocalDateTime.now());

            Patient savedPatient = patientRepository.save(patient);

            // 如果有诊断描述，创建档案记录
            if (request.getDiagnosisDescription() != null && !request.getDiagnosisDescription().trim().isEmpty()) {
                com.Bone.BoneSys.entity.Record record = new com.Bone.BoneSys.entity.Record();
                record.setPatient(savedPatient);
                record.setRecordNumber(generateRecordNumber());
                record.setDiagnosisDescription(request.getDiagnosisDescription().trim());
                record.setCreatedAt(LocalDateTime.now().toLocalDate());
                record.setSessionsCompletedCount(0);
                
                recordRepository.save(record);
            }

            CreatePatientResponse response = new CreatePatientResponse();
            response.setPatientId(savedPatient.getId());
            response.setPatientNumber(request.getPatientNumber());

            return ApiResponse.success("患者创建成功", response);

        } catch (Exception e) {
            logger.error("Error creating patient", e);
            return ApiResponse.error(500, "创建患者失败: " + e.getMessage());
        }
    }

    /**
     * 构建基本信息
     */
    private BasicInfo buildBasicInfo(Patient patient) {
        BasicInfo basicInfo = new BasicInfo();
        basicInfo.setAvatarUrl("/images/avatars/default.png");
        basicInfo.setName(patient.getName());
        basicInfo.setPatientNumber("P" + String.format("%03d", patient.getId()));
        basicInfo.setCardId(patient.getPatientCardId());
        basicInfo.setGender(patient.getGender() != null ? patient.getGender() : "未知");
        basicInfo.setAge(patient.getAge() != null ? patient.getAge() + "岁" : "未知");
        basicInfo.setPhone(patient.getContactInfo() != null ? patient.getContactInfo() : "未填写");

        // 获取最新的诊断信息
        List<com.Bone.BoneSys.entity.Record> records = recordRepository.findByPatientId(patient.getId());
        if (!records.isEmpty()) {
            com.Bone.BoneSys.entity.Record latestRecord = records.get(records.size() - 1);
            basicInfo.setDiagnosis(latestRecord.getDiagnosisDescription() != null ?
                latestRecord.getDiagnosisDescription() : "暂无诊断信息");
        } else {
            basicInfo.setDiagnosis("暂无诊断信息");
        }

        return basicInfo;
    }

    /**
     * 构建治疗统计
     */
    private TreatmentStats buildTreatmentStats(Long patientId) {
        TreatmentStats stats = new TreatmentStats();
        
        // 查询部位统计
        List<BodyPartStat> bodyPartStats = bodyPartStatRepository.findByPatientId(patientId);
        
        int totalMinutes = bodyPartStats.stream()
            .mapToInt(BodyPartStat::getTotalDurationMinutes)
            .sum();
        
        stats.setTotalDuration(formatDuration(totalMinutes));
        
        List<BodyPartStatItem> bodyPartStatItems = bodyPartStats.stream()
            .map(stat -> {
                BodyPartStatItem item = new BodyPartStatItem();
                item.setBodyPart(stat.getBodyPart());
                item.setDuration(formatDuration(stat.getTotalDurationMinutes()));
                return item;
            })
            .collect(Collectors.toList());
        
        stats.setBodyPartStats(bodyPartStatItems);
        return stats;
    }

    /**
     * 构建治疗历史记录
     */
    private TreatmentHistory buildTreatmentHistory(Long patientId, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("id").descending());
        Page<TreatmentDetail> detailPage = treatmentDetailRepository.findByPatientId(patientId, pageable);
        
        List<TreatmentHistoryItem> records = detailPage.getContent().stream()
            .map(this::convertToHistoryItem)
            .collect(Collectors.toList());
        
        TreatmentHistory history = new TreatmentHistory();
        history.setRecords(records);
        history.setPagination(PaginationInfo.fromPage(detailPage));
        
        return history;
    }

    /**
     * 转换为列表项
     */
    private PatientListItem convertToListItem(Patient patient) {
        PatientListItem item = new PatientListItem();
        item.setId(patient.getId());
        item.setName(patient.getName() != null ? patient.getName() : "");
        item.setPatientNumber("P" + String.format("%03d", patient.getId()));
        item.setCardId(patient.getPatientCardId() != null ? patient.getPatientCardId() : "");
        item.setGender(patient.getGender() != null ? patient.getGender() : "未知");
        item.setAge(patient.getAge() != null ? patient.getAge() : "未知");
        item.setPhone(patient.getContactInfo() != null ? patient.getContactInfo() : "未填写");
        item.setCreatedAt(patient.getCreatedAt() != null ? 
            patient.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")) : "");
        item.setTotalSessions(0);
        
        return item;
    }

    /**
     * 转换为历史记录项
     */
    private TreatmentHistoryItem convertToHistoryItem(TreatmentDetail detail) {
        TreatmentHistoryItem item = new TreatmentHistoryItem();
        item.setTreatmentDate(detail.getProcess().getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        item.setBodyPart(detail.getBodyPart());
        item.setIntensity(detail.getIntensity() + "mW/C");
        item.setHeadNumber(detail.getHeadNumberUsed());
        item.setDuration(detail.getDuration() + "分钟");
        return item;
    }

    /**
     * 转换为诊断项
     */
    private DiagnosisItem convertToDiagnosisItem(com.Bone.BoneSys.entity.Record record) {
        DiagnosisItem item = new DiagnosisItem();
        item.setDiagnosisDate(record.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        item.setDoctorName("系统医生"); // 模拟医生姓名
        item.setDescription(record.getDiagnosisDescription());
        return item;
    }

    /**
     * 格式化时长
     */
    private String formatDuration(int minutes) {
        int hours = minutes / 60;
        int mins = minutes % 60;
        return hours + "小时" + mins + "分钟";
    }

    /**
     * 生成档案编号
     */
    private String generateRecordNumber() {
        return "R" + System.currentTimeMillis();
    }

    // DTO类定义
    @Data
    public static class PatientListResponse {
        private List<PatientListItem> patients;
        private PaginationInfo pagination;
    }

    @Data
    public static class PatientListItem {
        private Long id;
        private String name;
        private String patientNumber;
        private String cardId;
        private String gender;
        private String age;
        private String phone;
        private String createdAt;
        private Integer totalSessions;
    }

    @Data
    public static class PatientDetailResponse {
        private BasicInfo basicInfo;
        private TreatmentStats treatmentStats;
        private TreatmentHistory treatmentHistory;
    }

    @Data
    public static class BasicInfo {
        private String avatarUrl;
        private String name;
        private String patientNumber;
        private String cardId;
        private String gender;
        private String age;
        private String phone;
        private String diagnosis;

        public void setDiagnosis(String diagnosis) {
            this.diagnosis = diagnosis;
        }

        public String getDiagnosis() {
            return diagnosis;
        }
    }

    @Data
    public static class TreatmentStats {
        private String totalDuration;
        private List<BodyPartStatItem> bodyPartStats;
    }

    @Data
    public static class BodyPartStatItem {
        private String bodyPart;
        private String duration;
    }

    @Data
    public static class TreatmentHistory {
        private List<TreatmentHistoryItem> records;
        private PaginationInfo pagination;
    }

    @Data
    public static class TreatmentHistoryItem {
        private String treatmentDate;
        private String bodyPart;
        private String intensity;
        private Integer headNumber;
        private String duration;
    }

    @Data
    public static class DiagnosisListResponse {
        private List<DiagnosisItem> diagnoses;
    }

    @Data
    public static class DiagnosisItem {
        private String diagnosisDate;
        private String doctorName;
        private String description;
    }

    @Data
    public static class NextNumberResponse {
        private String patientNumber;
    }

    @Data
    public static class CreatePatientRequest {
        private String patientNumber;
        private String name;
        private String age;
        private String gender;
        private String phone;
        private String cardId;
        private String diagnosisDescription;
    }

    @Data
    public static class CreatePatientResponse {
        private Long patientId;
        private String patientNumber;
    }

    @Data
    public static class UpdatePatientDiagnosisRequest {
        private String diagnosis;
    }

}

