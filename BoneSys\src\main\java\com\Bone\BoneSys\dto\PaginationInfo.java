package com.Bone.BoneSys.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 分页信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginationInfo {
    
    /**
     * 当前页码（从1开始）
     */
    private Integer currentPage;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer totalPages;
    
    /**
     * 总记录数
     */
    private Long totalRecords;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 根据Spring Data的Page对象创建分页信息
     */
    public static PaginationInfo fromPage(org.springframework.data.domain.Page<?> page) {
        PaginationInfo pagination = new PaginationInfo();
        pagination.setCurrentPage(page.getNumber() + 1); // Spring Data从0开始，转换为从1开始
        pagination.setPageSize(page.getSize());
        pagination.setTotalPages(page.getTotalPages());
        pagination.setTotalRecords(page.getTotalElements());
        pagination.setHasNext(page.hasNext());
        pagination.setHasPrevious(page.hasPrevious());
        return pagination;
    }
}