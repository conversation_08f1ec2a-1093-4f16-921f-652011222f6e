html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.box_1 {
  height: 28.614rem;
  background: url(./img/6fb3e10f3e96c7288259592a9ab58684.png)
    0rem -0.187rem no-repeat;
  background-size: 51.094rem 28.8rem;
  margin-left: 0.107rem;
  width: 51.094rem;
}

.section_1 {
  height: 28.8rem;
  background: url(./img/b111809ee101ba601c84c4c92f0294ca.png)
    0rem -0.187rem no-repeat;
  background-size: 51.094rem 28.987rem;
  width: 51.094rem;
}

.block_1 {
  width: 51.2rem;
  height: 28.8rem;
  background: url(./img/86810806097762d56f754980fac2b438.png)
    0rem -0.16rem no-repeat;
  background-size: 51.2rem 28.96rem;
  margin-left: -0.106rem;
}

.section_2 {
  width: 26.747rem;
  height: 1.867rem;
  margin: 0.347rem 0 0 2.854rem;
}

.image_1 {
  width: 4.507rem;
  height: 1.867rem;
}

.text_1 {
  width: 7.84rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.24rem;
}

.section_3 {
  width: 42.267rem;
  height: 2.427rem;
  margin: 1.414rem 0 0 3.894rem;
}

.group_1 {
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 12.64rem;
  height: 1.76rem;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin-top: 0.24rem;
}

.text_2 {
  width: 4.08rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 0.96rem;
}

.image_2 {
  width: 0.027rem;
  height: 1.147rem;
  margin: 0.347rem 0 0 0.8rem;
}

.text_3 {
  width: 2.534rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 2rem 0 2.24rem;
}

.group_2 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 9.227rem;
  height: 1.76rem;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 0.24rem 0 0 1.547rem;
}

.text_4 {
  width: 1.867rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 0.987rem;
}

.image_3 {
  width: 0.027rem;
  height: 1.147rem;
  margin: 0.347rem 0 0 0.72rem;
}

.text_5 {
  width: 2.534rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 1.574rem 0 1.52rem;
}

.group_3 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 9.227rem;
  height: 1.76rem;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 0.267rem 0 0 1.92rem;
}

.text_6 {
  width: 1.974rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 0.64rem;
}

.image_4 {
  width: 0.027rem;
  height: 1.147rem;
  margin: 0.347rem 0 0 0.72rem;
}

.text_7 {
  width: 2.534rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 1.974rem 0 1.36rem;
}

.text-wrapper_1 {
  height: 2.427rem;
  background: url(./img/a4bebd0b7c83d15be147f3b345ef791d.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 1.654rem;
  width: 6.054rem;
}

.text_8 {
  width: 2.134rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 1.894rem;
}

.section_4 {
  width: 45.04rem;
  height: 20.88rem;
  background: url(./img/79c3ec967a296bb1f063e3795e500ab9.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 0.587rem 0 1.28rem 3.014rem;
}

.text-wrapper_2 {
  width: 36.48rem;
  height: 0.907rem;
  margin: 1.254rem 0 0 4.48rem;
}

.text_9 {
  width: 4.16rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_10 {
  width: 1.894rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 4.294rem;
}

.text_11 {
  width: 4.187rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 6.134rem;
}

.text_12 {
  width: 2rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 6.347rem;
}

.text_13 {
  width: 2rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 5.467rem;
}

.image_5 {
  width: 39.36rem;
  height: 0.027rem;
  margin: 0.427rem 0 0 3.094rem;
}

.block_2 {
  width: 37.707rem;
  height: 1.254rem;
  margin: 0.32rem 0 0 4.56rem;
}

.text_14 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.214rem;
}

.text_15 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.16rem 0 0 3.254rem;
}

.text_16 {
  width: 3.067rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.16rem 0 0 5.574rem;
}

.image-text_1 {
  width: 5.6rem;
  height: 1.014rem;
  margin: 0.16rem 0 0 4.4rem;
}

.label_1 {
  width: 1.04rem;
  height: 0.987rem;
  margin-top: 0.027rem;
}

.text-group_1 {
  width: 4.187rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text-wrapper_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 2.534rem;
  width: 2.374rem;
}

.text_17 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.24rem 0 0 0.374rem;
}

.text-wrapper_4 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.374rem;
  width: 2.374rem;
}

.text_18 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.24rem 0 0 0.374rem;
}

.image_6 {
  width: 39.36rem;
  height: 0.027rem;
  margin: 0.294rem 0 0 3.094rem;
}

.block_3 {
  width: 37.707rem;
  height: 1.28rem;
  margin: 0.32rem 0 0 4.56rem;
}

.text_19 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.294rem;
}

.text_20 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.24rem 0 0 3.254rem;
}

.text_21 {
  width: 3.067rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.214rem 0 0 5.574rem;
}

.image-text_2 {
  width: 5.547rem;
  height: 1.04rem;
  margin: 0.24rem 0 0 4.454rem;
}

.label_2 {
  width: 1.014rem;
  height: 1.014rem;
  margin-top: 0.027rem;
}

.text-group_2 {
  width: 4.187rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text-wrapper_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 2.534rem;
  width: 2.374rem;
}

.text_22 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.24rem 0 0 0.374rem;
}

.text-wrapper_6 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.374rem;
  width: 2.374rem;
}

.text_23 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.24rem 0 0 0.374rem;
}

.image-wrapper_1 {
  height: 0.027rem;
  background: url(./img/982c824b98c17706cb7e17a9100be65c.png)
    0rem 0rem no-repeat;
  background-size: 39.36rem 0.054rem;
  width: 39.36rem;
  margin: 0.267rem 0 0 3.094rem;
}

.image_7 {
  width: 39.36rem;
  height: 0.027rem;
}

.block_4 {
  width: 37.707rem;
  height: 1.254rem;
  margin: 0.32rem 0 0 4.56rem;
}

.text_24 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.267rem;
}

.text_25 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.214rem 0 0 3.254rem;
}

.text_26 {
  width: 1.974rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.187rem 0 0 6.134rem;
}

.label_3 {
  width: 1.227rem;
  height: 1.227rem;
  margin: 0.027rem 0 0 4.934rem;
}

.text_27 {
  width: 3.014rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.214rem 0 0 0.747rem;
}

.text-wrapper_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 3.147rem;
  width: 2.374rem;
}

.text_28 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.24rem 0 0 0.374rem;
}

.text-wrapper_8 {
  background-color: rgba(254, 134, 33, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.374rem;
  width: 2.374rem;
}

.text_29 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.24rem 0 0 0.374rem;
}

.image-wrapper_2 {
  height: 0.027rem;
  background: url(./img/3c5d899b5feda3dc325b832e42bab2a8.png)
    0rem 0rem no-repeat;
  background-size: 39.36rem 0.054rem;
  width: 39.36rem;
  margin: 0.294rem 0 0 3.094rem;
}

.image_8 {
  width: 39.36rem;
  height: 0.027rem;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 1.52rem;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 6.934rem;
  margin: 8.214rem 0 2.854rem 36.427rem;
}

.image_9 {
  width: 6.267rem;
  height: 1.014rem;
  margin: 0.24rem 0 0 0.267rem;
}
