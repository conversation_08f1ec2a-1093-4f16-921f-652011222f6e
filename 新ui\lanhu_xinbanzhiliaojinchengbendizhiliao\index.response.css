.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.box_1 {
  height: 56.25vw;
  background: url(./img/581596b6024a8797eb084676c601cc04.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 0.06vw;
  width: 99.95vw;
}

.group_1 {
  width: 52.24vw;
  height: 3.65vw;
  margin: 1.04vw 0 0 5.36vw;
}

.image_1 {
  width: 8.81vw;
  height: 3.65vw;
}

.text_1 {
  width: 15.32vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.53vw;
}

.group_2 {
  position: relative;
  width: 88.29vw;
  height: 4.8vw;
  margin: 4.01vw 0 0 5.83vw;
}

.image_2 {
  width: 2.92vw;
  height: 2.92vw;
  margin-top: 0.84vw;
}

.text_2 {
  width: 4.33vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.56vw 0 0 1.25vw;
}

.block_1 {
  width: 17.97vw;
  height: 4.74vw;
  background: url(./img/da69ab339dafb93891d48a37c09b9a2f.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 28.55vw;
}

.label_1 {
  width: 1.67vw;
  height: 1.52vw;
  margin: 1.25vw 0 0 1.3vw;
}

.text_3 {
  width: 11.2vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.19vw 2.55vw 0 1.25vw;
}

.block_2 {
  width: 17.97vw;
  height: 4.74vw;
  background: url(./img/f328c43c1f4187c31ec25f2a47113077.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 1.78vw;
}

.label_2 {
  width: 1.67vw;
  height: 1.52vw;
  margin: 1.25vw 0 0 1.3vw;
}

.text_4 {
  width: 11.2vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.19vw 2.55vw 0 1.25vw;
}

.text_5 {
  width: 3.65vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.19vw 2.96vw 0 6.92vw;
}

.block_3 {
  position: absolute;
  left: 76.46vw;
  top: 0.06vw;
  width: 11.83vw;
  height: 4.74vw;
  background: url(./img/5e50169bee19e094657b18f2bc6e3b7a.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.label_3 {
  width: 1.93vw;
  height: 1.93vw;
  margin: 1.04vw 0 0 1.71vw;
}

.text_6 {
  width: 3.65vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 1.14vw 3.22vw 0 1.3vw;
}

.group_3 {
  width: 88.49vw;
  height: 14.54vw;
  margin: 7.39vw 0 20.83vw 5.46vw;
}

.group_4 {
  box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
  background-color: rgba(255, 255, 255, 1);
  width: 25.84vw;
  height: 14.48vw;
  border: 0.5px solid rgba(254, 152, 8, 1);
  margin-top: 0.06vw;
}

.group_5 {
  width: 25.84vw;
  height: 5.53vw;
  background: url(./img/8b04f3885006c864b55e5ac79f51715d.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image-text_1 {
  width: 6.67vw;
  height: 1.78vw;
  margin: 1.56vw 0 0 2.08vw;
}

.label_4 {
  width: 1.78vw;
  height: 1.78vw;
}

.text-group_1 {
  width: 4.17vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.21vw;
}

.image_3 {
  width: 2.66vw;
  height: 2.66vw;
  margin: 0.98vw 2.76vw 0 11.66vw;
}

.text-wrapper_1 {
  width: 19.9vw;
  height: 1.72vw;
  margin: 2.39vw 0 0 2.91vw;
}

.text_7 {
  width: 7.35vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_8 {
  width: 8.29vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.image_4 {
  width: 21.25vw;
  height: 0.16vw;
  margin: 0.57vw 0 0 2.5vw;
}

.text-wrapper_2 {
  width: 21.41vw;
  height: 1.72vw;
  margin: 0.67vw 0 1.71vw 2.96vw;
}

.text_9 {
  width: 7.4vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_10 {
  width: 11.46vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.11vw;
}

.group_6 {
  box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
  background-color: rgba(255, 255, 255, 1);
  width: 25.84vw;
  height: 14.48vw;
  border: 0.5px solid rgba(254, 152, 8, 1);
  margin-left: 5.47vw;
}

.section_1 {
  width: 25.84vw;
  height: 5.53vw;
  background: url(./img/61fd8b7a2d7928b12bfc10eef686680c.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image-text_2 {
  width: 6.67vw;
  height: 1.78vw;
  margin: 1.56vw 0 0 2.08vw;
}

.label_5 {
  width: 1.78vw;
  height: 1.78vw;
}

.text-group_2 {
  width: 4.12vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.21vw;
}

.image_5 {
  width: 2.66vw;
  height: 2.66vw;
  margin: 0.98vw 2.76vw 0 11.66vw;
}

.text-wrapper_3 {
  width: 19.85vw;
  height: 1.78vw;
  margin: 2.34vw 0 0 2.96vw;
}

.text_11 {
  width: 7.35vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.06vw;
}

.text_12 {
  width: 8.29vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.image_6 {
  width: 21.25vw;
  height: 0.16vw;
  margin: 0.57vw 0 0 2.55vw;
}

.text-wrapper_4 {
  width: 21.46vw;
  height: 1.78vw;
  margin: 0.62vw 0 1.71vw 2.96vw;
}

.text_13 {
  width: 7.4vw;
  height: 1.78vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_14 {
  width: 11.46vw;
  height: 1.57vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.16vw;
}

.group_7 {
  box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
  background-color: rgba(255, 255, 255, 1);
  width: 25.84vw;
  height: 14.48vw;
  border: 0.5px solid rgba(254, 152, 8, 1);
  margin: 0.05vw 0 0 5.52vw;
}

.box_2 {
  width: 25.84vw;
  height: 5.53vw;
  background: url(./img/b088b7478763ec69ff3418f572620403.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image-text_3 {
  width: 6.67vw;
  height: 1.78vw;
  margin: 1.56vw 0 0 2.08vw;
}

.label_6 {
  width: 1.78vw;
  height: 1.78vw;
}

.text-group_3 {
  width: 4.17vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.21vw;
}

.image_7 {
  width: 2.66vw;
  height: 2.66vw;
  margin: 0.98vw 2.76vw 0 11.66vw;
}

.text-wrapper_5 {
  width: 19.9vw;
  height: 1.72vw;
  margin: 2.39vw 0 0 2.91vw;
}

.text_15 {
  width: 7.35vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_16 {
  width: 8.29vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.image_8 {
  width: 21.25vw;
  height: 0.16vw;
  margin: 0.57vw 0 0 2.5vw;
}

.text-wrapper_6 {
  width: 21.41vw;
  height: 1.72vw;
  margin: 0.67vw 0 1.71vw 2.96vw;
}

.text_17 {
  width: 7.4vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_18 {
  width: 11.46vw;
  height: 1.62vw;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.11vw;
}
