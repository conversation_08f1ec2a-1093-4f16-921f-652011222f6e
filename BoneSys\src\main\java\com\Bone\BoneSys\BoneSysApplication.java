package com.Bone.BoneSys;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * FREEBONE医疗系统主应用类
 * 
 * 系统功能：
 * - 患者档案管理
 * - 治疗参数设置
 * - 治疗头设备管理
 * - 治疗进程控制
 * - 串口硬件通信
 */
@SpringBootApplication
@EnableConfigurationProperties
@EnableScheduling
public class BoneSysApplication {

	public static void main(String[] args) {
		SpringApplication.run(BoneSysApplication.class, args);
	}

}
