.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.group_1 {
  height: 56.15vw;
  background: url(./img/4da74a82a76211bac6221a1e0edabddc.png) -0.16vw -0.11vw
    no-repeat;
  background-size: 100vw 56.25vw;
  width: 99.85vw;
}

.box_1 {
  width: 100vw;
  height: 56.25vw;
  background: url(./img/c30b12fbf3037e890c790dcd36bdf001.png) -0.16vw -0.11vw
    no-repeat;
  background-size: 100.15vw 56.35vw;
}

.block_1 {
  width: 52.3vw;
  height: 3.65vw;
  margin: 0.93vw 0 0 5.2vw;
}

.image_1 {
  width: 8.81vw;
  height: 3.65vw;
}

.text_1 {
  width: 15.37vw;
  height: 2.56vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.53vw;
}

.block_2 {
  width: 84.74vw;
  height: 14.33vw;
  margin: 3.64vw 0 0 7.08vw;
}

.box_2 {
  width: 49.02vw;
  height: 14.22vw;
  background: url(./img/e974f01faef346da53cab0c398e50bea.png)
    0vw -0.06vw no-repeat;
  background-size: 49.06vw 14.27vw;
}

.block_3 {
  background-color: rgba(239, 239, 239, 1);
  border-radius: 50%;
  width: 8.08vw;
  height: 8.08vw;
  border: 1px solid rgba(203, 203, 203, 1);
  margin: 2.23vw 0 0 1.35vw;
}

.block_4 {
  width: 16.72vw;
  height: 8.13vw;
  margin: 1.19vw 0 0 1.25vw;
}

.box_3 {
  width: 16.72vw;
  height: 2.56vw;
}

.text_2 {
  width: 3.34vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-top: 0.73vw;
}

.box_4 {
  border-radius: 10px;
  width: 1.15vw;
  height: 2.56vw;
  margin-left: 0.06vw;
}

.text_3 {
  width: 3.34vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-top: 0.73vw;
}

.text_4 {
  width: 3.55vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 0.72vw 0 0 5.31vw;
}

.text-wrapper_1 {
  width: 16.52vw;
  height: 1.31vw;
  margin-top: 1.1vw;
}

.text_5 {
  width: 3.34vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
}

.text_6 {
  width: 2.45vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 0.05vw 0 0 1.14vw;
}

.text_7 {
  width: 3.29vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-left: 6.31vw;
}

.text-wrapper_2 {
  width: 13.39vw;
  height: 1.31vw;
  margin-top: 1.88vw;
}

.text_8 {
  width: 6.62vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
}

.text_9 {
  width: 5.63vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-top: 0.06vw;
}

.block_5 {
  width: 19.17vw;
  height: 11.46vw;
  margin: 1.92vw 1.4vw 0 1.04vw;
}

.text-wrapper_3 {
  width: 14.33vw;
  height: 1.31vw;
}

.text_10 {
  width: 1.2vw;
  height: 1.25vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-top: 0.06vw;
}

.text_11 {
  width: 3.55vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin-left: 6.72vw;
}

.text_12 {
  width: 1.72vw;
  height: 1.05vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 0.15vw 0 0 1.14vw;
}

.text_13 {
  width: 8.6vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 1.66vw 0 0 0.05vw;
}

.section_1 {
  height: 3.6vw;
  background: url(./img/8868f2acf37f3079fab139b3ff5da01f.png) -0.53vw
    0vw no-repeat;
  background-size: 13.12vw 4.79vw;
  width: 12.09vw;
  margin: 3.8vw 0 0 7.08vw;
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 3.34vw;
  width: 11.78vw;
  margin: 0.15vw 0 0 0.15vw;
}

.text_14 {
  width: 8.91vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 0.83vw 0 0 1.35vw;
}

.box_5 {
  height: 14.33vw;
  background: url(./img/e19c135acf78908ffc6870560b6ac03d.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 33.13vw;
}

.text-wrapper_5 {
  width: 24.38vw;
  height: 1.36vw;
  margin: 1.97vw 0 0 3.54vw;
}

.text_15 {
  width: 4.8vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.06vw;
}

.text_16 {
  width: 5.21vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.1vw 0 0 0.88vw;
}

.text_17 {
  width: 3.34vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 3.96vw;
}

.text_18 {
  width: 5.21vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.1vw 0 0 0.98vw;
}

.text-wrapper_6 {
  width: 24.38vw;
  height: 1.31vw;
  margin: 1.56vw 0 0 3.54vw;
}

.text_19 {
  width: 4.8vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
}

.text_20 {
  width: 5.21vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.05vw 0 0 0.88vw;
}

.text_21 {
  width: 3.34vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 3.96vw;
}

.text_22 {
  width: 5.21vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.05vw 0 0 0.98vw;
}

.text-wrapper_7 {
  width: 24.38vw;
  height: 1.52vw;
  margin: 1.56vw 0 0 3.54vw;
}

.text_23 {
  width: 6.25vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-top: 0.21vw;
}

.text_24 {
  width: 4.33vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.36vw 0 0 1.19vw;
}

.text_25 {
  width: 3.34vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin-left: 3.13vw;
}

.text_26 {
  width: 5.21vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 8.08vw;
  margin: 0.05vw 0 0 0.93vw;
}

.group_2 {
  width: 12.09vw;
  height: 2.4vw;
  margin: 1.61vw 0 1.04vw 19.63vw;
}

.text-wrapper_8 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  height: 2.61vw;
  border: 2px solid rgba(245, 245, 245, 0.1);
  width: 12.3vw;
  margin: -0.1vw 0 0 -0.1vw;
}

.text_27 {
  width: 10.16vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.84vw;
  margin: 0.62vw 0 0 1.3vw;
}

.block_6 {
  position: relative;
  width: 87.97vw;
  height: 29.9vw;
  background: url(./img/89849cff18b90a27d9b139bda364e8ce.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 1.56vw 0 2.23vw 5.67vw;
}

.text-wrapper_9 {
  width: 75.21vw;
  height: 1.72vw;
  margin: 3.8vw 0 0 7.13vw;
}

.text_28 {
  width: 7.45vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_29 {
  width: 3.44vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 6.36vw;
}

.text_30 {
  width: 3.29vw;
  height: 1.67vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.05vw 0 0 6.14vw;
}

.text_31 {
  width: 7.45vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 4.85vw;
}

.text_32 {
  width: 7.45vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 4.64vw;
}

.text_33 {
  width: 9.33vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 3.44vw;
}

.text_34 {
  width: 7.4vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 4.02vw;
}

.image_2 {
  width: 76.88vw;
  height: 0.06vw;
  margin: 0.88vw 0 0 5.88vw;
}

.block_7 {
  width: 72.09vw;
  height: 2.14vw;
  margin: 0.83vw 0 0 8.59vw;
}

.text_35 {
  width: 4.69vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.63vw;
}

.text_36 {
  width: 5.58vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.57vw 0 0 6.66vw;
}

.text_37 {
  width: 5.47vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.62vw 0 0 3.8vw;
}

.text_38 {
  width: 4.17vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.57vw 0 0 5.46vw;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 2.14vw;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 5vw;
  width: 8.75vw;
}

.box_6 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  width: 6.1vw;
  height: 2.14vw;
  border: 0.5px solid rgba(191, 191, 191, 1);
  margin-left: 3.49vw;
}

.text_39 {
  width: 1.2vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.62vw 0 0 7.44vw;
}

.text_40 {
  width: 4.07vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.62vw 0 0 9.79vw;
}

.image_3 {
  width: 76.88vw;
  height: 0.06vw;
  margin: 0.67vw 0 0 5.88vw;
}

.block_8 {
  width: 72.09vw;
  height: 2.14vw;
  margin: 0.72vw 0 0 8.59vw;
}

.text_41 {
  width: 4.69vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.58vw;
}

.text_42 {
  width: 5.58vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 6.66vw;
}

.text_43 {
  width: 5.47vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.57vw 0 0 3.8vw;
}

.text_44 {
  width: 4.17vw;
  height: 1.31vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 5.46vw;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 2.14vw;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 4.95vw;
  width: 8.75vw;
  position: relative;
}

.text_45 {
  width: 2.19vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 6.62vw;
  margin: 0.46vw 0 0 0.72vw;
}

.box_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  height: 2.14vw;
  border: 0.5px solid rgba(191, 191, 191, 1);
  width: 6.1vw;
  position: absolute;
  left: 3.49vw;
  top: 0;
}

.text-wrapper_10 {
  width: 5.32vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 5.32vw;
  margin: 0.41vw 0 0 0.41vw;
}

.text_46 {
  width: 5.32vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 5.32vw;
}

.text_47 {
  width: 5.32vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 5.32vw;
}

.text_48 {
  width: 1.2vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.57vw 0 0 7.5vw;
}

.text_49 {
  width: 4.07vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.57vw 0 0 9.79vw;
}

.image-wrapper_1 {
  height: 0.06vw;
  background: url(./img/3d94914d453f437746263c64a7c4d7d9.png)
    0vw -0.06vw no-repeat;
  background-size: 76.87vw 0.1vw;
  width: 76.88vw;
  margin: 0.78vw 0 0 5.88vw;
}

.image_4 {
  width: 76.88vw;
  height: 0.06vw;
}

.image-wrapper_2 {
  height: 0.06vw;
  background: url(./img/efc88efd533cf9bdf65716324b2cf8b9.png)
    0vw -0.06vw no-repeat;
  background-size: 76.87vw 0.1vw;
  width: 76.88vw;
  margin: 3.64vw 0 0 5.88vw;
}

.image_5 {
  width: 76.88vw;
  height: 0.06vw;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 2.97vw;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 13.55vw;
  margin: 3.8vw 0 5.57vw 70.98vw;
}

.image_6 {
  width: 12.24vw;
  height: 1.98vw;
  margin: 0.46vw 0 0 0.52vw;
}

.block_9 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 2.14vw;
  border: 0.5px solid rgba(159, 159, 160, 1);
  width: 8.75vw;
  position: absolute;
  left: 49.38vw;
  top: 7.35vw;
}

.text_50 {
  width: 2.19vw;
  height: 1.1vw;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 6.62vw;
  margin: 0.46vw 0 0 0.72vw;
}

.section_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  height: 2.14vw;
  border: 0.5px solid rgba(191, 191, 191, 1);
  width: 6.1vw;
  position: absolute;
  left: 3.49vw;
  top: 0;
}

.text-wrapper_11 {
  width: 5.32vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 5.32vw;
  margin: 0.41vw 0 0 0.41vw;
}

.text_51 {
  width: 5.32vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 5.32vw;
}

.text_52 {
  width: 5.32vw;
  height: 1.2vw;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 1.3vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 5.32vw;
}
