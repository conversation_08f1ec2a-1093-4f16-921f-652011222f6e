html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.section_1 {
  height: 28.8rem;
  background: url(./img/6ce9f9861ced27679296af9c9ac6c482.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 51.2rem;
}

.section_2 {
  width: 35.174rem;
  height: 20.774rem;
  margin: 3.094rem 0 0 8.027rem;
}

.box_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  width: 35.174rem;
  height: 20.774rem;
  border: 1px solid rgba(96, 96, 96, 1);
}

.image_1 {
  width: 18.054rem;
  height: 2.907rem;
  margin: 2.427rem 0 0 3.6rem;
}

.text-group_1 {
  width: 6.774rem;
  height: 2.694rem;
  margin: 1.627rem 0 0 3.84rem;
}

.text-wrapper_1 {
  width: 4.187rem;
  height: 1.147rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: ArialMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
  margin-left: 0.08rem;
}

.text_1 {
  width: 4.187rem;
  height: 1.147rem;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 1.333rem;
  font-family: ArialMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.text_2 {
  width: 4.187rem;
  height: 1.147rem;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 1.333rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.827rem;
}

.text_3 {
  width: 6.774rem;
  height: 1.014rem;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 1.333rem;
  font-family: ArialMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 0.774rem;
  margin-top: 0.534rem;
}

.section_3 {
  width: 16.454rem;
  height: 1.84rem;
  margin: 5.04rem 0 4.24rem 9.334rem;
}

.block_1 {
  width: 8.88rem;
  height: 1.494rem;
  background: url(./img/62c8f553b93483119365b407a30a731f.png) -0.24rem
    0rem no-repeat;
  background-size: 9.387rem 2.08rem;
  margin-top: 0.187rem;
}

.label_1 {
  width: 0.854rem;
  height: 0.96rem;
  margin: 0.24rem 0 0 0.88rem;
}

.box_2 {
  width: 4.72rem;
  height: 0.667rem;
  margin: 0.427rem 0 0 0.64rem;
}

.text_4 {
  width: 3.84rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(116, 116, 116, 1);
  font-size: 0.533rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 0.134rem;
  margin-left: 0.56rem;
}

.image_2 {
  width: 4.72rem;
  height: 0.027rem;
  margin-top: 0.08rem;
}

.label_2 {
  width: 0.64rem;
  height: 0.56rem;
  margin: 0.454rem 0.614rem 0 0.534rem;
}

.block_2 {
  height: 1.84rem;
  background: url(./img/8b5f27b4d61eee989b13cfe693bdf49d.png) -0.24rem
    0rem no-repeat;
  background-size: 6.72rem 2.454rem;
  width: 6.187rem;
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  width: 6.027rem;
  height: 1.707rem;
  margin: 0.08rem 0 0 0.08rem;
}

.image-wrapper_1 {
  height: 0.987rem;
  background: url(./img/680c683b8cfaea5137d473c73bd994eb.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 0.934rem;
  margin: 0.347rem 0 0 1.147rem;
}

.image_3 {
  width: 0.987rem;
  height: 0.534rem;
  margin: 0.214rem 0 0 -0.187rem;
}

.text_5 {
  width: 1.787rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.72rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.48rem 1.334rem 0 0.827rem;
}

.image-wrapper_2 {
  width: 3.68rem;
  height: 1.334rem;
  margin: 2.427rem 0 1.174rem 45.547rem;
}

.image_4 {
  width: 3.68rem;
  height: 1.334rem;
}
