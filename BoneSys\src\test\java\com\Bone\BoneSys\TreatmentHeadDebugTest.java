package com.Bone.BoneSys;

import com.Bone.BoneSys.dto.hardware.TreatmentHeadInfo;
import com.Bone.BoneSys.service.HardwareService;
import com.Bone.BoneSys.service.TreatmentHeadRecommendationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

@SpringBootTest
@TestPropertySource(properties = {
    "hardware.simulator.enabled=true"
})
public class TreatmentHeadDebugTest {
    
    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private TreatmentHeadRecommendationService recommendationService;
    
    @Test
    public void debugTreatmentHeadAvailability() throws Exception {
        System.out.println("=== 调试治疗头可用性 ===");
        
        // 1. 同步治疗头数据
        List<TreatmentHeadInfo> heads = hardwareService.syncAllTreatmentHeads();
        System.out.println("同步到的治疗头数量: " + heads.size());
        
        // 2. 检查前10个治疗头的详细信息
        System.out.println("\n前10个治疗头详细信息:");
        for (int i = 0; i < Math.min(10, heads.size()); i++) {
            TreatmentHeadInfo head = heads.get(i);
            System.out.println(String.format("治疗头%d: 编号=%d, 电量=%d%%, 状态=%s, 仓位=%s", 
                i+1, head.getHeadNumber(), head.getBatteryLevel(), head.getStatus(), head.getCompartmentType()));
        }
        
        // 3. 统计可用的治疗头
        long shallowAvailable = heads.stream()
            .filter(head -> "SHALLOW".equals(head.getCompartmentType()))
            .filter(head -> isHeadAvailable(head))
            .count();
            
        long deepAvailable = heads.stream()
            .filter(head -> "DEEP".equals(head.getCompartmentType()))
            .filter(head -> isHeadAvailable(head))
            .count();
            
        System.out.println(String.format("\n可用治疗头统计: 浅部=%d, 深部=%d", shallowAvailable, deepAvailable));
        
        // 4. 检查不可用的治疗头原因
        System.out.println("\n不可用治疗头分析:");
        heads.stream()
            .filter(head -> !isHeadAvailable(head))
            .limit(5)
            .forEach(head -> {
                System.out.println(String.format("治疗头%d不可用: 电量=%d%%, 状态=%s (需要状态为CHARGING且电量>60%%或CHARGED且电量=100%%)", 
                    head.getHeadNumber(), head.getBatteryLevel(), head.getStatus()));
            });
    }
    
    private boolean isHeadAvailable(TreatmentHeadInfo head) {
        String status = head.getStatus();
        int batteryLevel = head.getBatteryLevel();
        
        // 充电中且电量大于60%
        if ("CHARGING".equals(status) && batteryLevel > 60) {
            return true;
        }
        
        // 充电完成且电量为100%
        if ("CHARGED".equals(status) && batteryLevel == 100) {
            return true;
        }
        
        return false;
    }
}