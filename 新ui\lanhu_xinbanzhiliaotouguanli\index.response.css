.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.image-wrapper_1 {
  height: 56.25vw;
  background: url(./img/a05eb3d383cf70e0da6d25beca0e335d.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 0.16vw;
  width: 99.85vw;
}

.image_1 {
  width: 99.48vw;
  height: 51.72vw;
  margin: 4.53vw 0 0 0.36vw;
}

.box_1 {
  height: 56.25vw;
  background: url(./img/a64bc209b8c81865ebcc45621fe19bbe.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 99.95vw;
  position: absolute;
  left: 0.06vw;
  top: 0;
}

.group_1 {
  width: 54.38vw;
  height: 3.65vw;
  margin: 1.04vw 0 0 5.46vw;
}

.image_2 {
  width: 8.81vw;
  height: 3.65vw;
}

.text_1 {
  width: 19.59vw;
  height: 2.61vw;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 2.6vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.53vw;
}

.group_2 {
  position: relative;
  width: 96.05vw;
  height: 42.3vw;
  margin: 5.1vw 0 0 1.71vw;
}

.box_2 {
  width: 79.8vw;
  height: 42.3vw;
  background: url(./img/7165f76fa0b053e05201a568aa4fc31f.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 8.18vw;
}

.text-wrapper_1 {
  width: 70.32vw;
  height: 1.72vw;
  margin: 6.04vw 0 0 6.71vw;
}

.text_2 {
  width: 3.91vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
}

.text_3 {
  width: 3.91vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 8.49vw;
}

.text_4 {
  width: 3.91vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 8.91vw;
}

.text_5 {
  width: 6.31vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 5vw;
}

.text_6 {
  width: 8.08vw;
  height: 1.67vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 5.73vw;
}

.text_7 {
  width: 10.27vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-left: 5.84vw;
}

.image_3 {
  width: 71.98vw;
  height: 0.06vw;
  margin: 0.88vw 0 0 4.37vw;
}

.box_3 {
  width: 65.53vw;
  height: 2.19vw;
  margin: 0.62vw 0 0 7.5vw;
}

.text_8 {
  width: 2.04vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.42vw;
}

.image_4 {
  width: 3.03vw;
  height: 1.72vw;
  margin: 0.36vw 0 0 4.37vw;
}

.text_9 {
  width: 8.18vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.31vw 0 0 2.08vw;
}

.text_10 {
  width: 0.84vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.41vw 0 0 6.35vw;
}

.text_11 {
  width: 0.79vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.41vw 0 0 9.27vw;
}

.text_12 {
  width: 5.58vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.41vw 0 0 9.73vw;
}

.label_1 {
  width: 2.19vw;
  height: 2.19vw;
  margin-left: 11.1vw;
}

.image_5 {
  width: 71.98vw;
  height: 0.06vw;
  margin: 0.83vw 0 0 4.37vw;
}

.box_4 {
  width: 65.53vw;
  height: 2.35vw;
  margin: 0.83vw 0 0 7.5vw;
}

.text_13 {
  width: 2.35vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.37vw;
}

.image_6 {
  width: 2.97vw;
  height: 2.35vw;
  margin-left: 4.07vw;
}

.text_14 {
  width: 5.89vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.26vw 0 0 3.22vw;
}

.text_15 {
  width: 0.99vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.31vw 0 0 7.44vw;
}

.text_16 {
  width: 0.99vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.31vw 0 0 9.11vw;
}

.text_17 {
  width: 5.58vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.52vw 0 0 9.63vw;
}

.label_2 {
  width: 2.19vw;
  height: 2.19vw;
  margin-left: 11.1vw;
}

.image-wrapper_2 {
  height: 0.06vw;
  background: url(./img/5ff61b1d884414dd419a6d37f974583e.png)
    0vw -0.06vw no-repeat;
  background-size: 71.97vw 0.1vw;
  width: 71.98vw;
  margin: 0.46vw 0 0 4.37vw;
}

.image_7 {
  width: 71.98vw;
  height: 0.06vw;
}

.box_5 {
  width: 65.53vw;
  height: 2.19vw;
  margin: 0.83vw 0 0 7.5vw;
}

.text_18 {
  width: 2.3vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin-top: 0.32vw;
}

.label_3 {
  width: 2.3vw;
  height: 2.09vw;
  margin-left: 4.12vw;
}

.text_19 {
  width: 5.89vw;
  height: 1.72vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.2vw 0 0 3.9vw;
}

.text_20 {
  width: 0.53vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.26vw 0 0 7.6vw;
}

.text_21 {
  width: 0.53vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.26vw 0 0 9.58vw;
}

.text_22 {
  width: 5.58vw;
  height: 1.41vw;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.14vw;
  margin: 0.31vw 0 0 9.94vw;
}

.label_4 {
  width: 2.19vw;
  height: 2.19vw;
  margin-left: 11.1vw;
}

.image-wrapper_3 {
  height: 0.06vw;
  background: url(./img/372e95f51139b9c892d32fe3b3ae2b6e.png)
    0vw -0.06vw no-repeat;
  background-size: 71.97vw 0.1vw;
  width: 71.98vw;
  margin: 0.62vw 0 22.5vw 4.37vw;
}

.image_8 {
  width: 71.98vw;
  height: 0.06vw;
}

.image_9 {
  width: 8.81vw;
  height: 8.96vw;
  margin: 14.84vw 0 0 -0.72vw;
}

.image_10 {
  position: absolute;
  left: 0;
  top: 14.85vw;
  width: 8.81vw;
  height: 8.96vw;
}

.text-wrapper_2 {
  width: 3.18vw;
  height: 1.36vw;
  margin: 0.1vw 0 2.7vw 49.06vw;
}

.text_23 {
  width: 3.18vw;
  height: 1.36vw;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 1.4vw;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 2.56vw;
}
