-- ====================================================================================
-- FREEBONE医疗系统数据库表结构创建脚本 (增强版)
-- 更新说明：
-- 1. 用户表采用明文密码存储，统一为123456
-- 2. 增加参数设置表，支持治疗参数档位配置
-- 3. 治疗强度档位：30、45、60 (mW/cm²)
-- 4. 治疗频率档位：100Hz、1000Hz
-- 5. 治疗时长范围：5-60分钟
-- 6. 完整的UTF-8编码支持
-- ====================================================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `bonesys` 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

-- 确保当前操作的数据库是 `bonesys`
USE `bonesys`;

-- -----------------------------------------------------
-- 表 1: `users` (用户表) - 简化版明文密码
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`users` (
  `id` INT NOT NULL,
  `username` VARCHAR(50) NOT NULL,
  `password` VARCHAR(50) NOT NULL DEFAULT '123456' COMMENT '用户密码（明文存储）',
  `last_updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `username_UNIQUE` (`username` ASC) VISIBLE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '用户表，单行存储默认用户和密码信息';

-- -----------------------------------------------------
-- 表 2: `system_parameters` (系统参数表) - 新增
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`system_parameters` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `parameter_category` VARCHAR(50) NOT NULL COMMENT '参数分类：TREATMENT, DEVICE, SERIAL, HARDWARE',
  `parameter_key` VARCHAR(100) NOT NULL COMMENT '参数键名',
  `parameter_value` TEXT NOT NULL COMMENT '参数值（JSON格式）',
  `parameter_type` VARCHAR(20) NOT NULL DEFAULT 'STRING' COMMENT '参数类型：STRING, INTEGER, DOUBLE, BOOLEAN, JSON',
  `description` VARCHAR(255) NULL COMMENT '参数描述',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `category_key_UNIQUE` (`parameter_category` ASC, `parameter_key` ASC) VISIBLE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '系统参数配置表';

-- -----------------------------------------------------
-- 表 3: `treatment_parameter_presets` (治疗参数预设表) - 新增
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`treatment_parameter_presets` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `preset_name` VARCHAR(100) NOT NULL COMMENT '预设名称',
  `body_part` VARCHAR(50) NOT NULL COMMENT '治疗部位',
  `default_duration` INT NOT NULL DEFAULT 20 COMMENT '默认治疗时长（分钟）',
  `default_intensity` INT NOT NULL DEFAULT 45 COMMENT '默认治疗强度档位（30/45/60）',
  `default_frequency` INT NOT NULL DEFAULT 1000 COMMENT '默认治疗频率（100/1000Hz）',
  `patch_type` VARCHAR(20) NOT NULL DEFAULT 'SHALLOW' COMMENT '贴片类型：SHALLOW（浅部）, DEEP（深部）',
  `recommended_count` INT NOT NULL DEFAULT 1 COMMENT '推荐贴片数量（1-4）',
  `is_default` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为默认预设',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `preset_body_part_UNIQUE` (`preset_name` ASC, `body_part` ASC) VISIBLE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '治疗参数预设配置表';

-- -----------------------------------------------------
-- 表 4: `patients` (患者表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`patients` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `patient_card_id` VARCHAR(50) NOT NULL COMMENT '就诊卡号',
  `name` VARCHAR(100) NOT NULL COMMENT '患者姓名',
  `gender` VARCHAR(10) NULL DEFAULT NULL,
  `age` VARCHAR(10) NULL DEFAULT NULL,
  `contact_info` VARCHAR(255) NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `patient_card_id_UNIQUE` (`patient_card_id` ASC) VISIBLE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '患者基本信息表';

-- -----------------------------------------------------
-- 表 5: `records` (档案表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`records` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_number` VARCHAR(50) NOT NULL COMMENT '档案唯一编号',
  `patient_id` BIGINT NOT NULL,
  `diagnosis_description` TEXT NULL DEFAULT NULL,
  `sessions_completed_count` INT NOT NULL DEFAULT 0 COMMENT '已完成的总会话次数',
  `created_at` DATE NOT NULL COMMENT '建档日期',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `record_number_UNIQUE` (`record_number` ASC) VISIBLE,
  INDEX `fk_records_patients_idx` (`patient_id` ASC) VISIBLE,
  CONSTRAINT `fk_records_patients`
    FOREIGN KEY (`patient_id`)
    REFERENCES `bonesys`.`patients` (`id`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '诊断档案信息表';

-- -----------------------------------------------------
-- 表 6: `treatment_heads` (治疗头管理表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`treatment_heads` (
  `head_id` BIGINT NOT NULL AUTO_INCREMENT,
  `head_number` INT NOT NULL COMMENT '治疗头编号 (1-20)',
  `slot_number` INT NULL DEFAULT NULL COMMENT '槽位号 (1-10浅部上仓, 11-20深部下仓)',
  `light_color` INT NOT NULL DEFAULT 0 COMMENT '指示灯颜色 (0=关闭, 1=橙色, 2=蓝色, 3=绿色)',
  `realtime_status` VARCHAR(20) NOT NULL DEFAULT 'CHARGING' COMMENT '实时状态: CHARGING(充电中), CHARGED(充电完成), TREATING(治疗中)',
  `battery_level` INT NULL DEFAULT NULL COMMENT '实时电量 (0-100)',
  `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT '累计总使用次数',
  `total_usage_minutes` INT NOT NULL DEFAULT 0 COMMENT '累计总使用时长（分钟）',
  `max_usage_count` INT NOT NULL DEFAULT 500 COMMENT '设计的总可用次数',
  PRIMARY KEY (`head_id`),
  UNIQUE INDEX `head_number_UNIQUE` (`head_number` ASC) VISIBLE,
  UNIQUE INDEX `slot_number_UNIQUE` (`slot_number` ASC) VISIBLE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '治疗头设备管理表 - 支持20个治疗头，上下两仓设计，三种状态';

-- -----------------------------------------------------
-- 表 7: `processes` (进程表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`processes` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_id` BIGINT NOT NULL,
  `treatment_mode` VARCHAR(20) NOT NULL COMMENT '治疗模式: ON_SITE, TAKE_AWAY',
  `status` VARCHAR(20) NOT NULL COMMENT '进程状态: IN_PROGRESS, COMPLETED, CANCELLED',
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_processes_records_idx` (`record_id` ASC) VISIBLE,
  CONSTRAINT `fk_processes_records`
    FOREIGN KEY (`record_id`)
    REFERENCES `bonesys`.`records` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '单次治疗进程表';

-- -----------------------------------------------------
-- 表 8: `treatment_details` (治疗详情表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`treatment_details` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `process_id` BIGINT NOT NULL,
  `head_number_used` INT NOT NULL COMMENT '使用的治疗头编号 (1-20)',
  `body_part` VARCHAR(50) NOT NULL COMMENT '肩颈部、腰背部、髋部、上肢、下肢、其他部位（可编辑）',
  `duration` INT NOT NULL COMMENT '治疗时长（分钟）',
  `intensity` DECIMAL(5,2) NOT NULL COMMENT '治疗声强 (mW/cm²)',
  `frequency` INT NOT NULL COMMENT '脉冲频率(Hz)',
  `patch_type` VARCHAR(20) NOT NULL COMMENT '贴片类型: DEEP (深部), SHALLOW (浅层)',
  `patch_quantity` INT NOT NULL COMMENT '贴片数量 (1-4)',
  `status` VARCHAR(20) NOT NULL COMMENT '详情状态: TREATING, COMPLETED, AWAITING_RETURN, RETURNED, TERMINATED',
  PRIMARY KEY (`id`),
  INDEX `fk_details_processes_idx` (`process_id` ASC) VISIBLE,
  INDEX `fk_details_heads_idx` (`head_number_used` ASC) VISIBLE,
  CONSTRAINT `fk_details_processes`
    FOREIGN KEY (`process_id`)
    REFERENCES `bonesys`.`processes` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
  CONSTRAINT `fk_details_heads`
    FOREIGN KEY (`head_number_used`)
    REFERENCES `bonesys`.`treatment_heads` (`head_number`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '单次进程中的具体治疗项';

-- -----------------------------------------------------
-- 表 9: `body_part_stats` (部位统计表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`body_part_stats` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_id` BIGINT NOT NULL,
  `body_part` VARCHAR(50) NOT NULL,
  `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT '该部位累计使用次数',
  `total_duration_minutes` INT NOT NULL DEFAULT 0 COMMENT '该部位累计使用时长（分钟）',
  PRIMARY KEY (`id`),
  INDEX `fk_stats_records_idx` (`record_id` ASC) VISIBLE,
  UNIQUE INDEX `record_body_part_UNIQUE` (`record_id` ASC, `body_part` ASC) VISIBLE,
  CONSTRAINT `fk_stats_records`
    FOREIGN KEY (`record_id`)
    REFERENCES `bonesys`.`records` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
ENGINE = InnoDB
DEFAULT CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci
COMMENT = '档案下各部位的累计统计表';

-- -----------------------------------------------------
-- 初始化数据
-- -----------------------------------------------------

-- 插入默认用户数据（明文密码123456）
INSERT INTO `bonesys`.`users` (`id`, `username`, `password`)
VALUES (1, 'admin', '123456')
ON DUPLICATE KEY UPDATE
  `password` = VALUES(`password`);

-- 插入系统参数配置
INSERT INTO `bonesys`.`system_parameters` (`parameter_category`, `parameter_key`, `parameter_value`, `parameter_type`, `description`) VALUES
('TREATMENT', 'default_duration', '20', 'INTEGER', '默认治疗时长（分钟）'),
('TREATMENT', 'default_intensity', '45', 'INTEGER', '默认治疗强度档位'),
('TREATMENT', 'default_frequency', '1000', 'INTEGER', '默认治疗频率（Hz）'),
('TREATMENT', 'intensity_options', '[30, 45, 60]', 'JSON', '可选治疗强度档位'),
('TREATMENT', 'frequency_options', '[100, 1000]', 'JSON', '可选治疗频率'),
('TREATMENT', 'duration_range', '{"min": 5, "max": 60}', 'JSON', '治疗时长范围'),
('TREATMENT', 'max_usage_count', '500', 'INTEGER', '治疗头最大使用次数'),
('TREATMENT', 'low_battery_threshold', '20', 'INTEGER', '低电量阈值（%）'),
('DEVICE', 'total_treatment_heads', '20', 'INTEGER', '治疗头总数'),
('DEVICE', 'shallow_compartment_range', '{"start": 1, "end": 10}', 'JSON', '浅部仓位范围'),
('DEVICE', 'deep_compartment_range', '{"start": 11, "end": 20}', 'JSON', '深部仓位范围'),
('SERIAL', 'port_name', 'COM1', 'STRING', '串口名称'),
('SERIAL', 'baud_rate', '115200', 'INTEGER', '波特率'),
('SERIAL', 'timeout', '5000', 'INTEGER', '超时时间（毫秒）')
ON DUPLICATE KEY UPDATE
  `parameter_value` = VALUES(`parameter_value`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 插入治疗参数预设
INSERT INTO `bonesys`.`treatment_parameter_presets` (`preset_name`, `body_part`, `default_duration`, `default_intensity`, `default_frequency`, `patch_type`, `recommended_count`, `is_default`) VALUES
('标准预设', '肩颈部', 20, 45, 1000, 'SHALLOW', 2, TRUE),
('标准预设', '腰背部', 25, 60, 100, 'DEEP', 2, TRUE),
('标准预设', '髋部', 20, 45, 1000, 'DEEP', 1, TRUE),
('标准预设', '上肢', 15, 30, 1000, 'SHALLOW', 1, TRUE),
('标准预设', '下肢', 20, 45, 100, 'DEEP', 2, TRUE),
('标准预设', '其他部位', 15, 30, 1000, 'SHALLOW', 1, TRUE),
('强化预设', '肩颈部', 25, 60, 1000, 'SHALLOW', 3, FALSE),
('强化预设', '腰背部', 30, 60, 100, 'DEEP', 3, FALSE),
('温和预设', '肩颈部', 15, 30, 1000, 'SHALLOW', 1, FALSE),
('温和预设', '腰背部', 20, 30, 100, 'DEEP', 1, FALSE)
ON DUPLICATE KEY UPDATE
  `default_duration` = VALUES(`default_duration`),
  `default_intensity` = VALUES(`default_intensity`),
  `default_frequency` = VALUES(`default_frequency`);

-- 插入20个治疗头数据（三种状态分布）
INSERT INTO `bonesys`.`treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`)
VALUES
  -- 上仓浅部治疗头 (槽号1-10)
  (1, 1, 0, 'CHARGING', 50, 5, 100, 500),
  (2, 2, 0, 'CHARGING', 60, 4, 80, 500),
  (3, 3, 0, 'CHARGED', 100, 10, 200, 500),
  (4, 4, 0, 'CHARGED', 100, 8, 160, 500),
  (5, 5, 0, 'CHARGING', 70, 3, 60, 500),
  (6, 6, 0, 'CHARGED', 100, 12, 240, 500),
  (7, 7, 0, 'TREATING', 85, 6, 120, 500),
  (8, 8, 0, 'CHARGED', 100, 9, 180, 500),
  (9, 9, 0, 'CHARGING', 80, 7, 140, 500),
  (10, 10, 0, 'CHARGED', 100, 5, 100, 500),

  -- 下仓深部治疗头 (槽号11-20)
  (11, 11, 0, 'CHARGING', 65, 8, 160, 500),
  (12, 12, 0, 'CHARGED', 100, 11, 220, 500),
  (13, 13, 0, 'CHARGING', 55, 4, 80, 500),
  (14, 14, 0, 'CHARGED', 100, 6, 120, 500),
  (15, 15, 0, 'TREATING', 90, 9, 180, 500),
  (16, 16, 0, 'CHARGED', 100, 13, 260, 500),
  (17, 17, 0, 'CHARGING', 75, 5, 100, 500),
  (18, 18, 0, 'CHARGED', 100, 10, 200, 500),
  (19, 19, 0, 'CHARGING', 85, 7, 140, 500),
  (20, 20, 0, 'CHARGED', 100, 8, 160, 500)
ON DUPLICATE KEY UPDATE
  `realtime_status` = VALUES(`realtime_status`),
  `battery_level` = VALUES(`battery_level`);

-- -----------------------------------------------------
-- 初始化数据
-- -----------------------------------------------------

-- 插入默认用户数据（明文密码123456）
INSERT INTO `bonesys`.`users` (`id`, `username`, `password`)
VALUES (1, 'admin', '123456')
ON DUPLICATE KEY UPDATE
  `password` = VALUES(`password`);

-- 插入系统参数配置
INSERT INTO `bonesys`.`system_parameters` (`parameter_category`, `parameter_key`, `parameter_value`, `parameter_type`, `description`) VALUES
('TREATMENT', 'default_duration', '20', 'INTEGER', '默认治疗时长（分钟）'),
('TREATMENT', 'default_intensity', '45', 'INTEGER', '默认治疗强度档位'),
('TREATMENT', 'default_frequency', '1000', 'INTEGER', '默认治疗频率（Hz）'),
('TREATMENT', 'intensity_options', '[30, 45, 60]', 'JSON', '可选治疗强度档位'),
('TREATMENT', 'frequency_options', '[100, 1000]', 'JSON', '可选治疗频率'),
('TREATMENT', 'duration_range', '{"min": 5, "max": 60}', 'JSON', '治疗时长范围'),
('TREATMENT', 'max_usage_count', '500', 'INTEGER', '治疗头最大使用次数'),
('TREATMENT', 'low_battery_threshold', '20', 'INTEGER', '低电量阈值（%）'),
('DEVICE', 'total_treatment_heads', '20', 'INTEGER', '治疗头总数'),
('DEVICE', 'shallow_compartment_range', '{"start": 1, "end": 10}', 'JSON', '浅部仓位范围'),
('DEVICE', 'deep_compartment_range', '{"start": 11, "end": 20}', 'JSON', '深部仓位范围'),
('SERIAL', 'port_name', 'COM1', 'STRING', '串口名称'),
('SERIAL', 'baud_rate', '115200', 'INTEGER', '波特率'),
('SERIAL', 'timeout', '5000', 'INTEGER', '超时时间（毫秒）')
ON DUPLICATE KEY UPDATE
  `parameter_value` = VALUES(`parameter_value`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 插入治疗参数预设
INSERT INTO `bonesys`.`treatment_parameter_presets` (`preset_name`, `body_part`, `default_duration`, `default_intensity`, `default_frequency`, `patch_type`, `recommended_count`, `is_default`) VALUES
('标准预设', '肩颈部', 20, 45, 1000, 'SHALLOW', 2, TRUE),
('标准预设', '腰背部', 25, 60, 100, 'DEEP', 2, TRUE),
('标准预设', '髋部', 20, 45, 1000, 'DEEP', 1, TRUE),
('标准预设', '上肢', 15, 30, 1000, 'SHALLOW', 1, TRUE),
('标准预设', '下肢', 20, 45, 100, 'DEEP', 2, TRUE),
('标准预设', '其他部位', 15, 30, 1000, 'SHALLOW', 1, TRUE),
('强化预设', '肩颈部', 25, 60, 1000, 'SHALLOW', 3, FALSE),
('强化预设', '腰背部', 30, 60, 100, 'DEEP', 3, FALSE),
('温和预设', '肩颈部', 15, 30, 1000, 'SHALLOW', 1, FALSE),
('温和预设', '腰背部', 20, 30, 100, 'DEEP', 1, FALSE)
ON DUPLICATE KEY UPDATE
  `default_duration` = VALUES(`default_duration`),
  `default_intensity` = VALUES(`default_intensity`),
  `default_frequency` = VALUES(`default_frequency`);

-- 治疗头数据已在上面插入，此处删除重复内容
