import axios from 'axios';
import { MessagePlugin } from 'tdesign-vue-next';

// 检查是否有有效的JWT Token
export const hasValidToken = (): boolean => {
  const token = localStorage.getItem('jwtToken');
  if (!token) return false;
  
  try {
    // 简单检查token格式（实际应该检查过期时间）
    const parts = token.split('.');
    return parts.length === 3;
  } catch {
    return false;
  }
};

// 自动登录获取Token
export const autoLogin = async (): Promise<boolean> => {
  try {
    // 尝试使用默认密码登录
    const response = await axios.post('/api/auth/login', {
      password: '123456'
    });
    
    if (response.data && response.data.token) {
      localStorage.setItem('jwtToken', response.data.token);
      localStorage.setItem('isAuthenticated', 'true');
      console.log('自动登录成功');
      return true;
    }
  } catch (error) {
    console.warn('自动登录失败，可能需要手动登录:', error);
  }
  
  return false;
};

// 清除认证信息
export const clearAuth = (): void => {
  localStorage.removeItem('jwtToken');
  localStorage.removeItem('isAuthenticated');
};

// 获取当前Token
export const getToken = (): string | null => {
  return localStorage.getItem('jwtToken');
}; 