.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1079px;
  background: url(./img/1a341ef99c616d2adf6f24df400246f3.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1919px;
  margin: 1px 0 0 1px;
}

.group_1 {
  height: 1080px;
  background: url(./img/33e32ea7db779740051708feb5f55caa.png)
    0px -2px no-repeat;
  background-size: 1920px 1082px;
  width: 1920px;
  margin: -1px 0 0 -1px;
}

.section_1 {
  box-shadow: 4px 7px 52px 1px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 16px;
  height: 949px;
  border: 3px solid rgba(92, 212, 200, 1);
  width: 1208px;
  position: relative;
  margin: 63px 0 0 331px;
}

.text-wrapper_1 {
  width: 87px;
  height: 33px;
  margin: 838px 0 0 292px;
}

.text_1 {
  width: 87px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
}

.group_2 {
  position: absolute;
  left: 144px;
  top: 31px;
  width: 1019px;
  height: 811px;
}

.box_2 {
  width: 408px;
  height: 789px;
  margin-top: 22px;
}

.text_2 {
  width: 87px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 145px;
}

.box_3 {
  height: 367px;
  background: url(./img/5152c094067a79cd409c54eef84cf3bc.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 5px;
  width: 408px;
  position: relative;
}

.image-wrapper_1 {
  width: 273px;
  height: 18px;
  margin: 62px 0 0 55px;
}

.image_1 {
  width: 122px;
  height: 18px;
}

.image_2 {
  width: 122px;
  height: 18px;
}

.image-wrapper_2 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_3 {
  width: 122px;
  height: 18px;
}

.image_4 {
  width: 122px;
  height: 18px;
}

.image-wrapper_3 {
  width: 273px;
  height: 21px;
  margin: 32px 0 0 55px;
}

.image_5 {
  width: 122px;
  height: 18px;
  margin-top: 3px;
}

.image_6 {
  width: 122px;
  height: 18px;
}

.image-wrapper_4 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_7 {
  width: 122px;
  height: 18px;
}

.image_8 {
  width: 122px;
  height: 18px;
}

.image-wrapper_5 {
  width: 273px;
  height: 18px;
  margin: 32px 0 98px 55px;
}

.image_9 {
  width: 122px;
  height: 18px;
}

.image_10 {
  width: 122px;
  height: 18px;
}

.image-wrapper_6 {
  height: 50px;
  background: url(./img/d9d24d14a9e926fb56de667c05530e1b.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 122px;
  position: absolute;
  left: 207px;
  top: 29px;
}

.image_11 {
  width: 122px;
  height: 50px;
}

.image_12 {
  position: absolute;
  left: 207px;
  top: 123px;
  width: 122px;
  height: 50px;
}

.image-wrapper_7 {
  position: absolute;
  left: 56px;
  top: 30px;
  width: 122px;
  height: 50px;
  background: url(./img/44e10394a537fdee3a5270878f1c7ba2.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_13 {
  position: absolute;
  left: 0;
  top: 92px;
  width: 122px;
  height: 50px;
}

.image_14 {
  position: absolute;
  left: 1px;
  top: 43px;
  width: 122px;
  height: 50px;
}

.box_4 {
  height: 367px;
  background: url(./img/81af175f3bb76cdbc04c0e0a963c84af.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 18px;
  width: 408px;
  position: relative;
}

.image-wrapper_8 {
  width: 273px;
  height: 18px;
  margin: 62px 0 0 55px;
}

.image_15 {
  width: 122px;
  height: 18px;
}

.image_16 {
  width: 122px;
  height: 18px;
}

.image-wrapper_9 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_17 {
  width: 122px;
  height: 18px;
}

.image_18 {
  width: 122px;
  height: 18px;
}

.image-wrapper_10 {
  width: 273px;
  height: 21px;
  margin: 32px 0 0 55px;
}

.image_19 {
  width: 122px;
  height: 18px;
  margin-top: 3px;
}

.image_20 {
  width: 122px;
  height: 18px;
}

.image-wrapper_11 {
  width: 273px;
  height: 18px;
  margin: 25px 0 0 55px;
}

.image_21 {
  width: 122px;
  height: 18px;
}

.image_22 {
  width: 122px;
  height: 18px;
}

.image-wrapper_12 {
  width: 273px;
  height: 18px;
  margin: 32px 0 98px 55px;
}

.image_23 {
  width: 122px;
  height: 18px;
}

.image_24 {
  width: 122px;
  height: 18px;
}

.image-wrapper_13 {
  height: 50px;
  background: url(./img/1b43bd65afe98bab350d75e7bba53a41.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 122px;
  position: absolute;
  left: 207px;
  top: 29px;
}

.image_25 {
  width: 122px;
  height: 50px;
}

.image_26 {
  position: absolute;
  left: 207px;
  top: 123px;
  width: 122px;
  height: 50px;
}

.image-wrapper_14 {
  position: absolute;
  left: 56px;
  top: 30px;
  width: 122px;
  height: 50px;
  background: url(./img/c49ec2ffeadf1111877902f6f3472ea7.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image_27 {
  position: absolute;
  left: 0;
  top: 92px;
  width: 122px;
  height: 50px;
}

.image_28 {
  position: absolute;
  left: 1px;
  top: 43px;
  width: 122px;
  height: 50px;
}

.box_5 {
  width: 441px;
  height: 678px;
  margin: 69px 0 0 93px;
}

.group_3 {
  width: 441px;
  height: 542px;
  background: url(./img/5f43da0a126981d2eae897b287a64411.png) -9px
    0px no-repeat;
  background-size: 460px 564px;
}

.group_4 {
  width: 298px;
  height: 63px;
  margin: 90px 0 0 70px;
}

.image_29 {
  width: 63px;
  height: 63px;
}

.text_3 {
  width: 205px;
  height: 41px;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 254px;
  margin-top: 11px;
}

.paragraph_1 {
  width: 353px;
  height: 210px;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 59px;
  margin: 82px 0 97px 50px;
}

.group_5 {
  height: 69px;
  background: url(./img/5285ef37f0c7ca55a77fffdac391cdf9.png) -10px
    0px no-repeat;
  background-size: 253px 92px;
  width: 232px;
  margin: 67px 0 0 110px;
}

.text-wrapper_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 3px 0 0 2px;
}

.text_4 {
  width: 93px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin: 16px 0 0 72px;
}

.image_30 {
  width: 60px;
  height: 60px;
  margin-left: 17px;
}
