package com.Bone.BoneSys.util;

import com.Bone.BoneSys.dto.EnhancedCandidateItem;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 数据验证工具类
 * 提供各种数据格式验证和清理功能
 */
@Slf4j
public class DataValidationUtil {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    private static final Pattern CARD_ID_PATTERN = Pattern.compile("^[A-Z]\\d{10}$");
    private static final Pattern AGE_PATTERN = Pattern.compile("^\\d{1,3}(岁)?$");
    
    /**
     * 验证就诊卡号格式
     */
    public static boolean isValidCardId(String cardId) {
        if (cardId == null || cardId.trim().isEmpty()) {
            return false;
        }
        return CARD_ID_PATTERN.matcher(cardId.trim()).matches();
    }
    
    /**
     * 验证患者姓名
     */
    public static boolean isValidName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        String trimmedName = name.trim();
        return trimmedName.length() >= 2 && trimmedName.length() <= 50;
    }
    
    /**
     * 验证年龄格式
     */
    public static boolean isValidAge(String age) {
        if (age == null || age.trim().isEmpty()) {
            return false;
        }
        return AGE_PATTERN.matcher(age.trim()).matches();
    }
    
    /**
     * 验证性别
     */
    public static boolean isValidGender(String gender) {
        if (gender == null || gender.trim().isEmpty()) {
            return false;
        }
        String trimmedGender = gender.trim();
        return "男".equals(trimmedGender) || "女".equals(trimmedGender);
    }
    
    /**
     * 验证日期格式
     */
    public static boolean isValidDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return false;
        }
        try {
            LocalDate.parse(dateStr.trim(), DATE_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
    
    /**
     * 验证治疗次数
     */
    public static boolean isValidSessionCount(Integer sessions) {
        return sessions != null && sessions >= 0 && sessions <= 10000;
    }
    
    /**
     * 清理和验证候选患者数据
     */
    public static EnhancedCandidateItem cleanAndValidate(EnhancedCandidateItem item) {
        if (item == null) {
            return null;
        }
        
        try {
            // 清理数据
            item.setCardId(cleanCardId(item.getCardId()));
            item.setName(cleanName(item.getName()));
            item.setAge(cleanAge(item.getAge()));
            item.setGender(cleanGender(item.getGender()));
            item.setVisitTime(cleanVisitTime(item.getVisitTime()));
            item.setTreatmentParts(cleanTreatmentParts(item.getTreatmentParts()));
            item.setTotalSessions(cleanSessionCount(item.getTotalSessions()));
            
            return item;
        } catch (Exception e) {
            log.warn("清理候选患者数据时发生错误: {}", e.getMessage());
            return item;
        }
    }
    
    /**
     * 清理就诊卡号
     */
    private static String cleanCardId(String cardId) {
        if (cardId == null || cardId.trim().isEmpty()) {
            return "未知";
        }
        String cleaned = cardId.trim().toUpperCase();
        return isValidCardId(cleaned) ? cleaned : cardId.trim();
    }
    
    /**
     * 清理患者姓名
     */
    private static String cleanName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "未知患者";
        }
        String cleaned = name.trim();
        return isValidName(cleaned) ? cleaned : "未知患者";
    }
    
    /**
     * 清理年龄
     */
    private static String cleanAge(String age) {
        if (age == null || age.trim().isEmpty()) {
            return "未知";
        }
        String cleaned = age.trim();
        if (isValidAge(cleaned)) {
            return cleaned.endsWith("岁") ? cleaned : cleaned + "岁";
        }
        return "未知";
    }
    
    /**
     * 清理性别
     */
    private static String cleanGender(String gender) {
        if (gender == null || gender.trim().isEmpty()) {
            return "未知";
        }
        String cleaned = gender.trim();
        return isValidGender(cleaned) ? cleaned : "未知";
    }
    
    /**
     * 清理就诊时间
     */
    private static String cleanVisitTime(String visitTime) {
        if (visitTime == null || visitTime.trim().isEmpty()) {
            return "无档案";
        }
        String cleaned = visitTime.trim();
        return isValidDate(cleaned) ? cleaned : "无档案";
    }
    
    /**
     * 清理治疗部位
     */
    private static String cleanTreatmentParts(String treatmentParts) {
        if (treatmentParts == null || treatmentParts.trim().isEmpty()) {
            return "待确定";
        }
        
        String cleaned = treatmentParts.trim();
        if ("待确定".equals(cleaned) || "无".equals(cleaned)) {
            return "待确定";
        }
        
        // 清理部位列表
        String[] parts = cleaned.split("[,，]");
        List<String> validParts = new ArrayList<>();
        
        for (String part : parts) {
            String trimmedPart = part.trim();
            if (!trimmedPart.isEmpty() && isValidBodyPart(trimmedPart)) {
                validParts.add(trimmedPart);
            }
        }
        
        return validParts.isEmpty() ? "待确定" : String.join(", ", validParts);
    }
    
    /**
     * 验证治疗部位是否有效
     */
    private static boolean isValidBodyPart(String bodyPart) {
        if (bodyPart == null || bodyPart.trim().isEmpty()) {
            return false;
        }
        
        List<String> validParts = List.of(
            "肩颈部", "腰背部", "髋部", "上肢", "下肢", "其他部位",
            "肩部", "颈部", "腰部", "背部", "手臂", "腿部"
        );
        
        return validParts.contains(bodyPart.trim());
    }
    
    /**
     * 清理治疗次数
     */
    private static Integer cleanSessionCount(Integer sessions) {
        if (sessions == null || sessions < 0) {
            return 0;
        }
        return Math.min(sessions, 10000); // 设置合理的上限
    }
    
    /**
     * 批量验证候选患者列表
     */
    public static List<EnhancedCandidateItem> validateCandidateList(List<EnhancedCandidateItem> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<EnhancedCandidateItem> validatedList = new ArrayList<>();
        int invalidCount = 0;
        
        for (EnhancedCandidateItem candidate : candidates) {
            EnhancedCandidateItem cleaned = cleanAndValidate(candidate);
            if (cleaned != null && cleaned.isValid()) {
                validatedList.add(cleaned);
            } else {
                invalidCount++;
                log.warn("发现无效的候选患者数据: {}", candidate);
            }
        }
        
        if (invalidCount > 0) {
            log.warn("共发现 {} 条无效的候选患者数据", invalidCount);
        }
        
        return validatedList;
    }
    
    /**
     * 生成数据质量报告
     */
    public static String generateDataQualityReport(List<EnhancedCandidateItem> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            return "无数据";
        }
        
        int total = candidates.size();
        int withRecords = 0;
        int withTreatments = 0;
        int validCardIds = 0;
        int validNames = 0;
        
        for (EnhancedCandidateItem candidate : candidates) {
            if (candidate.hasRecords()) withRecords++;
            if (candidate.hasTreatments()) withTreatments++;
            if (isValidCardId(candidate.getCardId())) validCardIds++;
            if (isValidName(candidate.getName())) validNames++;
        }
        
        return String.format(
            "数据质量报告: 总数=%d, 有档案=%d(%.1f%%), 有治疗=%d(%.1f%%), 有效卡号=%d(%.1f%%), 有效姓名=%d(%.1f%%)",
            total,
            withRecords, (withRecords * 100.0 / total),
            withTreatments, (withTreatments * 100.0 / total),
            validCardIds, (validCardIds * 100.0 / total),
            validNames, (validNames * 100.0 / total)
        );
    }
}