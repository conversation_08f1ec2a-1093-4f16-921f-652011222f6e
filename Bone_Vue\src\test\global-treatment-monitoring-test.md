# 全局状态监听功能测试指南

## 功能概述
本次实现了全局状态监听功能（增强版），具体包括：

1. **全局状态监听**：在系统的所有页面（除治疗详情页面外）实时检查治疗进程的状态变化
2. **时间到期自动更新**：
   - 实时检查治疗时间，当剩余时间为0时自动更新状态
   - 本地治疗模式：自动将状态从 `TREATING` 更新为 `COMPLETED`
   - 取走治疗模式：自动将状态从 `TREATING` 更新为 `AWAITING_RETURN`
3. **智能通知触发**：
   - 本地治疗完成：所有部位状态变为 `COMPLETED` 时弹出"治疗已完成"通知
   - 取走治疗待回收：所有部位状态变为 `AWAITING_RETURN` 时弹出"治疗头待取回"通知
4. **页面路由过滤**：在治疗详情页面禁用弹窗触发逻辑
5. **状态同步更新**：自动更新进程管理页面的状态

## 测试步骤

### 1. 准备测试环境
- 确保后端服务正常运行
- 确保有正在进行的治疗进程（包含多个部位）
- 确保前端应用正常启动

### 2. 测试全局状态监听启动
1. 打开浏览器开发者工具的控制台
2. 启动前端应用
3. 验证控制台输出：
   - 应该看到 "全局治疗状态监听已启动" 消息
   - 应该看到 "开始全局治疗状态监听" 消息

### 3. 测试页面路由过滤功能
1. 在主页、档案管理页面等非治疗详情页面：
   - 全局状态监听应该正常工作
   - 控制台应该显示定期的状态检查日志
2. 进入治疗详情页面（`/treatment-process/{processId}` 或 `/treatment-process-takeaway/{processId}`）：
   - 通知弹窗应该自动隐藏
   - 状态监听继续工作但不显示通知

### 4. 测试时间到期自动状态更新功能 (新增核心测试)
1. **准备测试数据**：
   - 创建一个本地治疗进程，设置较短的治疗时间（如1-2分钟）
   - 创建一个取走治疗进程，设置较短的治疗时间（如1-2分钟）

2. **测试本地治疗时间到期**：
   - 在非治疗详情页面等待治疗时间到期
   - 观察控制台日志，应该看到：
     ```
     开始检查 X 个进程的治疗时间到期情况
     检查进程 X (患者姓名) - 治疗模式: ON_SITE
       部位: 腰背部, 状态: TREATING, 剩余时间: 0秒
     检测到部位 腰背部 治疗时间到期，准备更新状态
     - 目标状态: COMPLETED
     部位 腰背部 状态已更新为 COMPLETED
     ```
   - 验证状态自动更新为 `COMPLETED`

3. **测试取走治疗时间到期**：
   - 在非治疗详情页面等待治疗时间到期
   - 观察控制台日志，应该看到：
     ```
     检查进程 X (患者姓名) - 治疗模式: TAKE_AWAY
       部位: 肩颈部, 状态: TREATING, 剩余时间: 0秒
     检测到部位 肩颈部 治疗时间到期，准备更新状态
     - 目标状态: AWAITING_RETURN
     部位 肩颈部 状态已更新为 AWAITING_RETURN
     ```
   - 验证状态自动更新为 `AWAITING_RETURN`

### 5. 测试本地治疗完成通知
1. 创建一个本地治疗进程，包含多个部位
2. 等待时间到期自动更新或手动触发所有部位状态变为 `COMPLETED`
3. 验证：
   - 应该弹出"治疗已完成"通知弹窗
   - 弹窗显示患者姓名和"治疗已完成"文字
   - 进程管理页面状态应该更新为"已完成"
   - 控制台应该显示 "触发治疗完成通知" 消息

### 6. 测试取走治疗待回收通知
1. 创建一个取走治疗进程，包含多个部位
2. 等待时间到期自动更新或手动触发所有部位状态变为 `AWAITING_RETURN`
3. 验证：
   - 应该弹出"治疗头待取回"通知弹窗
   - 弹窗显示"该患者治疗头待取回"文字
   - 进程管理页面状态应该更新为"待回收"
   - 控制台应该显示 "触发取走治疗待回收通知" 消息

### 7. 测试弹窗交互功能
1. 当弹窗出现时，测试以下功能：
   - **关闭功能**：点击右上角×按钮，弹窗应该消失
   - **拖拽功能**：按住弹窗并拖拽，弹窗应该跟随鼠标移动
   - **置顶功能**：点击弹窗，该弹窗应该置于最上层
   - **多弹窗布局**：多个弹窗应该自动排列，每个弹窗右移130px

### 8. 测试状态同步更新
1. 当触发通知后，检查进程管理页面：
   - 对应进程的状态应该自动更新
   - 本地治疗完成 → 状态显示"已完成"
   - 取走治疗待回收 → 状态显示"待回收"

## 技术实现要点

### 核心组件
- **状态监听店**: `src/stores/globalTreatmentMonitor.ts`
- **通知组件**: `src/components/GlobalTreatmentNotifications.vue`
- **主应用集成**: `src/App.vue`

### 关键配置
- **监听间隔**: 5秒检查一次状态变化
- **排除路由**: `/treatment-process*` 路径下不显示通知
- **API集成**: 使用现有的 `/processes/details` 接口获取状态

### 状态检查逻辑
```typescript
// 本地治疗完成检查
const allCompleted = currentProcess.details.every(detail => 
  detail.status === 'COMPLETED'
)

// 取走治疗待回收检查
const allAwaitingReturn = currentProcess.details.every(detail => 
  detail.status === 'AWAITING_RETURN'
)
```

## 注意事项
1. **性能考虑**: 监听间隔设置为5秒，避免过于频繁的API调用
2. **内存管理**: 组件卸载时会自动清理定时器和事件监听
3. **错误处理**: 网络错误时会在控制台输出错误信息，不影响其他功能
4. **状态去重**: 避免重复触发相同的通知

## 故障排除
1. **通知不显示**: 检查当前页面路由是否在排除列表中
2. **状态不更新**: 检查后端API是否正常返回数据
3. **弹窗样式异常**: 检查图片资源是否正确加载
4. **拖拽不工作**: 检查是否有其他元素阻挡了鼠标事件

## 开发调试
- 在浏览器控制台中可以查看详细的状态监听日志
- 可以通过 `treatmentMonitorStore.notifications` 查看当前通知列表
- 可以通过 `treatmentMonitorStore.activeProcesses` 查看当前监听的进程
