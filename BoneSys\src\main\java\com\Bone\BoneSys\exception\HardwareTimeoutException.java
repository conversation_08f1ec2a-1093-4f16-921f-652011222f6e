package com.Bone.BoneSys.exception;

/**
 * 硬件通信超时异常
 */
public class HardwareTimeoutException extends HardwareException {
    
    private final long timeoutMs;
    
    public HardwareTimeoutException(String message, long timeoutMs) {
        super("TIMEOUT_ERROR", message, HardwareErrorType.TIMEOUT, true);
        this.timeoutMs = timeoutMs;
    }
    
    public HardwareTimeoutException(String message, Throwable cause, long timeoutMs) {
        super("TIMEOUT_ERROR", message, cause, HardwareErrorType.TIMEOUT, true);
        this.timeoutMs = timeoutMs;
    }
    
    public long getTimeoutMs() {
        return timeoutMs;
    }
}