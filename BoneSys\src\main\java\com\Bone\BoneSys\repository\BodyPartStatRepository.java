package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.BodyPartStat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 部位统计数据访问接口
 */
@Repository
public interface BodyPartStatRepository extends JpaRepository<BodyPartStat, Long> {
    
    /**
     * 根据档案ID查找部位统计
     */
    List<BodyPartStat> findByRecordId(Long recordId);
    
    /**
     * 根据档案ID和部位查找统计
     */
    Optional<BodyPartStat> findByRecordIdAndBodyPart(Long recordId, String bodyPart);
    
    /**
     * 根据治疗部位查找统计
     */
    List<BodyPartStat> findByBodyPart(String bodyPart);
    
    /**
     * 根据档案ID按使用次数排序查找统计
     */
    List<BodyPartStat> findByRecordIdOrderByTotalUsageCountDesc(Long recordId);
    
    /**
     * 根据档案ID按使用时长排序查找统计
     */
    List<BodyPartStat> findByRecordIdOrderByTotalDurationMinutesDesc(Long recordId);
    
    /**
     * 查找使用次数最多的部位统计
     */
    @Query("SELECT b FROM BodyPartStat b WHERE b.totalUsageCount = " +
           "(SELECT MAX(b2.totalUsageCount) FROM BodyPartStat b2 WHERE b2.record.id = :recordId)")
    List<BodyPartStat> findMostUsedBodyPartsByRecord(@Param("recordId") Long recordId);
    
    /**
     * 统计所有部位的总使用情况
     */
    @Query("SELECT b.bodyPart, SUM(b.totalUsageCount), SUM(b.totalDurationMinutes) FROM BodyPartStat b " +
           "GROUP BY b.bodyPart ORDER BY SUM(b.totalUsageCount) DESC")
    List<Object[]> getOverallBodyPartStats();
    
    /**
     * 根据患者ID查找部位统计
     */
    @Query("SELECT b FROM BodyPartStat b JOIN b.record r WHERE r.patient.id = :patientId")
    List<BodyPartStat> findByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 检查档案和部位的统计是否存在
     */
    boolean existsByRecordIdAndBodyPart(Long recordId, String bodyPart);
    
    /**
     * 获取档案的部位统计总数
     */
    @Query("SELECT COUNT(b) FROM BodyPartStat b WHERE b.record.id = :recordId")
    Long countByRecordId(@Param("recordId") Long recordId);
    
    /**
     * 获取患者部位统计汇总（按部位分组）
     */
    @Query("SELECT bps.bodyPart, SUM(bps.totalUsageCount) FROM BodyPartStat bps " +
           "JOIN bps.record r WHERE r.patient.id = :patientId " +
           "GROUP BY bps.bodyPart")
    List<Object[]> getPatientBodyPartSummary(@Param("patientId") Long patientId);
    
    /**
     * 获取患者所有部位的总治疗次数
     */
    @Query("SELECT COALESCE(SUM(bps.totalUsageCount), 0) FROM BodyPartStat bps " +
           "JOIN bps.record r WHERE r.patient.id = :patientId")
    Integer getPatientTotalSessions(@Param("patientId") Long patientId);
    
    /**
     * 获取患者的所有治疗部位（去重）
     */
    @Query("SELECT DISTINCT bps.bodyPart FROM BodyPartStat bps " +
           "JOIN bps.record r WHERE r.patient.id = :patientId " +
           "AND bps.bodyPart IS NOT NULL AND bps.bodyPart != '' " +
           "ORDER BY bps.bodyPart")
    List<String> getPatientDistinctBodyParts(@Param("patientId") Long patientId);
    
    /**
     * 批量获取多个患者的部位统计信息
     */
    @Query("SELECT r.patient.id, bps.bodyPart, SUM(bps.totalUsageCount) " +
           "FROM BodyPartStat bps JOIN bps.record r " +
           "WHERE r.patient.id IN :patientIds " +
           "GROUP BY r.patient.id, bps.bodyPart")
    List<Object[]> getBatchPatientBodyPartStats(@Param("patientIds") List<Long> patientIds);
}