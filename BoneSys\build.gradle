plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.7'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.Bone'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// Spring Boot核心依赖
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-websocket'
	
	// JWT认证
	implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'
	
	// 串口通信
	implementation 'com.fazecast:jSerialComm:2.10.4'
	
	// 数据库
	runtimeOnly 'com.mysql:mysql-connector-j'
	runtimeOnly 'com.h2database:h2'
	
	// 工具类
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	
	// 开发工具
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	
	// 测试依赖
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
}
