package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.EnhancedCandidateItem;
import com.Bone.BoneSys.dto.EnhancedCandidateListResponse;
import com.Bone.BoneSys.entity.Patient;
import com.Bone.BoneSys.entity.Record;
import com.Bone.BoneSys.repository.PatientRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import com.Bone.BoneSys.service.PatientDataAggregationService;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 档案管理控制器
 * 对应UI页面：新版-档案管理.png
 */
@RestController
@RequestMapping("/api/records")
@CrossOrigin(origins = "*")
public class RecordController {

    private static final Logger logger = LoggerFactory.getLogger(RecordController.class);

    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private PatientDataAggregationService patientDataAggregationService;
    
    @Autowired
    private com.Bone.BoneSys.service.DataMissingHandlerService dataMissingHandlerService;
    
    @Autowired
    private com.Bone.BoneSys.service.DataMissingTestService dataMissingTestService;
    
    @Autowired
    private com.Bone.BoneSys.service.DataConsistencyValidationService dataConsistencyValidationService;

    /**
     * 获取档案管理页面数据
     * GET /api/records
     */
    @GetMapping
    public ApiResponse<RecordListResponse> getRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String cardId) {
        
        try {
            logger.info("Fetching records list - page: {}, size: {}, search: {}, cardId: {}", 
                       page, size, search, cardId);

            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
            Page<Record> recordPage;

            // 根据搜索条件查询
            if (search != null && !search.trim().isEmpty()) {
                recordPage = recordRepository.findByPatientNameContainingOrPatientCardIdContaining(
                    search.trim(), search.trim(), pageable);
            } else if (cardId != null && !cardId.trim().isEmpty()) {
                recordPage = recordRepository.findByPatientCardId(cardId.trim(), pageable);
            } else {
                recordPage = recordRepository.findAll(pageable);
            }

            // 转换为响应格式
            List<RecordItem> records = recordPage.getContent().stream()
                .map(this::convertToRecordItem)
                .collect(Collectors.toList());

            RecordListResponse response = new RecordListResponse();
            response.setRecords(records);
            response.setPagination(new PaginationInfo(
                page,
                recordPage.getTotalPages(),
                (int) recordPage.getTotalElements(),
                size
            ));

            return ApiResponse.success("档案列表获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching records list", e);
            return ApiResponse.error(500, "获取档案列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除档案
     * DELETE /api/records/{recordId}
     */
    @DeleteMapping("/{recordId}")
    public ApiResponse<Void> deleteRecord(
            @PathVariable Long recordId,
            @RequestBody DeleteRecordRequest request) {
        
        try {
            logger.info("Deleting record: {}", recordId);

            // 验证密码（这里简化处理，实际应该验证管理员密码）
            if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
                return ApiResponse.error(400, "密码不能为空");
            }

            // 检查档案是否存在
            if (!recordRepository.existsById(recordId)) {
                return ApiResponse.error(404, "档案不存在");
            }

            // 简单密码验证（实际应该加密验证）
            if (!"123456".equals(request.getPassword())) {
                return ApiResponse.error(403, "密码错误");
            }

            // 删除档案
            recordRepository.deleteById(recordId);

            return ApiResponse.success("删除成功");

        } catch (Exception e) {
            logger.error("Error deleting record: {}", recordId, e);
            return ApiResponse.error(500, "删除档案失败: " + e.getMessage());
        }
    }

    /**
     * 获取新建档案候选列表（增强版）
     * GET /api/records/candidates
     * 
     * 返回包含完整患者信息的候选列表，包括：
     * - 患者基本信息（来自patients表）
     * - 最新就诊时间（来自records表的建档日期）
     * - 治疗部位（来自body_part_stats表的body_part）
     * - 治疗次数（来自body_part_stats表的total_usage_count总和）
     */
    @GetMapping("/candidates")
    public ApiResponse<EnhancedCandidateListResponse> getCandidates(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("获取候选患者列表 - 页码: {}, 大小: {}, 搜索: {}", page, size, search);

            // 创建分页参数，按患者创建时间降序排列
            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
            
            // 使用数据聚合服务获取完整的患者信息
            EnhancedCandidateListResponse response = patientDataAggregationService
                    .getPatientCandidatesWithStats(search, pageable);
            
            // 记录性能指标
            long duration = System.currentTimeMillis() - startTime;
            logger.info("候选患者列表获取完成 - 耗时: {}ms, 返回记录数: {}", 
                       duration, response.getCandidates().size());
            
            // 记录数据统计信息
            if (response.getCandidates() != null && !response.getCandidates().isEmpty()) {
                long withRecords = response.getCandidates().stream()
                        .mapToLong(c -> c.hasRecords() ? 1 : 0).sum();
                long withTreatments = response.getCandidates().stream()
                        .mapToLong(c -> c.hasTreatments() ? 1 : 0).sum();
                
                logger.info("数据统计 - 有档案: {}/{}, 有治疗: {}/{}", 
                           withRecords, response.getCandidates().size(),
                           withTreatments, response.getCandidates().size());
            }
            
            return ApiResponse.success("候选列表获取成功", response);

        } catch (Exception e) {
            logger.error("获取候选患者列表失败", e);
            
            // 创建空响应作为降级处理
            EnhancedCandidateListResponse emptyResponse = new EnhancedCandidateListResponse();
            emptyResponse.setCandidates(List.of());
            com.Bone.BoneSys.dto.PaginationInfo pagination = new com.Bone.BoneSys.dto.PaginationInfo();
            pagination.setCurrentPage(page);
            pagination.setTotalPages(1);
            pagination.setTotalRecords(0L);
            pagination.setPageSize(size);
            pagination.setHasNext(false);
            pagination.setHasPrevious(false);
            emptyResponse.setPagination(pagination);
            
            return ApiResponse.error(500, "获取候选列表失败: " + e.getMessage(), emptyResponse);
        }
    }
    
    /**
     * 获取新建档案候选列表（兼容旧版本）
     * GET /api/records/candidates/legacy
     * 
     * 保持向后兼容性的旧版本API
     */
    @GetMapping("/candidates/legacy")
    public ApiResponse<CandidateListResponse> getCandidatesLegacy(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        try {
            logger.info("获取候选患者列表（兼容版） - 页码: {}, 大小: {}, 搜索: {}", page, size, search);

            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
            Page<Patient> patientPage;

            // 根据搜索条件查询患者
            if (search != null && !search.trim().isEmpty()) {
                patientPage = patientRepository.findByNameContainingOrPatientCardIdContaining(
                    search.trim(), search.trim(), pageable);
            } else {
                patientPage = patientRepository.findAll(pageable);
            }

            // 转换为候选项格式
            List<CandidateItem> candidates = patientPage.getContent().stream()
                .map(this::convertToCandidateItem)
                .collect(Collectors.toList());

            CandidateListResponse response = new CandidateListResponse();
            response.setCandidates(candidates);
            response.setPagination(new PaginationInfo(
                page,
                patientPage.getTotalPages(),
                (int) patientPage.getTotalElements(),
                size
            ));

            return ApiResponse.success("候选列表获取成功", response);

        } catch (Exception e) {
            logger.error("获取候选患者列表（兼容版）失败", e);
            return ApiResponse.error(500, "获取候选列表失败: " + e.getMessage());
        }
    }

    /**
     * 转换Record为RecordItem
     */
    private RecordItem convertToRecordItem(Record record) {
        RecordItem item = new RecordItem();
        Patient patient = record.getPatient();
        
        item.setId(record.getId()); // 设置档案ID
        item.setPatientId(patient.getId());
        item.setCardId(patient.getPatientCardId());
        item.setName(patient.getName());
        item.setAge(patient.getAge() != null ? patient.getAge() + "岁" : "未知");
        item.setGender(patient.getGender() != null ? patient.getGender() : "未知");
        item.setTotalTreatments(record.getSessionsCompletedCount());
        item.setCreatedDate(record.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        
        return item;
    }

    /**
     * 转换Patient为CandidateItem（兼容旧版本）
     */
    private CandidateItem convertToCandidateItem(Patient patient) {
        CandidateItem item = new CandidateItem();
        
        item.setPatientId(patient.getId());
        item.setCardId(patient.getPatientCardId());
        item.setName(patient.getName());
        item.setAge(patient.getAge() != null ? patient.getAge() + "岁" : "未知");
        item.setGender(patient.getGender() != null ? patient.getGender() : "未知");
        item.setAppointmentTime("2025-07-27 14:30"); // 模拟预约时间
        item.setBodyPart("待确定"); // 模拟治疗部位
        item.setSessions(0); // 模拟次数
        
        return item;
    }
    
    /**
     * 数据一致性验证端点
     * GET /api/records/candidates/validate/{patientId}
     * 
     * 用于验证特定患者的数据显示是否与数据库一致
     */
    @GetMapping("/candidates/validate/{patientId}")
    public ApiResponse<Object> validatePatientData(@PathVariable Long patientId) {
        try {
            logger.info("验证患者数据一致性 - 患者ID: {}", patientId);
            
            // 获取患者统计摘要
            Map<String, Object> summary = patientDataAggregationService.getPatientStatsSummary(patientId);
            
            return ApiResponse.success("患者数据验证完成", summary);
            
        } catch (Exception e) {
            logger.error("验证患者数据失败 - 患者ID: {}", patientId, e);
            return ApiResponse.error(500, "验证患者数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量数据质量检查端点
     * GET /api/records/candidates/quality-check
     * 
     * 检查候选患者数据的整体质量
     */
    @GetMapping("/candidates/quality-check")
    public ApiResponse<Object> performQualityCheck(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "50") int size) {
        
        try {
            logger.info("执行数据质量检查 - 页码: {}, 大小: {}", page, size);
            
            Pageable pageable = PageRequest.of(page - 1, size);
            EnhancedCandidateListResponse response = patientDataAggregationService
                    .getPatientCandidatesWithStats(null, pageable);
            
            // 生成质量报告
            Map<String, Object> qualityReport = Map.of(
                "totalCandidates", response.getCandidates().size(),
                "withRecords", response.getCandidates().stream()
                        .mapToLong(c -> c.hasRecords() ? 1 : 0).sum(),
                "withTreatments", response.getCandidates().stream()
                        .mapToLong(c -> c.hasTreatments() ? 1 : 0).sum(),
                "averageTreatmentParts", response.getCandidates().stream()
                        .mapToInt(c -> c.getTreatmentPartsCount()).average().orElse(0.0),
                "totalSessions", response.getCandidates().stream()
                        .mapToInt(c -> c.getTotalSessions() != null ? c.getTotalSessions() : 0).sum()
            );
            
            return ApiResponse.success("数据质量检查完成", qualityReport);
            
        } catch (Exception e) {
            logger.error("数据质量检查失败", e);
            return ApiResponse.error(500, "数据质量检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 数据缺失情况报告端点
     * GET /api/records/candidates/missing-data-report
     * 
     * 生成详细的数据缺失统计报告
     */
    @GetMapping("/candidates/missing-data-report")
    public ApiResponse<Object> getMissingDataReport(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "100") int size) {
        
        try {
            logger.info("生成数据缺失报告 - 页码: {}, 大小: {}", page, size);
            
            Pageable pageable = PageRequest.of(page - 1, size);
            EnhancedCandidateListResponse response = patientDataAggregationService
                    .getPatientCandidatesWithStats(null, pageable);
            
            // 生成数据缺失报告
            Map<String, Object> missingDataReport = dataMissingHandlerService
                    .generateMissingDataReport(response.getCandidates());
            
            // 添加数据完整性分析
            long completeData = response.getCandidates().stream()
                    .mapToLong(c -> dataMissingHandlerService.isDataComplete(c) ? 1 : 0).sum();
            
            Map<String, Object> fullReport = Map.of(
                "missingDataStats", missingDataReport,
                "completenessStats", Map.of(
                    "completeRecords", completeData,
                    "totalRecords", response.getCandidates().size(),
                    "completenessPercentage", String.format("%.1f%%", 
                            (completeData * 100.0 / response.getCandidates().size()))
                ),
                "sampleHints", response.getCandidates().stream()
                        .limit(5)
                        .collect(Collectors.toMap(
                                c -> "患者" + c.getPatientId(),
                                dataMissingHandlerService::createMissingDataHint
                        ))
            );
            
            return ApiResponse.success("数据缺失报告生成完成", fullReport);
            
        } catch (Exception e) {
            logger.error("生成数据缺失报告失败", e);
            return ApiResponse.error(500, "生成数据缺失报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取单个患者的数据完整性信息
     * GET /api/records/candidates/completeness/{patientId}
     */
    @GetMapping("/candidates/completeness/{patientId}")
    public ApiResponse<Object> getPatientDataCompleteness(@PathVariable Long patientId) {
        try {
            logger.info("获取患者数据完整性信息 - 患者ID: {}", patientId);
            
            // 获取患者统计摘要
            Map<String, Object> summary = patientDataAggregationService.getPatientStatsSummary(patientId);
            
            // 如果有错误，直接返回
            if (summary.containsKey("error")) {
                return ApiResponse.error(404, "患者不存在或数据异常", summary);
            }
            
            // 创建一个临时的候选项来分析完整性
            Pageable pageable = PageRequest.of(0, 1);
            EnhancedCandidateListResponse response = patientDataAggregationService
                    .getPatientCandidatesWithStats(null, pageable);
            
            // 查找特定患者
            EnhancedCandidateItem candidate = response.getCandidates().stream()
                    .filter(c -> c.getPatientId().equals(patientId))
                    .findFirst()
                    .orElse(null);
            
            if (candidate == null) {
                return ApiResponse.error(404, "未找到患者信息");
            }
            
            Map<String, Object> completenessInfo = Map.of(
                "patientId", patientId,
                "isComplete", dataMissingHandlerService.isDataComplete(candidate),
                "completenessLevel", dataMissingHandlerService.getDataCompletenessLevel(candidate),
                "missingDataHint", dataMissingHandlerService.createMissingDataHint(candidate),
                "hasRecords", candidate.hasRecords(),
                "hasTreatments", candidate.hasTreatments(),
                "treatmentPartsCount", candidate.getTreatmentPartsCount(),
                "rawData", candidate
            );
            
            return ApiResponse.success("患者数据完整性信息获取成功", completenessInfo);
            
        } catch (Exception e) {
            logger.error("获取患者数据完整性信息失败 - 患者ID: {}", patientId, e);
            return ApiResponse.error(500, "获取患者数据完整性信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试数据缺失处理逻辑
     * GET /api/records/candidates/test-missing-data
     * 
     * 用于测试和验证数据缺失处理逻辑的正确性
     */
    @GetMapping("/candidates/test-missing-data")
    public ApiResponse<Object> testMissingDataHandling() {
        try {
            logger.info("开始测试数据缺失处理逻辑");
            
            // 执行测试
            Map<String, Object> testResult = dataMissingTestService.testMissingDataHandling();
            
            // 验证处理逻辑
            boolean validationPassed = dataMissingTestService.validateMissingDataHandling();
            
            Map<String, Object> response = Map.of(
                "testResult", testResult,
                "validationPassed", validationPassed,
                "message", validationPassed ? "数据缺失处理测试通过" : "数据缺失处理测试失败"
            );
            
            return ApiResponse.success("数据缺失处理测试完成", response);
            
        } catch (Exception e) {
            logger.error("测试数据缺失处理逻辑失败", e);
            return ApiResponse.error(500, "测试数据缺失处理逻辑失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证数据一致性
     * GET /api/records/candidates/validate-consistency
     * 
     * 验证前端显示的数据与数据库中的实际数据是否一致
     */
    @GetMapping("/candidates/validate-consistency")
    public ApiResponse<Object> validateDataConsistency() {
        try {
            logger.info("开始验证数据一致性");
            
            Map<String, Object> validationReport = dataConsistencyValidationService
                    .validateAllCandidatesConsistency();
            
            return ApiResponse.success("数据一致性验证完成", validationReport);
            
        } catch (Exception e) {
            logger.error("验证数据一致性失败", e);
            return ApiResponse.error(500, "验证数据一致性失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证单个患者数据一致性
     * GET /api/records/candidates/validate-consistency/{patientId}
     */
    @GetMapping("/candidates/validate-consistency/{patientId}")
    public ApiResponse<Object> validateSinglePatientConsistency(@PathVariable Long patientId) {
        try {
            logger.info("验证患者{}数据一致性", patientId);
            
            // 先获取患者的API数据
            EnhancedCandidateListResponse response = patientDataAggregationService
                    .getPatientCandidatesWithStats(null, PageRequest.of(0, 100));
            
            EnhancedCandidateItem candidate = response.getCandidates().stream()
                    .filter(c -> c.getPatientId().equals(patientId))
                    .findFirst()
                    .orElse(null);
            
            if (candidate == null) {
                return ApiResponse.error(404, "未找到患者数据");
            }
            
            Map<String, Object> validationResult = dataConsistencyValidationService
                    .validateSinglePatientConsistency(candidate);
            
            return ApiResponse.success("单个患者数据一致性验证完成", validationResult);
            
        } catch (Exception e) {
            logger.error("验证患者{}数据一致性失败", patientId, e);
            return ApiResponse.error(500, "验证患者数据一致性失败: " + e.getMessage());
        }
    }
    
    /**
     * 修复数据不一致问题
     * POST /api/records/candidates/fix-inconsistencies
     */
    @PostMapping("/candidates/fix-inconsistencies")
    public ApiResponse<Object> fixDataInconsistencies(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> patientIds = (List<Long>) request.get("patientIds");
            
            if (patientIds == null || patientIds.isEmpty()) {
                return ApiResponse.error(400, "患者ID列表不能为空");
            }
            
            logger.info("开始修复{}个患者的数据不一致问题", patientIds.size());
            
            Map<String, Object> fixResult = dataConsistencyValidationService
                    .fixDataInconsistencies(patientIds);
            
            return ApiResponse.success("数据不一致问题修复完成", fixResult);
            
        } catch (Exception e) {
            logger.error("修复数据不一致问题失败", e);
            return ApiResponse.error(500, "修复数据不一致问题失败: " + e.getMessage());
        }
    }

    // DTO类定义
    @Data
    public static class RecordListResponse {
        private List<RecordItem> records;
        private PaginationInfo pagination;
    }

    @Data
    public static class RecordItem {
        private Long id; // 档案ID
        private Long patientId;
        private String cardId;
        private String name;
        private String age;
        private String gender;
        private Integer totalTreatments;
        private String createdDate;
    }

    @Data
    public static class CandidateListResponse {
        private List<CandidateItem> candidates;
        private PaginationInfo pagination;
    }

    @Data
    public static class CandidateItem {
        private Long patientId;
        private String cardId;
        private String name;
        private String age;
        private String gender;
        private String appointmentTime;
        private String bodyPart;
        private Integer sessions;
    }

    @Data
    public static class PaginationInfo {
        private int currentPage;
        private int totalPages;
        private int totalRecords;
        private int pageSize;

        public PaginationInfo(int currentPage, int totalPages, int totalRecords, int pageSize) {
            this.currentPage = currentPage;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
            this.pageSize = pageSize;
        }
    }

    @Data
    public static class DeleteRecordRequest {
        private String password;
    }
}
