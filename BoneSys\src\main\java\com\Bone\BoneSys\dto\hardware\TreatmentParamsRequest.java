package com.Bone.BoneSys.dto.hardware;

import java.util.List;

/**
 * 治疗参数请求DTO
 * 用于向硬件发送治疗参数设置请求
 */
public class TreatmentParamsRequest {
    
    private int duration;           // 设置时间（分钟）
    private int intensity;          // 设置声强 (mW/cm²)
    private int frequency;          // 脉冲频率 (Hz): 100 或 1000
    private List<Integer> headNumbers;  // 治疗头编号列表
    
    public TreatmentParamsRequest() {}
    
    public TreatmentParamsRequest(int duration, int intensity, int frequency, List<Integer> headNumbers) {
        this.duration = duration;
        this.intensity = intensity;
        this.frequency = frequency;
        this.headNumbers = headNumbers;
    }
    
    // Getters and Setters
    public int getDuration() {
        return duration;
    }
    
    public int getDurationMinutes() {
        return duration;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
    
    public int getIntensity() {
        return intensity;
    }
    
    public void setIntensity(int intensity) {
        this.intensity = intensity;
    }
    
    public int getFrequency() {
        return frequency;
    }
    
    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }
    
    public List<Integer> getHeadNumbers() {
        return headNumbers;
    }
    
    public void setHeadNumbers(List<Integer> headNumbers) {
        this.headNumbers = headNumbers;
    }
    
    /**
     * 获取频率标记（用于硬件指令）
     * @return 0表示100Hz，1表示1000Hz
     */
    public int getFrequencyFlag() {
        return frequency == 1000 ? 1 : 0;
    }
    
    /**
     * 验证参数有效性
     */
    public boolean isValid() {
        return duration > 0 && duration <= 99 &&
               intensity > 0 && intensity <= 999 &&
               (frequency == 100 || frequency == 1000) &&
               headNumbers != null && !headNumbers.isEmpty() &&
               headNumbers.size() <= 99;
    }
    
    @Override
    public String toString() {
        return String.format("TreatmentParamsRequest{duration=%d min, intensity=%d mW/cm², frequency=%d Hz, headNumbers=%s}", 
                           duration, intensity, frequency, headNumbers);
    }
}