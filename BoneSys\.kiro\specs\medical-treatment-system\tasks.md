# 实现任务列表

- [x] 1. 项目基础架构搭建



  - 创建SpringBoot后端项目结构，配置Gradle构建文件
  - 创建Vue 3前端项目结构，配置Vite构建工具
  - 配置MySQL数据库连接和JPA配置
  - 配置跨域和基础安全设置
  - _需求: 1, 2_

- [x] 2. 数据库表结构创建和基础实体类实现



  - [x] 2.1 执行数据库建表脚本


    - bonesys.sql是之前的数据库表，build.sql是最新的数据库设计
    - 请结合之前数据库的优势，但属性、逻辑需要和build.sql一致，再合理创建另一个数据库
    - 验证表结构和约束关系正确性，保证查询速度
    - _需求: 3, 4, 7, 8, 9_

  - [x] 2.2 实现JPA实体类


    - 创建User、Patient、Record、Process、TreatmentDetail、TreatmentHead、BodyPartStat实体类
    - 配置实体类之间的关联关系和JPA注解
    - 实现实体类的构造函数、getter/setter方法
    - _需求: 1, 3, 4, 7, 8, 9_

  - [x] 2.3 创建Repository接口


    - 实现UserRepository、PatientRepository、RecordRepository等数据访问接口
    - 添加自定义查询方法（如按就诊卡号查询、按姓名模糊查询等）
    - _需求: 1, 3, 8_

- [x] 3. 串口通信核心功能实现






  - [x] 3.1 实现串口通信服务基础框架

    - 集成jSerialComm库，配置串口连接参数（波特率115200等）
    - 实现串口初始化、连接建立和关闭功能
    - 实现基础的指令发送和响应接收机制
    - _需求: 10_

  - [x] 3.2 实现硬件指令解析器


    - 实现TRZI指令的响应解析（治疗头查询）
    - 实现TWSC、TWSN指令的响应验证（指示灯控制）
    - 实现TWSDT、TWS、TWZO指令的响应验证（参数下载、启动、停止）
    - 添加指令格式验证和错误处理
    - _需求: 10_

  - [x] 3.3 实现具体硬件操作方法


    - 实现queryTreatmentHeads()方法查询治疗头状态
    - 实现lightUpTreatmentHeads()和lightOffTreatmentHeads()方法控制指示灯
    - 实现downloadParameters()、startTreatment()、stopTreatment()方法
    - 添加串口通信异常处理和重连机制
    - _需求: 5, 6, 7, 10_

- [x] 4. 用户认证模块实现
  - [x] 4.1 实现后端认证服务
    - 创建AuthService实现密码验证逻辑（首次通过厂家密码设置用户密码+之后直接使用用户密码）
    - 实现JWT token生成和验证功能
    - 实现登录时间更新逻辑
    - _需求: 1_

  - [x] 4.2 实现认证Controller和DTO
    - 创建AuthController处理登录请求
    - 创建LoginRequest、LoginResponse等DTO类
    - 实现登录接口/api/login、/api/reset-password、/api/change-password
    - _需求: 1_

  - [ ] 4.3 实现前端登录页面
    - 创建LoginPage组件，实现用户名密码输入表单
    - 实现厂家密码和用户密码切换功能
    - 集成API调用和错误处理
    - 实现登录成功后跳转到主界面
    - _需求: 1_

- [ ] 5. 主界面和导航功能实现
  - [ ] 5.1 实现主界面后端接口
    - 创建DashboardController实现/api/dashboard/main接口
    - 返回系统状态信息和快捷操作数据
    - _需求: 2_

  - [ ] 5.2 实现前端主界面组件
    - 创建MainPage组件显示系统时间和状态
    - 实现快捷操作按钮和功能模块入口
    - 配置路由导航到各功能界面
    - _需求: 2_

- [x] 6. 患者档案管理功能实现
  - [x] 6.1 实现患者管理后端服务
    - 创建PatientService实现患者CRUD操作（通过DatabaseDebugController实现）
    - 实现就诊卡号唯一性验证逻辑
    - 实现患者信息模糊查询功能
    - _需求: 3_

  - [x] 6.2 实现患者管理Controller和接口
    - 创建DatabaseDebugController实现患者相关API接口
    - 实现/api/patient-record/search、/api/patient-record/create、/api/debug/patient-info等接口
    - 创建相应的Request和Response DTO类
    - _需求: 3_

  - [ ] 6.3 实现新建档案前端页面
    - 创建NewPatientPage组件显示最近患者列表
    - 实现分页功能和患者信息展示
    - 添加创建新患者的导航功能
    - _需求: 3_

  - [ ] 6.4 实现患者信息录入前端页面
    - 创建CreatePatientPage组件实现患者信息表单
    - 实现表单验证和就诊卡号重复检查
    - 集成API调用和错误提示功能
    - _需求: 3_

  - [ ] 6.5 实现档案管理和个人信息页面
    - 创建PatientArchivePage组件实现档案查询和列表显示
    - 创建PatientProfilePage组件显示患者详细信息和治疗历史
    - 实现患者信息编辑和保存功能
    - _需求: 3_

- [x] 7. 治疗参数设置功能实现
  - [x] 7.1 实现治疗参数后端服务
    - 创建TreatmentService实现治疗参数验证和保存逻辑（通过DatabaseDebugController实现）
    - 实现身体部位配置管理
    - 实现治疗参数安全范围验证
    - _需求: 4_

  - [x] 7.2 实现治疗参数Controller和接口
    - 创建DatabaseDebugController实现治疗相关API接口
    - 实现/api/debug/parameter-setting等接口
    - 创建TreatmentSettingsRequest、BodyPartsResponse等DTO
    - _需求: 4_

  - [ ] 7.3 实现参数设置前端页面
    - 创建PatientSettingsPage组件实现人体部位图和参数设置表单
    - 实现部位选择、参数输入和验证功能
    - 集成贴片类型和数量设置
    - _需求: 4_

- [x] 8. 治疗头设备管理功能实现
  - [x] 8.1 实现治疗头管理后端服务


    - 创建HardwareService集成串口通信服务
    - 实现治疗头状态查询和可用性检查逻辑
    - 实现治疗头推荐算法（基于电量、使用次数等）
    - _需求: 5, 9_

  - [x] 8.2 实现治疗头管理Controller和接口


    - 创建HardwareController实现设备管理API接口
    - 实现/api/hardware/treatment-heads/sync、/api/hardware/treatment-heads/lights等接口
    - 创建TreatmentHeadInfo、TreatmentHeadLightRequest等DTO
    - _需求: 5, 9_

  - [ ] 8.3 实现治疗头可用性检查页面
    - 创建治疗头数量不足提示组件，电量大于60%的才能使用
    - 实现可用治疗头数量显示和刷新功能
    - 集成等待和重试机制
    - _需求: 5_

  - [ ] 8.4 实现治疗头管理前端页面
    - 创建TreatmentHeadManagementPage组件显示设备状态列表
    - 实现治疗头状态、电量、使用情况的实时显示
    - 添加设备异常提醒和处理建议
    - _需求: 9_

- [ ] 9. 贴片指导和治疗进程功能实现
  - [ ] 9.1 实现贴片指导后端服务
    - 实现贴片选择逻辑和指导信息生成
    - 创建贴片位置配置和图片资源管理
    - _需求: 6_

  - [ ] 9.2 实现贴片指导Controller和接口
    - 实现/api/treatment-heads/patch-selection接口
    - 创建PatchSelectionRequest、PatchSelectionResponse等DTO
    - _需求: 6_

  - [ ] 9.3 实现贴片指导前端页面
    - 创建PatchGuidePage组件显示贴片指导图和说明
    - 实现深部/浅层贴片类型的不同指导显示
    - 添加贴片确认和进入治疗流程功能
    - _需求: 6_

  - [ ] 9.4 实现治疗进程管理后端服务
    - 创建ProcessService实现治疗进程的创建、监控和管理
    - 集成串口通信服务启动和停止治疗
    - 实现治疗状态实时更新和数据统计
    - _需求: 7_

  - [ ] 9.5 实现治疗进程Controller和接口
    - 实现/api/treatment/start、/api/treatment/status/{processId}等接口
    - 实现/api/treatment/complete/{processId}、/api/treatment/terminate/{processId}接口
    - 创建StartTreatmentRequest、TreatmentStatusResponse等DTO
    - _需求: 7_

  - [ ] 9.6 实现治疗进程前端页面
    - 创建TreatmentProcessPage组件显示治疗进度和状态
    - 实现实时进度更新、剩余时间显示
    - 添加治疗终止和完成处理功能
    - _需求: 7_

- [x] 10. 进程管理功能实现
  - [x] 10.1 实现进程管理后端服务
    - 实现进程列表查询和状态管理（通过DatabaseDebugController实现）
    - 实现进程筛选和分页功能
    - _需求: 8_

  - [x] 10.2 实现进程管理Controller和接口
    - 实现/api/debug/process-management、/api/debug/process/terminate等接口
    - 创建ProcessListResponse、ProcessInfo等DTO
    - _需求: 8_

  - [ ] 10.3 实现进程管理前端页面
    - 创建ProcessManagementPage组件显示进程列表
    - 实现进程状态颜色区分和操作按钮
    - 添加进程查询和筛选功能
    - _需求: 8_

- [ ] 11. 系统设置功能实现
  - [ ] 11.1 实现系统设置后端服务
    - 创建SettingsService实现系统参数配置管理
    - 实现默认治疗参数的保存和加载
    - _需求: 11_

  - [ ] 11.2 实现系统设置Controller和接口
    - 实现/api/settings/parameters接口
    - 创建SettingsRequest、SettingsResponse等DTO
    - _需求: 11_

  - [ ] 11.3 实现系统设置前端页面
    - 创建SystemSettingsPage组件实现参数配置表单
    - 实现默认参数设置和保存功能
    - _需求: 11_

- [ ] 12. 全局异常处理和错误处理实现
  - [ ] 12.1 实现后端全局异常处理
    - 创建GlobalExceptionHandler处理各种业务异常
    - 实现SerialCommunicationException、PatientCardIdDuplicateException等自定义异常
    - 配置统一的错误响应格式
    - _需求: 1, 3, 5, 7, 10_

  - [ ] 12.2 实现前端错误处理机制
    - 配置Axios请求拦截器处理API错误
    - 实现错误提示组件和用户友好的错误信息显示
    - 添加网络异常和硬件异常的特殊处理
    - _需求: 1, 5, 10_

- [ ] 13. 前端路由和导航完善
  - [ ] 13.1 配置Vue Router路由系统
    - 配置所有页面的路由规则和导航守卫
    - 实现JWT token验证和自动登录跳转
    - _需求: 1, 2_

  - [ ] 13.2 实现页面间数据传递和状态管理
    - 配置Vuex或Pinia状态管理
    - 实现页面间的数据共享和状态同步
    - _需求: 4, 6, 7_

- [ ] 14. 数据统计和报告功能实现
  - [ ] 14.1 实现统计数据计算服务
    - 创建StatisticsService实现各部位治疗统计计算
    - 实现治疗历史数据汇总和分析
    - _需求: 3, 7_

  - [ ] 14.2 完善患者档案统计显示
    - 在个人信息页面集成统计数据显示
    - 实现治疗效果趋势图表（可选）
    - _需求: 3_

- [ ] 15. 系统测试和优化
  - [ ] 15.1 编写单元测试
    - 为Service层业务逻辑编写单元测试
    - 为串口通信服务编写Mock测试
    - 为硬件指令解析器编写测试用例
    - _需求: 10_

  - [ ] 15.2 进行集成测试
    - 测试API接口的完整性和正确性
    - 测试数据库操作的事务性和一致性
    - 测试串口硬件通信的稳定性
    - _需求: 1, 3, 10_

  - [ ] 15.3 进行系统性能优化
    - 优化数据库查询性能和索引配置
    - 优化前端页面加载速度和用户体验
    - 优化串口通信的响应时间和错误处理
    - _需求: 8, 9, 10_

- [ ] 16. 部署和配置
  - [ ] 16.1 配置生产环境部署
    - 配置生产环境的数据库连接和JVM参数
    - 配置前端构建和静态资源部署
    - 配置串口设备驱动和权限
    - _需求: 10_

  - [ ] 16.2 编写部署文档和用户手册
    - 编写系统安装和配置文档
    - 编写用户操作手册和故障排除指南
    - _需求: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11_