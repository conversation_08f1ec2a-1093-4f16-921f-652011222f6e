<template>
  <div class="global-treatment-notifications">
    <!-- 治疗完成通知弹窗 -->
    <div
      v-for="notification in treatmentCompletedNotifications"
      :key="notification.id"
      class="notification-wrapper treatment-completed-wrapper"
      :style="{
        left: `${54 + getNotificationIndex(notification.id) * 130}px`,
        top: '100px',
        zIndex: notification.zIndex || 1000
      }"
      @mousedown="bringToFront(notification.id)"
    >
      <div
        class="treatment-completed-modal"
        :style="{
          transform: `translate(${notification.offsetX || 0}px, ${notification.offsetY || 0}px)`
        }"
        @mousedown="startDrag(notification.id, $event)"
      >
        <!-- 关闭按钮 -->
        <img
          @click="closeNotification(notification.id)"
          :src="closeIcon"
          alt="关闭"
          class="close-button"
        />
        
        <!-- 患者姓名 -->
        <div class="patient-name">{{ notification.patientName }}</div>
        
        <!-- 完成提示文字 -->
        <div class="completion-text">治疗已完成</div>
      </div>
    </div>

    <!-- 治疗头待取回通知弹窗 -->
    <div
      v-for="notification in pickupReminderNotifications"
      :key="notification.id"
      class="notification-wrapper pickup-reminder-wrapper"
      :style="{
        left: `${54 + getNotificationIndex(notification.id) * 130}px`,
        top: '100px',
        zIndex: notification.zIndex || 1000
      }"
      @mousedown="bringToFront(notification.id)"
    >
      <div
        class="pickup-reminder-modal"
        :style="{
          transform: `translate(${notification.offsetX || 0}px, ${notification.offsetY || 0}px)`
        }"
        @mousedown="startDrag(notification.id, $event)"
      >
        <!-- 关闭按钮 -->
        <img
          @click="closeNotification(notification.id)"
          :src="closeIcon"
          alt="关闭"
          class="close-button"
        />
        
        <!-- 待取回提示文字 -->
        <div class="pickup-text">该患者治疗头待取回</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useGlobalTreatmentMonitorStore, NotificationType, type NotificationData } from '@/stores/globalTreatmentMonitor'

// 导入图片
import completedBgImage from '@/assets/images/交互界面/主页/提示某人.png'
import pickupBgImage from '@/assets/images/交互界面/主页/提示治疗头.png'
import closeIcon from '@/assets/images/交互界面/主页/提示×.png'

// 扩展通知数据接口
interface ExtendedNotificationData extends NotificationData {
  zIndex?: number
  offsetX?: number
  offsetY?: number
}

const route = useRoute()
const treatmentMonitorStore = useGlobalTreatmentMonitorStore()

// 状态
const notifications = ref<ExtendedNotificationData[]>([])
let maxZIndex = 1000

// 拖拽状态
let dragState = {
  isDragging: false,
  notificationId: '',
  startX: 0,
  startY: 0,
  startOffsetX: 0,
  startOffsetY: 0
}

// 计算属性
const treatmentCompletedNotifications = computed(() => 
  notifications.value.filter(n => n.type === NotificationType.TREATMENT_COMPLETED)
)

const pickupReminderNotifications = computed(() => 
  notifications.value.filter(n => n.type === NotificationType.PICKUP_REMINDER)
)

// 检查是否应该显示通知
const shouldShowNotifications = computed(() => {
  return treatmentMonitorStore.shouldShowNotifications(route.path)
})

// 获取通知在列表中的索引
const getNotificationIndex = (notificationId: string): number => {
  return notifications.value.findIndex(n => n.id === notificationId)
}

// 监听store中的通知变化
watch(
  () => treatmentMonitorStore.notifications,
  (newNotifications) => {
    // 只在允许显示通知的页面处理
    if (!shouldShowNotifications.value) {
      return
    }

    // 添加新通知
    for (const storeNotification of newNotifications) {
      const existingNotification = notifications.value.find(n => n.id === storeNotification.id)
      if (!existingNotification) {
        const extendedNotification: ExtendedNotificationData = {
          ...storeNotification,
          zIndex: ++maxZIndex,
          offsetX: 0,
          offsetY: 0
        }
        notifications.value.push(extendedNotification)
        console.log(`显示新通知: ${storeNotification.type}`, storeNotification)
      }
    }

    // 移除已经不在store中的通知
    notifications.value = notifications.value.filter(notification =>
      newNotifications.some(storeNotification => storeNotification.id === notification.id)
    )
  },
  { deep: true, immediate: true }
)

// 监听路由变化，在不允许显示通知的页面清除通知
watch(
  () => route.path,
  (newPath) => {
    if (!treatmentMonitorStore.shouldShowNotifications(newPath)) {
      notifications.value = []
    }
  }
)

// 置顶通知
const bringToFront = (notificationId: string): void => {
  const notification = notifications.value.find(n => n.id === notificationId)
  if (notification) {
    notification.zIndex = ++maxZIndex
  }
}

// 关闭通知
const closeNotification = (notificationId: string): void => {
  // 从本地列表移除
  const index = notifications.value.findIndex(n => n.id === notificationId)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
  
  // 从store移除
  treatmentMonitorStore.removeNotification(notificationId)
}

// 开始拖拽
const startDrag = (notificationId: string, event: MouseEvent): void => {
  const notification = notifications.value.find(n => n.id === notificationId)
  if (!notification) return

  dragState = {
    isDragging: true,
    notificationId,
    startX: event.clientX,
    startY: event.clientY,
    startOffsetX: notification.offsetX || 0,
    startOffsetY: notification.offsetY || 0
  }

  // 置顶
  bringToFront(notificationId)

  // 添加全局事件监听
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
  
  // 防止文本选择
  event.preventDefault()
}

// 处理拖拽
const handleDrag = (event: MouseEvent): void => {
  if (!dragState.isDragging) return

  const notification = notifications.value.find(n => n.id === dragState.notificationId)
  if (!notification) return

  const deltaX = event.clientX - dragState.startX
  const deltaY = event.clientY - dragState.startY

  notification.offsetX = dragState.startOffsetX + deltaX
  notification.offsetY = dragState.startOffsetY + deltaY
}

// 停止拖拽
const stopDrag = (): void => {
  dragState.isDragging = false
  
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 生命周期
onMounted(() => {
  console.log('GlobalTreatmentNotifications 组件已挂载')
})

onUnmounted(() => {
  // 清理拖拽事件
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
})
</script>

<style scoped>
/* 通知容器 */
.global-treatment-notifications {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 999;
}

/* 通知包装器 */
.notification-wrapper {
  position: absolute;
  pointer-events: auto;
  cursor: move;
}

/* 治疗完成弹窗样式 */
.treatment-completed-modal {
  width: 612px;
  height: 752px;
  background: url('@/assets/images/交互界面/主页/提示某人.png') no-repeat;
  background-size: 631px 774px;
  position: relative;
  cursor: move;
}

/* 治疗头待取回弹窗样式 */
.pickup-reminder-modal {
  width: 612px;
  height: 752px;
  background: url('@/assets/images/交互界面/主页/提示治疗头.png') no-repeat;
  background-size: 631px 774px;
  position: relative;
  cursor: move;
}

/* 关闭按钮 */
.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  z-index: 10;
}

.close-button:hover {
  opacity: 0.8;
}

/* 患者姓名样式 */
.patient-name {
  position: absolute;
  top: 280px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 42.95px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

/* 完成提示文字样式 */
.completion-text {
  position: absolute;
  top: 350px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24.15px;
  color: #666;
  text-align: center;
}

/* 待取回提示文字样式 */
.pickup-text {
  position: absolute;
  top: 320px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 41.67px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

/* 拖拽时的样式 */
.notification-wrapper:active {
  cursor: grabbing;
}
</style>
