# 新建档案页面数据显示修复需求文档

## 介绍

当前新建档案页面显示的患者数据与数据库中的实际数据不一致，需要修复前后端数据映射问题，确保页面显示的患者基本信息、就诊时间、治疗部位和治疗次数与数据库中的真实数据完全一致。

## 需求

### 需求 1：修复患者基本信息显示

**用户故事：** 作为医疗系统操作员，我希望在新建档案页面看到准确的患者基本信息，以便正确识别和选择患者。

#### 验收标准

1. WHEN 访问新建档案页面 THEN 系统 SHALL 显示患者表(patients)中的真实姓名、年龄、性别信息
2. WHEN 患者姓名在数据库中为具体姓名 THEN 系统 SHALL 显示该具体姓名而不是"??"占位符
3. WHEN 患者年龄在数据库中有具体数值 THEN 系统 SHALL 显示该年龄而不是默认值
4. WHEN 患者性别在数据库中有具体值 THEN 系统 SHALL 显示该性别而不是默认值

### 需求 2：修复就诊时间显示

**用户故事：** 作为医疗系统操作员，我希望看到患者的真实就诊时间，以便了解患者的治疗历史。

#### 验收标准

1. WHEN 显示就诊时间 THEN 系统 SHALL 从records表的created_at字段获取该患者对应档案的建档日期
2. WHEN 患者有多个档案记录 THEN 系统 SHALL 显示最新的建档日期
3. WHEN 患者没有档案记录 THEN 系统 SHALL 显示"无档案"或适当的提示信息
4. WHEN 就诊时间格式显示 THEN 系统 SHALL 使用"YYYY.MM.DD"格式显示日期

### 需求 3：修复治疗部位显示

**用户故事：** 作为医疗系统操作员，我希望看到患者的实际治疗部位，以便了解患者的治疗范围。

#### 验收标准

1. WHEN 显示治疗部位 THEN 系统 SHALL 从body_part_stats表获取该患者档案对应的body_part字段
2. WHEN 患者有多个治疗部位 THEN 系统 SHALL 显示所有部位，用逗号分隔
3. WHEN 患者没有治疗部位记录 THEN 系统 SHALL 显示"待确定"
4. WHEN 治疗部位为标准部位 THEN 系统 SHALL 显示完整的部位名称（肩颈部、腰背部、髋部、上肢、下肢、其他部位）

### 需求 4：修复治疗次数显示

**用户故事：** 作为医疗系统操作员，我希望看到患者的准确治疗次数，以便评估患者的治疗进度。

#### 验收标准

1. WHEN 显示治疗次数 THEN 系统 SHALL 从body_part_stats表的total_usage_count字段计算总治疗次数
2. WHEN 患者有多个部位治疗记录 THEN 系统 SHALL 计算所有部位的total_usage_count总和
3. WHEN 患者没有治疗记录 THEN 系统 SHALL 显示0
4. WHEN 治疗次数为0 THEN 系统 SHALL 显示"0"而不是空白

### 需求 5：修复API数据查询逻辑

**用户故事：** 作为系统开发者，我希望API能够正确关联多个数据表，以便前端获取完整准确的患者信息。

#### 验收标准

1. WHEN 调用获取候选患者列表API THEN 系统 SHALL 执行多表关联查询获取完整数据
2. WHEN 查询患者信息 THEN 系统 SHALL 关联patients、records、body_part_stats三个表
3. WHEN 数据库中存在相关记录 THEN 系统 SHALL 返回真实数据而不是模拟数据
4. WHEN 某个字段在数据库中为空 THEN 系统 SHALL 返回合适的默认值或提示信息

### 需求 6：优化前端数据渲染

**用户故事：** 作为医疗系统操作员，我希望页面加载速度快且数据显示准确，以便高效完成工作。

#### 验收标准

1. WHEN 页面加载完成 THEN 系统 SHALL 在2秒内显示准确的患者数据
2. WHEN 数据更新后 THEN 系统 SHALL 立即刷新页面显示
3. WHEN 网络请求失败 THEN 系统 SHALL 显示明确的错误提示信息
4. WHEN 数据为空 THEN 系统 SHALL 显示友好的空状态提示