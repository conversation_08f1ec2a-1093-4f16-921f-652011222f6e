package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.Patient;
import com.Bone.BoneSys.entity.Record;
import com.Bone.BoneSys.entity.BodyPartStat;
import com.Bone.BoneSys.repository.PatientRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import com.Bone.BoneSys.repository.BodyPartStatRepository;
import com.Bone.BoneSys.repository.ProcessRepository;
import com.Bone.BoneSys.repository.TreatmentDetailRepository;
import com.Bone.BoneSys.entity.TreatmentDetail;
import com.Bone.BoneSys.entity.Process;
import com.Bone.BoneSys.service.AuthService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.*;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import com.Bone.BoneSys.entity.TreatmentHead;

@RestController
@RequestMapping("/api/debug")
@CrossOrigin(origins = "*")
public class DatabaseDebugController {

    @Autowired
    private DataSource dataSource;

    @Autowired private PatientRepository patientRepository;
    @Autowired private RecordRepository recordRepository;
    @Autowired private BodyPartStatRepository bodyPartStatRepository;
    @Autowired private AuthService authService;
    @Autowired private ProcessRepository processRepository;
    @Autowired private TreatmentDetailRepository treatmentDetailRepository;
    @Autowired private TreatmentHeadRepository treatmentHeadRepository;

    @GetMapping("/table-structure")
    public Map<String, Object> getTableStructure() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 查看treatment_heads表结构
            ResultSet rs = stmt.executeQuery("DESCRIBE treatment_heads");
            List<Map<String, Object>> columns = new ArrayList<>();
            
            while (rs.next()) {
                Map<String, Object> column = new HashMap<>();
                column.put("field", rs.getString("Field"));
                column.put("type", rs.getString("Type"));
                column.put("null", rs.getString("Null"));
                column.put("key", rs.getString("Key"));
                column.put("default", rs.getString("Default"));
                column.put("extra", rs.getString("Extra"));
                columns.add(column);
            }
            
            response.put("success", true);
            response.put("table", "treatment_heads");
            response.put("columns", columns);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询表结构失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return response;
    }
    
    @GetMapping("/table-data")
    public Map<String, Object> getTableData() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 查看treatment_heads表数据
            ResultSet rs = stmt.executeQuery("SELECT * FROM treatment_heads LIMIT 5");
            List<Map<String, Object>> rows = new ArrayList<>();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                row.put("head_id", rs.getLong("head_id"));
                row.put("head_number", rs.getInt("head_number"));
                row.put("slot_number", rs.getObject("slot_number"));
                row.put("light_color", rs.getInt("light_color"));
                row.put("realtime_status", rs.getString("realtime_status"));
                row.put("battery_level", rs.getObject("battery_level"));
                row.put("total_usage_count", rs.getInt("total_usage_count"));
                row.put("total_usage_minutes", rs.getInt("total_usage_minutes"));
                row.put("max_usage_count", rs.getObject("max_usage_count"));
                rows.add(row);
            }
            
            response.put("success", true);
            response.put("table", "treatment_heads");
            response.put("rows", rows);
            response.put("count", rows.size());
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询表数据失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return response;
    }

    @GetMapping("/treatment-heads")
    public ApiResponse<List<TreatmentHeadVO>> getTreatmentHeads() {
        List<TreatmentHead> heads = treatmentHeadRepository.findAll();
        List<TreatmentHeadVO> voList = heads.stream().map(TreatmentHeadVO::new).collect(Collectors.toList());
        return ApiResponse.success(voList);
    }

    // 搜索患者及档案（含部位统计）
    @GetMapping("/patient-record/search")
    public ApiResponse<List<PatientRecordVO>> search(@RequestParam(required = false) String patientCardId,
                                                    @RequestParam(required = false) String name) {
        List<Patient> patients = patientRepository.findByPatientCardIdContainingAndNameContaining(
                patientCardId, name, PageRequest.of(0, 20)).getContent();
        List<PatientRecordVO> result = new ArrayList<>();
        for (Patient p : patients) {
            List<Record> records = recordRepository.findByPatientId(p.getId());
            for (Record r : records) {
                List<BodyPartStat> stats = bodyPartStatRepository.findByRecordId(r.getId());
                result.add(new PatientRecordVO(p, r, stats));
            }
        }
        return ApiResponse.success(result);
    }

    // 新建档案（含患者、档案、部位统计）
    @PostMapping("/patient-record/create")
    public ApiResponse<Void> create(@RequestBody CreateRecordRequest req) {
        // 检查患者是否存在
        Optional<Patient> patientOpt = patientRepository.findByPatientCardId(req.getPatientCardId());
        Patient patient = patientOpt.orElseGet(() -> {
            Patient p = new Patient();
            p.setPatientCardId(req.getPatientCardId());
            p.setName(req.getName());
            p.setGender(req.getGender());
            p.setAge(req.getAge());
            p.setContactInfo(req.getContactInfo());
            return patientRepository.save(p);
        });
        // 创建档案
        Record record = new Record();
        record.setRecordNumber(req.getRecordNumber());
        record.setPatient(patient);
        record.setDiagnosisDescription(req.getDiagnosisDescription());
        record.setSessionsCompletedCount(0);
        record = recordRepository.save(record);
        // 创建部位统计
        for (BodyPartStatDTO statDTO : req.getBodyParts()) {
            BodyPartStat stat = new BodyPartStat();
            stat.setRecord(record);
            stat.setBodyPart(statDTO.getBodyPart());
            stat.setTotalUsageCount(statDTO.getTotalUsageCount());
            stat.setTotalDurationMinutes(statDTO.getTotalDurationMinutes());
            bodyPartStatRepository.save(stat);
        }
        return ApiResponse.success();
    }

    @PostMapping("/patient-record/delete")
    public ApiResponse<Void> deleteRecord(@RequestBody DeleteRecordRequest req) {
        try {
            // 校验用户密码
            authService.login(req.getPassword());
            // 查找档案
            Record record = recordRepository.findByRecordNumber(req.getRecordNumber())
                    .orElseThrow(() -> new RuntimeException("档案不存在"));
            recordRepository.delete(record);
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.unauthorized(e.getMessage());
        }
    }

    @GetMapping("/patient-info")
    public ApiResponse<PatientInfoVO> getPatientInfo(@RequestParam String patientCardId) {
        Optional<Patient> patientOpt = patientRepository.findByPatientCardId(patientCardId);
        if (patientOpt.isEmpty()) {
            return ApiResponse.notFound("患者不存在");
        }
        Patient patient = patientOpt.get();
        List<Record> records = recordRepository.findByPatientId(patient.getId());
        List<RecordVO> recordVOList = new ArrayList<>();
        for (Record record : records) {
            List<BodyPartStat> stats = bodyPartStatRepository.findByRecordId(record.getId());
            List<Process> processes = processRepository.findByRecordId(record.getId());
            List<TreatmentDetail> allDetails = new ArrayList<>();
            for (Process process : processes) {
                allDetails.addAll(treatmentDetailRepository.findByProcessId(process.getId()));
            }
            recordVOList.add(new RecordVO(record, stats, allDetails));
        }
        return ApiResponse.success(new PatientInfoVO(patient, recordVOList));
    }

    @PostMapping("/record/update-diagnosis")
    public ApiResponse<Void> updateDiagnosis(@RequestBody UpdateDiagnosisRequest req) {
        Optional<Record> recordOpt = recordRepository.findByRecordNumber(req.getRecordNumber());
        if (recordOpt.isEmpty()) {
            return ApiResponse.notFound("档案不存在");
        }
        Record record = recordOpt.get();
        record.setDiagnosisDescription(req.getDiagnosisDescription());
        recordRepository.save(record);
        return ApiResponse.success();
    }

    @PostMapping("/parameter-setting")
    public ApiResponse<Void> parameterSetting(@RequestBody ParameterSettingRequest req) {
        // 查找档案
        var recordOpt = recordRepository.findByRecordNumber(req.getRecordNumber());
        if (recordOpt.isEmpty()) {
            return ApiResponse.notFound("档案不存在");
        }
        var record = recordOpt.get();
        // 创建治疗进程
        var process = new Process();
        process.setRecord(record);
        process.setTreatmentMode(req.getTreatmentMode());
        process.setStatus(com.Bone.BoneSys.entity.enums.ProcessStatus.IN_PROGRESS);
        process.setStartTime(java.time.LocalDateTime.now());
        // 创建治疗详情
        var details = new java.util.ArrayList<com.Bone.BoneSys.entity.TreatmentDetail>();
        for (TreatmentDetailSettingDTO d : req.getDetails()) {
            var detail = new com.Bone.BoneSys.entity.TreatmentDetail();
            detail.setProcess(process);
            detail.setBodyPart(d.getBodyPart());
            detail.setHeadNumberUsed(d.getHeadNumberUsed());
            detail.setDuration(d.getDuration());
            detail.setIntensity(new java.math.BigDecimal(d.getIntensity()));
            detail.setFrequency(d.getFrequency());
            detail.setPatchType(com.Bone.BoneSys.entity.enums.PatchType.fromString(d.getPatchType()));
            detail.setPatchQuantity(d.getPatchQuantity());
            detail.setStatus(com.Bone.BoneSys.entity.enums.TreatmentDetailStatus.TREATING);
            details.add(detail);
        }
        process.setTreatmentDetails(details);
        processRepository.save(process);
        return ApiResponse.success();
    }

    @GetMapping("/process-management")
    public ApiResponse<List<ProcessManagementVO>> processManagement(@RequestParam(required = false) String patientCardId,
                                                                   @RequestParam(required = false) String name,
                                                                   @RequestParam(required = false) String status) {
        // 查询进程
        com.Bone.BoneSys.entity.enums.ProcessStatus processStatus = null;
        if (status != null && !status.isEmpty()) {
            try {
                processStatus = com.Bone.BoneSys.entity.enums.ProcessStatus.valueOf(status);
            } catch (Exception ignored) {}
        }
        var page = processRepository.findByPatientInfoAndStatus(patientCardId, name, processStatus, org.springframework.data.domain.PageRequest.of(0, 100));
        var result = new java.util.ArrayList<ProcessManagementVO>();
        for (var process : page.getContent()) {
            // 只显示treatment_details中status!=RETURNED的进程
            var details = treatmentDetailRepository.findByProcessId(process.getId());
            boolean hasNotReturned = details.stream().anyMatch(d -> d.getStatus() != com.Bone.BoneSys.entity.enums.TreatmentDetailStatus.RETURNED);
            if (!hasNotReturned) continue;
            var record = process.getRecord();
            var patient = record.getPatient();
            for (var d : details) {
                if (d.getStatus() == com.Bone.BoneSys.entity.enums.TreatmentDetailStatus.RETURNED) continue;
                result.add(new ProcessManagementVO(
                    patient.getPatientCardId(),
                    patient.getName(),
                    d.getBodyPart(),
                    process.getStatus().name(),
                    process.getId()
                ));
            }
        }
        return ApiResponse.success(result);
    }

    @PostMapping("/process/terminate")
    public ApiResponse<Void> terminateProcess(@RequestBody TerminateProcessRequest req) {
        var processOpt = processRepository.findById(req.getProcessId());
        if (processOpt.isEmpty()) {
            return ApiResponse.notFound("进程不存在");
        }
        var process = processOpt.get();
        process.setStatus(com.Bone.BoneSys.entity.enums.ProcessStatus.CANCELLED);
        var details = treatmentDetailRepository.findByProcessId(process.getId());
        for (var d : details) {
            d.setStatus(com.Bone.BoneSys.entity.enums.TreatmentDetailStatus.TERMINATED);
        }
        processRepository.save(process);
        treatmentDetailRepository.saveAll(details);
        return ApiResponse.success();
    }

    @GetMapping("/process/{processId}/details")
    public ApiResponse<java.util.List<TreatmentDetailDTO>> getProcessDetails(@PathVariable Long processId) {
        var details = treatmentDetailRepository.findByProcessId(processId);
        var result = details.stream().map(TreatmentDetailDTO::new).toList();
        return ApiResponse.success(result);
    }

    @Data
    public static class PatientRecordVO {
        private String patientCardId;
        private String name;
        private String gender;
        private String age;
        private String contactInfo;
        private String recordNumber;
        private String diagnosisDescription;
        private Integer sessionsCompletedCount;
        private String createdAt;
        private List<BodyPartStatDTO> bodyParts;
        public PatientRecordVO(Patient p, Record r, List<BodyPartStat> stats) {
            this.patientCardId = p.getPatientCardId();
            this.name = p.getName();
            this.gender = p.getGender();
            this.age = p.getAge();
            this.contactInfo = p.getContactInfo();
            this.recordNumber = r.getRecordNumber();
            this.diagnosisDescription = r.getDiagnosisDescription();
            this.sessionsCompletedCount = r.getSessionsCompletedCount();
            this.createdAt = r.getCreatedAt() == null ? null : r.getCreatedAt().toString();
            this.bodyParts = stats.stream().map(BodyPartStatDTO::new).collect(Collectors.toList());
        }
    }
    @Data
    public static class BodyPartStatDTO {
        private String bodyPart;
        private Integer totalUsageCount;
        private Integer totalDurationMinutes;
        public BodyPartStatDTO(BodyPartStat stat) {
            this.bodyPart = stat.getBodyPart();
            this.totalUsageCount = stat.getTotalUsageCount();
            this.totalDurationMinutes = stat.getTotalDurationMinutes();
        }
        public BodyPartStatDTO() {}
    }
    @Data
    public static class CreateRecordRequest {
        private String patientCardId;
        private String name;
        private String gender;
        private String age;
        private String contactInfo;
        private String recordNumber;
        private String diagnosisDescription;
        private List<BodyPartStatDTO> bodyParts;
    }
    @Data
    public static class DeleteRecordRequest {
        private String recordNumber;
        private String password;
    }

    @Data
    public static class PatientInfoVO {
        private String patientCardId;
        private String name;
        private String gender;
        private String age;
        private String contactInfo;
        private List<RecordVO> records;
        public PatientInfoVO(Patient p, List<RecordVO> records) {
            this.patientCardId = p.getPatientCardId();
            this.name = p.getName();
            this.gender = p.getGender();
            this.age = p.getAge();
            this.contactInfo = p.getContactInfo();
            this.records = records;
        }
    }
    @Data
    public static class RecordVO {
        private String recordNumber;
        private String diagnosisDescription;
        private Integer sessionsCompletedCount;
        private String createdAt;
        private List<BodyPartStatDTO> bodyParts;
        private List<TreatmentDetailDTO> treatmentDetails;
        public RecordVO(Record r, List<BodyPartStat> stats, List<TreatmentDetail> details) {
            this.recordNumber = r.getRecordNumber();
            this.diagnosisDescription = r.getDiagnosisDescription();
            this.sessionsCompletedCount = r.getSessionsCompletedCount();
            this.createdAt = r.getCreatedAt() == null ? null : r.getCreatedAt().toString();
            this.bodyParts = stats.stream().map(BodyPartStatDTO::new).collect(Collectors.toList());
            this.treatmentDetails = details.stream().map(TreatmentDetailDTO::new).collect(Collectors.toList());
        }
    }
    @Data
    public static class TreatmentDetailDTO {
        private String bodyPart;
        private String intensity;
        private Integer headNumberUsed;
        private Integer duration;
        public TreatmentDetailDTO(TreatmentDetail d) {
            this.bodyPart = d.getBodyPart();
            this.intensity = d.getIntensity() == null ? null : d.getIntensity().toString();
            this.headNumberUsed = d.getHeadNumberUsed();
            this.duration = d.getDuration();
        }
    }
    @Data
    public static class UpdateDiagnosisRequest {
        private String recordNumber;
        private String diagnosisDescription;
    }

    @Data
    public static class TreatmentHeadVO {
        private Integer headNumber;
        private Integer slotNumber;
        private Integer lightColor;
        private String lightColorDescription;
        private String realtimeStatus;
        private String statusDescription;
        private Integer batteryLevel;
        private Integer totalUsageCount;
        private Integer totalUsageMinutes;
        private Integer maxUsageCount;
        private String compartmentType;
        public TreatmentHeadVO(TreatmentHead h) {
            this.headNumber = h.getHeadNumber();
            this.slotNumber = h.getSlotNumber();
            this.lightColor = h.getLightColor();
            this.lightColorDescription = h.getLightColorDescription();
            this.realtimeStatus = h.getRealtimeStatus() == null ? null : h.getRealtimeStatus().name();
            this.statusDescription = h.getStatusDescription();
            this.batteryLevel = h.getBatteryLevel();
            this.totalUsageCount = h.getTotalUsageCount();
            this.totalUsageMinutes = h.getTotalUsageMinutes();
            this.maxUsageCount = h.getMaxUsageCount();
            this.compartmentType = h.getCompartmentType();
        }
    }

    @Data
    public static class ParameterSettingRequest {
        private String recordNumber;
        private com.Bone.BoneSys.entity.enums.TreatmentMode treatmentMode;
        private java.util.List<TreatmentDetailSettingDTO> details;
    }
    @Data
    public static class TreatmentDetailSettingDTO {
        private String bodyPart;
        private Integer headNumberUsed;
        private Integer duration;
        private String intensity;
        private Integer frequency;
        private String patchType;
        private Integer patchQuantity;
    }

    @Data
    public static class ProcessManagementVO {
        private String patientCardId;
        private String name;
        private String bodyPart;
        private String status;
        private Long processId;
        public ProcessManagementVO(String patientCardId, String name, String bodyPart, String status, Long processId) {
            this.patientCardId = patientCardId;
            this.name = name;
            this.bodyPart = bodyPart;
            this.status = status;
            this.processId = processId;
        }
    }
    @Data
    public static class TerminateProcessRequest {
        private Long processId;
    }

    @GetMapping("/patient-treatment-stats")
    public ApiResponse<PatientTreatmentStatsVO> getPatientTreatmentStats(@RequestParam String patientCardId) {
        Optional<Patient> patientOpt = patientRepository.findByPatientCardId(patientCardId);
        if (patientOpt.isEmpty()) {
            return ApiResponse.notFound("患者不存在");
        }
        
        Patient patient = patientOpt.get();
        List<Record> records = recordRepository.findByPatientId(patient.getId());
        
        List<RecordStatsVO> recordStats = new ArrayList<>();
        List<BodyPartStatsVO> bodyPartStats = new ArrayList<>();
        
        // 收集所有部位统计数据
        Map<String, BodyPartStatsVO> bodyPartMap = new HashMap<>();
        
        for (Record record : records) {
            List<BodyPartStat> stats = bodyPartStatRepository.findByRecordId(record.getId());
            
            // 构建档案统计信息
            RecordStatsVO recordStat = new RecordStatsVO();
            recordStat.setRecordNumber(record.getRecordNumber());
            recordStat.setDiagnosisDescription(record.getDiagnosisDescription());
            recordStat.setSessionsCompletedCount(record.getSessionsCompletedCount());
            recordStat.setCreatedAt(record.getCreatedAt());
            
            List<BodyPartStatsVO> recordBodyParts = new ArrayList<>();
            for (BodyPartStat stat : stats) {
                BodyPartStatsVO bodyPartVO = new BodyPartStatsVO();
                bodyPartVO.setBodyPart(stat.getBodyPart());
                bodyPartVO.setTotalUsageCount(stat.getTotalUsageCount());
                bodyPartVO.setTotalDurationMinutes(stat.getTotalDurationMinutes());
                recordBodyParts.add(bodyPartVO);
                
                // 合并到总体部位统计
                bodyPartMap.merge(stat.getBodyPart(), bodyPartVO, (existing, newStat) -> {
                    existing.setTotalUsageCount(existing.getTotalUsageCount() + newStat.getTotalUsageCount());
                    existing.setTotalDurationMinutes(existing.getTotalDurationMinutes() + newStat.getTotalDurationMinutes());
                    return existing;
                });
            }
            recordStat.setBodyPartStats(recordBodyParts);
            recordStats.add(recordStat);
        }
        
        bodyPartStats.addAll(bodyPartMap.values());
        
        PatientTreatmentStatsVO result = new PatientTreatmentStatsVO();
        result.setPatient(new PatientBasicVO(patient));
        result.setRecordStats(recordStats);
        result.setOverallBodyPartStats(bodyPartStats);
        
        return ApiResponse.success(result);
    }

    // VO类定义
    @Data
    public static class PatientTreatmentStatsVO {
        private PatientBasicVO patient;
        private List<RecordStatsVO> recordStats;
        private List<BodyPartStatsVO> overallBodyPartStats;
    }
    
    @Data
    public static class PatientBasicVO {
        private String patientCardId;
        private String name;
        private String gender;
        private String age;
        private String contactInfo;
        
        public PatientBasicVO(Patient patient) {
            this.patientCardId = patient.getPatientCardId();
            this.name = patient.getName();
            this.gender = patient.getGender();
            this.age = patient.getAge();
            this.contactInfo = patient.getContactInfo();
        }
    }
    
    @Data
    public static class RecordStatsVO {
        private String recordNumber;
        private String diagnosisDescription;
        private Integer sessionsCompletedCount;
        private LocalDate createdAt;
        private List<BodyPartStatsVO> bodyPartStats;
    }
    
    @Data
    public static class BodyPartStatsVO {
        private String bodyPart;
        private Integer totalUsageCount;
        private Integer totalDurationMinutes;
        
        public BodyPartStatsVO() {}
        
        public BodyPartStatsVO(String bodyPart, Integer totalUsageCount, Integer totalDurationMinutes) {
            this.bodyPart = bodyPart;
            this.totalUsageCount = totalUsageCount;
            this.totalDurationMinutes = totalDurationMinutes;
        }
    }
}
