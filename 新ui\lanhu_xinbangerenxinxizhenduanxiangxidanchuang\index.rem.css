html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.section_1 {
  height: 28.8rem;
  background: url(./img/ae1459a70cc42a3228338312850b789b.png) -0.08rem -0.054rem
    no-repeat;
  background-size: 52.054rem 29.174rem;
  width: 51.2rem;
}

.box_1 {
  height: 28.8rem;
  background: url(./img/8011ab97892b73a8ac95abaff0b3a6f1.png) -0.08rem -0.054rem
    no-repeat;
  background-size: 52.054rem 29.174rem;
  width: 51.2rem;
}

.box_2 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 26px;
  width: 32.507rem;
  height: 23.814rem;
  border: 4px solid rgba(62, 160, 149, 1);
  margin: 2.88rem 0 0 9.654rem;
}

.group_1 {
  width: 17.494rem;
  height: 1.947rem;
  margin: 1.094rem 0 0 12.347rem;
}

.text_1 {
  width: 7.84rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.427rem;
}

.image_1 {
  width: 1.92rem;
  height: 1.947rem;
}

.group_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 26px;
  width: 24.667rem;
  height: 13.547rem;
  border: 4px solid rgba(184, 254, 246, 1);
  margin: 1.787rem 0 0 3.947rem;
}

.group_3 {
  height: 1.84rem;
  background: url(./img/ab196a073e26a39b689f8b4623da4884.png) -0.267rem
    0rem no-repeat;
  background-size: 6.747rem 2.454rem;
  width: 6.187rem;
  margin: 1.467rem 0 2.134rem 13.067rem;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 1.707rem;
  width: 6.027rem;
  margin: 0.08rem 0 0 0.054rem;
}

.text_2 {
  width: 4.08rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 0.427rem;
  margin: 0.427rem 0 0 1.12rem;
}
