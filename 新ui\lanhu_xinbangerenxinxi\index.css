.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.group_1 {
  height: 1078px;
  background: url(./img/4da74a82a76211bac6221a1e0edabddc.png) -3px -2px
    no-repeat;
  background-size: 1920px 1080px;
  width: 1917px;
}

.box_1 {
  width: 1920px;
  height: 1080px;
  background: url(./img/c30b12fbf3037e890c790dcd36bdf001.png) -3px -2px
    no-repeat;
  background-size: 1923px 1082px;
}

.block_1 {
  width: 1004px;
  height: 70px;
  margin: 18px 0 0 100px;
}

.image_1 {
  width: 169px;
  height: 70px;
}

.text_1 {
  width: 295px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 10px;
}

.block_2 {
  width: 1627px;
  height: 275px;
  margin: 70px 0 0 136px;
}

.box_2 {
  width: 941px;
  height: 273px;
  background: url(./img/e974f01faef346da53cab0c398e50bea.png)
    0px -1px no-repeat;
  background-size: 942px 274px;
}

.block_3 {
  background-color: rgba(239, 239, 239, 1);
  border-radius: 50%;
  width: 155px;
  height: 155px;
  border: 1px solid rgba(203, 203, 203, 1);
  margin: 43px 0 0 26px;
}

.block_4 {
  width: 321px;
  height: 156px;
  margin: 23px 0 0 24px;
}

.box_3 {
  width: 321px;
  height: 49px;
}

.text_2 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 14px;
}

.box_4 {
  border-radius: 10px;
  width: 22px;
  height: 49px;
  margin-left: 1px;
}

.text_3 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 14px;
}

.text_4 {
  width: 68px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 14px 0 0 102px;
}

.text-wrapper_1 {
  width: 317px;
  height: 25px;
  margin-top: 21px;
}

.text_5 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
}

.text_6 {
  width: 47px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 1px 0 0 22px;
}

.text_7 {
  width: 63px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 121px;
}

.text-wrapper_2 {
  width: 257px;
  height: 25px;
  margin-top: 36px;
}

.text_8 {
  width: 127px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
}

.text_9 {
  width: 108px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 1px;
}

.block_5 {
  width: 368px;
  height: 220px;
  margin: 37px 27px 0 20px;
}

.text-wrapper_3 {
  width: 275px;
  height: 25px;
}

.text_10 {
  width: 23px;
  height: 24px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 1px;
}

.text_11 {
  width: 68px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 129px;
}

.text_12 {
  width: 33px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 3px 0 0 22px;
}

.text_13 {
  width: 165px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 32px 0 0 1px;
}

.section_1 {
  height: 69px;
  background: url(./img/8868f2acf37f3079fab139b3ff5da01f.png) -10px
    0px no-repeat;
  background-size: 252px 92px;
  width: 232px;
  margin: 73px 0 0 136px;
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 3px 0 0 3px;
}

.text_14 {
  width: 171px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin: 16px 0 0 26px;
}

.box_5 {
  height: 275px;
  background: url(./img/e19c135acf78908ffc6870560b6ac03d.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 636px;
}

.text-wrapper_5 {
  width: 468px;
  height: 26px;
  margin: 38px 0 0 68px;
}

.text_15 {
  width: 92px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 1px;
}

.text_16 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 2px 0 0 17px;
}

.text_17 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 76px;
}

.text_18 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 2px 0 0 19px;
}

.text-wrapper_6 {
  width: 468px;
  height: 25px;
  margin: 30px 0 0 68px;
}

.text_19 {
  width: 92px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
}

.text_20 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 17px;
}

.text_21 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 76px;
}

.text_22 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 19px;
}

.text-wrapper_7 {
  width: 468px;
  height: 29px;
  margin: 30px 0 0 68px;
}

.text_23 {
  width: 120px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 4px;
}

.text_24 {
  width: 83px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 7px 0 0 23px;
}

.text_25 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 60px;
}

.text_26 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 18px;
}

.group_2 {
  width: 232px;
  height: 46px;
  margin: 31px 0 20px 377px;
}

.text-wrapper_8 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  height: 50px;
  border: 2px solid rgba(245, 245, 245, 0.1);
  width: 236px;
  margin: -2px 0 0 -2px;
}

.text_27 {
  width: 195px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin: 12px 0 0 25px;
}

.block_6 {
  position: relative;
  width: 1689px;
  height: 574px;
  background: url(./img/89849cff18b90a27d9b139bda364e8ce.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 30px 0 43px 109px;
}

.text-wrapper_9 {
  width: 1444px;
  height: 33px;
  margin: 73px 0 0 137px;
}

.text_28 {
  width: 143px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text_29 {
  width: 66px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 122px;
}

.text_30 {
  width: 63px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 1px 0 0 118px;
}

.text_31 {
  width: 143px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 93px;
}

.text_32 {
  width: 143px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 89px;
}

.text_33 {
  width: 179px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 66px;
}

.text_34 {
  width: 142px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 77px;
}

.image_2 {
  width: 1476px;
  height: 1px;
  margin: 17px 0 0 113px;
}

.block_7 {
  width: 1384px;
  height: 41px;
  margin: 16px 0 0 165px;
}

.text_35 {
  width: 90px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 12px;
}

.text_36 {
  width: 107px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 128px;
}

.text_37 {
  width: 105px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 73px;
}

.text_38 {
  width: 80px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 105px;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 96px;
  width: 168px;
}

.box_6 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  width: 117px;
  height: 41px;
  border: 0.5px solid rgba(191, 191, 191, 1);
  margin-left: 67px;
}

.text_39 {
  width: 23px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 143px;
}

.text_40 {
  width: 78px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 188px;
}

.image_3 {
  width: 1476px;
  height: 1px;
  margin: 13px 0 0 113px;
}

.block_8 {
  width: 1384px;
  height: 41px;
  margin: 14px 0 0 165px;
}

.text_41 {
  width: 90px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 11px;
}

.text_42 {
  width: 107px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 128px;
}

.text_43 {
  width: 105px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 73px;
}

.text_44 {
  width: 80px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 105px;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 95px;
  width: 168px;
  position: relative;
}

.text_45 {
  width: 42px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 127px;
  margin: 9px 0 0 14px;
}

.box_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(191, 191, 191, 1);
  width: 117px;
  position: absolute;
  left: 67px;
  top: 0;
}

.text-wrapper_10 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
  margin: 8px 0 0 8px;
}

.text_46 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
}

.text_47 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
}

.text_48 {
  width: 23px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 144px;
}

.text_49 {
  width: 78px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 188px;
}

.image-wrapper_1 {
  height: 1px;
  background: url(./img/3d94914d453f437746263c64a7c4d7d9.png)
    0px -1px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 15px 0 0 113px;
}

.image_4 {
  width: 1476px;
  height: 1px;
}

.image-wrapper_2 {
  height: 1px;
  background: url(./img/efc88efd533cf9bdf65716324b2cf8b9.png)
    0px -1px no-repeat;
  background-size: 1476px 2px;
  width: 1476px;
  margin: 70px 0 0 113px;
}

.image_5 {
  width: 1476px;
  height: 1px;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 57px;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 260px;
  margin: 73px 0 107px 1363px;
}

.image_6 {
  width: 235px;
  height: 38px;
  margin: 9px 0 0 10px;
}

.block_9 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(159, 159, 160, 1);
  width: 168px;
  position: absolute;
  left: 948px;
  top: 141px;
}

.text_50 {
  width: 42px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 127px;
  margin: 9px 0 0 14px;
}

.section_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(191, 191, 191, 1);
  width: 117px;
  position: absolute;
  left: 67px;
  top: 0;
}

.text-wrapper_11 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
  margin: 8px 0 0 8px;
}

.text_51 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
}

.text_52 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
}
