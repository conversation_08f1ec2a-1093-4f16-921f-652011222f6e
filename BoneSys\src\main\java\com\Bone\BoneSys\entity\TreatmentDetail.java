package com.Bone.BoneSys.entity;

import com.Bone.BoneSys.entity.enums.PatchType;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 治疗详情实体类
 * 对应数据库表：treatment_details
 */
@Entity
@Table(name = "treatment_details")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = {"process"})
@ToString(exclude = {"process"})
public class TreatmentDetail {
    
    /**
     * 治疗详情ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    /**
     * 关联的治疗进程
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "process_id", nullable = false)
    private Process process;
    
    /**
     * 使用的治疗头编号 (1-20)
     */
    @Column(name = "head_number_used", nullable = false)
    private Integer headNumberUsed;
    
    /**
     * 治疗部位
     */
    @Column(name = "body_part", nullable = false, length = 50)
    private String bodyPart;
    
    /**
     * 治疗时长（分钟）
     */
    @Column(name = "duration", nullable = false)
    private Integer duration;
    
    /**
     * 治疗声强 (mW/cm²)
     */
    @Column(name = "intensity", nullable = false, precision = 5, scale = 2)
    private BigDecimal intensity;
    
    /**
     * 脉冲频率(Hz)
     */
    @Column(name = "frequency", nullable = false)
    private Integer frequency;
    
    /**
     * 贴片类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "patch_type", nullable = false, length = 20)
    private PatchType patchType;
    
    /**
     * 贴片数量 (1-4)
     */
    @Column(name = "patch_quantity", nullable = false)
    private Integer patchQuantity;
    
    /**
     * 治疗详情状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private TreatmentDetailStatus status;
    
    /**
     * 验证贴片数量范围
     */
    @PrePersist
    @PreUpdate
    public void validatePatchQuantity() {
        if (patchQuantity != null && (patchQuantity < 1 || patchQuantity > 4)) {
            throw new IllegalArgumentException("贴片数量必须在1-4之间");
        }
    }
    
    /**
     * 判断是否正在治疗
     */
    @Transient
    public Boolean isTreating() {
        return status == TreatmentDetailStatus.TREATING;
    }
    
    /**
     * 判断是否已完成
     */
    @Transient
    public Boolean isCompleted() {
        return status == TreatmentDetailStatus.COMPLETED;
    }
    
    /**
     * 判断是否等待取回
     */
    @Transient
    public Boolean isAwaitingReturn() {
        return status == TreatmentDetailStatus.AWAITING_RETURN;
    }
}