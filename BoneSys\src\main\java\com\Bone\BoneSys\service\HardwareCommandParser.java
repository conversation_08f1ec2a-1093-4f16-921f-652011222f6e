package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 硬件指令解析器
 * 负责构建发送给硬件的指令和解析硬件返回的响应
 * 
 * 支持的6种硬件指令：
 * 1. TRZI - 查询所有治疗头数据
 * 2. TWSC - 点亮推荐治疗头指示灯
 * 3. TWSN - 关闭推荐治疗头指示灯
 * 4. TWSDT - 向治疗头发送治疗参数（串口）
 * 5. TWS - 向治疗头发送治疗参数并工作
 * 6. TWZO - 关闭治疗头指令
 */
@Service
public class HardwareCommandParser {
    
    private static final Logger logger = LoggerFactory.getLogger(HardwareCommandParser.class);
    
    @Autowired(required = false) // 当硬件模拟器未启用时可能为null
    private HardwareSimulatorService hardwareSimulator;
    
    // 指令结束符
    private static final String COMMAND_TERMINATOR = "\r\n";
    
    /**
     * 1. 构建查询所有治疗头数据指令
     * 发送格式：TRZI\r\n
     */
    public String buildQueryAllTreatmentHeadsCommand() {
        String command = "TRZI" + COMMAND_TERMINATOR;
        logger.debug("Built query all treatment heads command: {}", command.replace("\r\n", "\\r\\n"));
        return command;
    }
    
    /**
     * 解析查询所有治疗头数据的响应
     * 响应格式：TRZI+治疗头数量(2 char)+((治疗头编号(2char)+治疗头电量(2char)+治疗次数(3char)+槽编号（2char))*治疗头数量\r\n
     */
    public List<TreatmentHeadInfo> parseQueryAllTreatmentHeadsResponse(String response) throws SerialCommunicationException {
        try {
            logger.debug("Parsing TRZI response: {}", response);
            
            if (!response.startsWith("TRZI")) {
                throw new SerialCommunicationException("Invalid response format for TRZI command: " + response);
            }
            
            // 移除TRZI前缀和结束符
            String data = response.substring(4);
            // 移除所有可能的结束符
            data = data.replace("\r\n", "").replace("\r", "").replace("\n", "").trim();
            logger.debug("Data after removing prefix and terminator: {}", data);
            
            // 解析治疗头数量（2位字符）
            if (data.length() < 2) {
                throw new SerialCommunicationException("Response too short to contain head count. Data: " + data);
            }
            
            int headCount = Integer.parseInt(data.substring(0, 2));
            logger.debug("Parsed head count: {}", headCount);
            
            List<TreatmentHeadInfo> headInfoList = new ArrayList<>();
            String headsData = data.substring(2);
            logger.debug("Heads data: {}, length: {}", headsData, headsData.length());
            
            // 每个治疗头数据长度：2(编号) + 2(电量) + 3(次数) + 2(槽号) = 9字符
            int expectedLength = headCount * 9;
            if (headsData.length() != expectedLength) {
                // 如果数据长度比预期长，可能是模拟器的bug，尝试截取到预期长度
                if (headsData.length() > expectedLength) {
                    logger.warn("Data length is {} chars longer than expected, trimming to expected length. Expected: {}, Actual: {}", 
                              headsData.length() - expectedLength, expectedLength, headsData.length());
                    headsData = headsData.substring(0, expectedLength);
                    logger.debug("Trimmed heads data: {}, length: {}", headsData, headsData.length());
                } else {
                    logger.error("Length mismatch - Expected: {}, Actual: {}, HeadsData: '{}'", 
                               expectedLength, headsData.length(), headsData);
                    throw new SerialCommunicationException(
                        String.format("Invalid heads data length. Expected: %d, Actual: %d, Data: '%s'", 
                                    expectedLength, headsData.length(), headsData));
                }
            }
            
            // 解析每个治疗头的数据
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 9;
                String headData = headsData.substring(startIndex, startIndex + 9);
                
                int headNumber = Integer.parseInt(headData.substring(0, 2));
                int batteryLevel = Integer.parseInt(headData.substring(2, 4));
                int usageCount = Integer.parseInt(headData.substring(4, 7));
                int slotNumber = Integer.parseInt(headData.substring(7, 9));
                
                TreatmentHeadInfo headInfo = new TreatmentHeadInfo(headNumber, batteryLevel, usageCount, slotNumber);
                
                // 🚨 医疗安全修复：检查硬件模拟器的工作状态
                if (hardwareSimulator != null) {
                    HardwareSimulatorService.SimulatedTreatmentHead simulatedHead = 
                        hardwareSimulator.getSimulatedHead(headNumber);
                    if (simulatedHead != null && simulatedHead.isWorking) {
                        // 如果治疗头正在工作，设置为TREATING状态
                        headInfo.setStatus("TREATING");
                        logger.debug("Head {} is currently working, status set to TREATING", headNumber);
                    }
                }
                
                headInfoList.add(headInfo);
                
                logger.debug("Parsed head info: {}", headInfo);
            }
            
            return headInfoList;
            
        } catch (NumberFormatException e) {
            throw new SerialCommunicationException("Failed to parse numeric values in response: " + response, e);
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TRZI response: " + response, e);
        }
    }
    
    /**
     * 2. 构建点亮推荐治疗头指示灯指令
     * 发送格式：TWSC+治疗头数量(2 char)+(治疗头编号+颜色 3char)*治疗头数量\r\n
     */
    public String buildLightUpCommand(List<TreatmentHeadLightRequest> lightRequests) {
        StringBuilder command = new StringBuilder("TWSC");
        
        // 添加治疗头数量（2位字符，不足补0）
        command.append(String.format("%02d", lightRequests.size()));
        
        // 添加每个治疗头的编号和颜色
        for (TreatmentHeadLightRequest request : lightRequests) {
            command.append(String.format("%02d%d", request.getHeadNumber(), request.getColorCode()));
        }
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built light up command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 解析点亮推荐治疗头指示灯的响应
     * 响应格式：TWSC+治疗头数量(2 char)+(治疗头编号+颜色+槽编号)(5char)*治疗头数量\r\n
     */
    public List<TreatmentHeadLightResponse> parseLightUpResponse(String response) throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWSC")) {
                throw new SerialCommunicationException("Invalid response format for TWSC command: " + response);
            }
            
            String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
            int headCount = Integer.parseInt(data.substring(0, 2));
            
            List<TreatmentHeadLightResponse> responses = new ArrayList<>();
            String headsData = data.substring(2);
            
            // 每个治疗头响应数据长度：2(编号) + 1(颜色) + 2(槽号) = 5字符
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 5;
                String headData = headsData.substring(startIndex, startIndex + 5);
                
                int headNumber = Integer.parseInt(headData.substring(0, 2));
                int colorCode = Integer.parseInt(headData.substring(2, 3));
                int slotNumber = Integer.parseInt(headData.substring(3, 5));
                
                TreatmentHeadLightResponse lightResponse = new TreatmentHeadLightResponse(headNumber, colorCode, slotNumber);
                responses.add(lightResponse);
            }
            
            return responses;
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TWSC response: " + response, e);
        }
    }
    
    /**
     * 3. 构建关闭推荐治疗头指示灯指令
     * 发送格式：TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n
     */
    public String buildTurnOffLightCommand(List<Integer> headNumbers) {
        StringBuilder command = new StringBuilder("TWSN");
        
        // 添加治疗头数量
        command.append(String.format("%02d", headNumbers.size()));
        
        // 添加每个治疗头编号
        for (Integer headNumber : headNumbers) {
            command.append(String.format("%02d", headNumber));
        }
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built turn off light command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 解析关闭推荐治疗头指示灯的响应
     * 响应格式：TWSN+治疗头数量(2 char)+(治疗头编号2char)*治疗头数量\r\n
     */
    public List<Integer> parseTurnOffLightResponse(String response) throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWSN")) {
                throw new SerialCommunicationException("Invalid response format for TWSN command: " + response);
            }
            
            String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
            int headCount = Integer.parseInt(data.substring(0, 2));
            
            List<Integer> headNumbers = new ArrayList<>();
            String headsData = data.substring(2);
            
            // 每个治疗头编号：2字符
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 2;
                int headNumber = Integer.parseInt(headsData.substring(startIndex, startIndex + 2));
                headNumbers.add(headNumber);
            }
            
            return headNumbers;
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TWSN response: " + response, e);
        }
    }
    
    /**
     * 4. 构建发送治疗参数指令（不工作）
     * 发送格式：TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n
     */
    public String buildSendTreatmentParamsCommand(TreatmentParamsRequest request) {
        StringBuilder command = new StringBuilder("TWSDT");
        
        // 设置时间（2字符）
        command.append(String.format("%02d", request.getDuration()));
        
        // 设置声强（3字符）
        command.append(String.format("%03d", request.getIntensity()));
        
        // 固定字符F
        command.append("F");
        
        // 重复周期标记（1字符）：0=100Hz, 1=1000Hz
        command.append(request.getFrequency() == 1000 ? "1" : "0");
        
        // 固定字符ID
        command.append("ID");
        
        // 治疗头数量
        command.append(String.format("%02d", request.getHeadNumbers().size()));
        
        // 治疗头编号列表
        for (Integer headNumber : request.getHeadNumbers()) {
            command.append(String.format("%02d", headNumber));
        }
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built send treatment params command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 5. 构建发送治疗参数并工作指令
     * 发送格式：TWS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)\r\n
     */
    public String buildStartTreatmentCommand(int headNumber, int duration, int intensity, int frequency) {
        StringBuilder command = new StringBuilder("TWS");
        
        // 治疗头编号
        command.append(String.format("%02d", headNumber));
        
        // 设置时间
        command.append(String.format("%02d", duration));
        
        // 设置声强
        command.append(String.format("%03d", intensity));
        
        // 固定字符F
        command.append("F");
        
        // 重复周期标记
        command.append(frequency == 1000 ? "1" : "0");
        
        command.append(COMMAND_TERMINATOR);
        
        String result = command.toString();
        logger.debug("Built start treatment command: {}", result.replace("\r\n", "\\r\\n"));
        return result;
    }
    
    /**
     * 6. 构建关闭治疗头指令
     * 发送格式：TWZO+治疗头编号(2char)+\r\n
     */
    public String buildStopTreatmentCommand(int headNumber) {
        String command = "TWZO" + String.format("%02d", headNumber) + COMMAND_TERMINATOR;
        logger.debug("Built stop treatment command: {}", command.replace("\r\n", "\\r\\n"));
        return command;
    }
    
    /**
     * 验证开始治疗指令的响应
     * 响应格式：TWS+治疗头编号(2char)+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)\r\n
     */
    public boolean validateStartTreatmentResponse(String response, int headNumber, int duration, int intensity, int frequency) 
            throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWS")) {
                throw new SerialCommunicationException("Invalid response format for TWS command: " + response);
            }
            
            String data = response.substring(3).replace(COMMAND_TERMINATOR, "");
            
            // 解析响应数据
            int responseHeadNumber = Integer.parseInt(data.substring(0, 2));
            int responseDuration = Integer.parseInt(data.substring(2, 4));
            int responseIntensity = Integer.parseInt(data.substring(4, 7));
            // 跳过F字符
            int responseFrequencyFlag = Integer.parseInt(data.substring(8, 9));
            int responseFrequency = responseFrequencyFlag == 1 ? 1000 : 100;
            
            // 验证参数是否匹配
            boolean matches = responseHeadNumber == headNumber &&
                            responseDuration == duration &&
                            responseIntensity == intensity &&
                            responseFrequency == frequency;
            
            if (matches) {
                logger.debug("Start treatment response validated successfully for head {}", headNumber);
                return true;
            } else {
                logger.warn("Start treatment response parameters mismatch. Expected: head={}, duration={}, intensity={}, frequency={}. Got: head={}, duration={}, intensity={}, frequency={}", 
                           headNumber, duration, intensity, frequency,
                           responseHeadNumber, responseDuration, responseIntensity, responseFrequency);
                return false;
            }
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to validate TWS response: " + response, e);
        }
    }

    /**
     * 解析关闭治疗头指令的响应
     * 响应格式：TWZO+治疗头编号(2char)+\r\n
     */
    public int parseStopTreatmentResponse(String response) throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWZO")) {
                throw new SerialCommunicationException("Invalid response format for TWZO command: " + response);
            }
            
            String data = response.substring(4).replace(COMMAND_TERMINATOR, "");
            return Integer.parseInt(data);
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to parse TWZO response: " + response, e);
        }
    }
    
    /**
     * 解析发送治疗参数指令的响应
     * 响应格式：TWSDT+(设置时间2 char) +(设置声强3char)+F+重复周期标记(1 char)+ID+治疗头数量(2char)+治疗头编号(2char)*治疗头数量\r\n
     */
    public boolean validateSendTreatmentParamsResponse(String response, TreatmentParamsRequest originalRequest) 
            throws SerialCommunicationException {
        try {
            if (!response.startsWith("TWSDT")) {
                throw new SerialCommunicationException("Invalid response format for TWSDT command: " + response);
            }
            
            String data = response.substring(5).replace(COMMAND_TERMINATOR, "");
            
            // 解析响应中的参数
            int duration = Integer.parseInt(data.substring(0, 2));
            int intensity = Integer.parseInt(data.substring(2, 5));
            String frequencyFlag = data.substring(6, 7);
            int headCount = Integer.parseInt(data.substring(9, 11));
            
            // 验证参数是否匹配
            boolean durationMatch = duration == originalRequest.getDuration();
            boolean intensityMatch = intensity == originalRequest.getIntensity();
            boolean frequencyMatch = frequencyFlag.equals(originalRequest.getFrequency() == 1000 ? "1" : "0");
            boolean headCountMatch = headCount == originalRequest.getHeadNumbers().size();
            
            if (!durationMatch || !intensityMatch || !frequencyMatch || !headCountMatch) {
                logger.warn("TWSDT response parameters mismatch. Duration: {}/{}, Intensity: {}/{}, Frequency: {}/{}, HeadCount: {}/{}", 
                    duration, originalRequest.getDuration(),
                    intensity, originalRequest.getIntensity(),
                    frequencyFlag, (originalRequest.getFrequency() == 1000 ? "1" : "0"),
                    headCount, originalRequest.getHeadNumbers().size());
                return false;
            }
            
            // 验证治疗头编号
            String headsData = data.substring(11);
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 2;
                int responseHeadNumber = Integer.parseInt(headsData.substring(startIndex, startIndex + 2));
                if (!originalRequest.getHeadNumbers().contains(responseHeadNumber)) {
                    logger.warn("TWSDT response contains unexpected head number: {}", responseHeadNumber);
                    return false;
                }
            }
            
            logger.debug("TWSDT response validation successful");
            return true;
            
        } catch (Exception e) {
            throw new SerialCommunicationException("Failed to validate TWSDT response: " + response, e);
        }
    }
    

    
    /**
     * 验证响应格式是否正确
     */
    public boolean isValidResponse(String response, String expectedPrefix) {
        return response != null && 
               response.startsWith(expectedPrefix) && 
               response.endsWith(COMMAND_TERMINATOR);
    }
    
    /**
     * 验证指令格式
     */
    public boolean validateCommandFormat(String command) {
        if (command == null || !command.endsWith(COMMAND_TERMINATOR)) {
            return false;
        }
        
        String cmd = command.replace(COMMAND_TERMINATOR, "");
        
        // 验证各种指令格式
        if (cmd.equals("TRZI")) {
            return true;
        } else if (cmd.startsWith("TWSC") && cmd.length() >= 6) {
            return validateTWSCFormat(cmd);
        } else if (cmd.startsWith("TWSN") && cmd.length() >= 6) {
            return validateTWSNFormat(cmd);
        } else if (cmd.startsWith("TWSDT") && cmd.length() >= 13) {
            return validateTWSDTFormat(cmd);
        } else if (cmd.startsWith("TWS") && cmd.length() == 12) {
            return validateTWSFormat(cmd);
        } else if (cmd.startsWith("TWZO") && cmd.length() == 6) {
            return validateTWZOFormat(cmd);
        }
        
        return false;
    }
    
    private boolean validateTWSCFormat(String cmd) {
        try {
            int headCount = Integer.parseInt(cmd.substring(4, 6));
            return cmd.length() == 6 + headCount * 3;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private boolean validateTWSNFormat(String cmd) {
        try {
            int headCount = Integer.parseInt(cmd.substring(4, 6));
            return cmd.length() == 6 + headCount * 2;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private boolean validateTWSDTFormat(String cmd) {
        try {
            // TWSDT + 时间(2) + 声强(3) + F + 频率(1) + ID + 数量(2) + 编号列表
            if (!cmd.substring(9, 10).equals("F") || !cmd.substring(11, 13).equals("ID")) {
                return false;
            }
            int headCount = Integer.parseInt(cmd.substring(13, 15));
            return cmd.length() == 15 + headCount * 2;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    private boolean validateTWSFormat(String cmd) {
        try {
            // TWS + 编号(2) + 时间(2) + 声强(3) + F + 频率(1)
            return cmd.substring(8, 9).equals("F");
        } catch (Exception e) {
            return false;
        }
    }
    
    private boolean validateTWZOFormat(String cmd) {
        try {
            Integer.parseInt(cmd.substring(4, 6));
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}