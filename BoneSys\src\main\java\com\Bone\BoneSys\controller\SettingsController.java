package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.service.SettingsService;
import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.dto.PasswordResetRequest;
import com.Bone.BoneSys.dto.PasswordChangeRequest;
import com.Bone.BoneSys.dto.SystemSettingsRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/settings")
@CrossOrigin(origins = "*")
public class SettingsController {

    @Autowired
    private SettingsService settingsService;

    /**
     * 密码重置
     */
    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<String>> resetPassword(@RequestBody PasswordResetRequest request) {
        try {
            boolean success = settingsService.resetPassword(request.getFactoryPassword());
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("密码已重置为123456", null));
            } else {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "厂家密码错误"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body(ApiResponse.error(500, "密码重置失败"));
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public ResponseEntity<ApiResponse<String>> changePassword(@RequestBody PasswordChangeRequest request) {
        try {
            boolean success = settingsService.changePassword(request.getOldPassword(), request.getNewPassword());
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("密码修改成功", null));
            } else {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "原密码错误"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body(ApiResponse.error(500, "密码修改失败"));
        }
    }

    /**
     * 获取系统设置
     */
    @GetMapping("/system")
    public ResponseEntity<ApiResponse<Object>> getSystemSettings() {
        try {
            Object settings = settingsService.getSystemSettings();
            return ResponseEntity.ok(ApiResponse.success("系统设置获取成功", settings));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(ApiResponse.error(500, "系统设置获取失败"));
        }
    }

    /**
     * 更新系统设置
     */
    @PutMapping("/system")
    public ResponseEntity<ApiResponse<String>> updateSystemSettings(@RequestBody SystemSettingsRequest request) {
        try {
            settingsService.updateSystemSettings(request);
            return ResponseEntity.ok(ApiResponse.success("系统设置更新成功", null));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(ApiResponse.error(500, "系统设置更新失败"));
        }
    }
}
