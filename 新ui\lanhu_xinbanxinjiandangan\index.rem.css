html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.group_1 {
  width: 51.2rem;
  height: 28.8rem;
  background: url(./img/729506f7b0e9c8ed38af1c322c6e9cba.png) -0.027rem
    0rem no-repeat;
  background-size: 51.227rem 28.8rem;
}

.section_1 {
  width: 26.614rem;
  height: 1.867rem;
  margin: 0.48rem 0 0 2.774rem;
}

.image_1 {
  width: 4.507rem;
  height: 1.867rem;
}

.text_1 {
  width: 7.84rem;
  height: 1.307rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.214rem;
}

.text-wrapper_1 {
  height: 2.534rem;
  background: url(./img/f76b271dd10ae46f4ad862f0468cb5e2.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 21.067rem;
  margin: 1.227rem 0 0 3.467rem;
}

.text_2 {
  width: 2.347rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.4rem 0 0 17.467rem;
}

.block_2 {
  position: relative;
  width: 45.04rem;
  height: 22.32rem;
  background: url(./img/b7f4af218e44f0d3b8bc57a569417bba.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 0 0 0.374rem 2.987rem;
}

.text-wrapper_6 {
  width: 36.48rem;
  height: 0.907rem;
  margin: 1.547rem 0 0 4.48rem;
}

.text_3 {
  width: 4.16rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_4 {
  width: 1.894rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.987rem;
}

.text_5 {
  width: 1.974rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.507rem;
}

.text_6 {
  width: 1.947rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 1.627rem;
}

.text_7 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 4.747rem;
}

.text_8 {
  width: 4.187rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.16rem;
}

.text_9 {
  width: 2rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.187rem;
}

.box_1 {
  position: relative;
  width: 39.36rem;
  height: 13.28rem;
  margin: 0.427rem 0 0 3.094rem;
}

.text_10 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 10.054rem 0 0 1.467rem;
}

.text_11 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 8.16rem 0 0 -4.054rem;
}

.text_12 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 6.24rem 0 0 -4.054rem;
}

.text_13 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 4.4rem 0 0 -4.054rem;
}

.text_14 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 2.534rem 0 0 -4.054rem;
}

.text_15 {
  width: 4.054rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.56rem 0 0 -4.054rem;
}

.text_16 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 10rem 0 0 1.947rem;
}

.text_17 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 8.107rem 0 0 -4.107rem;
}

.text_18 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 6.187rem 0 0 -4.107rem;
}

.text_19 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 4.347rem 0 0 -4.107rem;
}

.text_20 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 2.48rem 0 0 -4.107rem;
}

.text_21 {
  width: 4.107rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 -4.107rem;
}

.text_22 {
  width: 1.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 10.054rem 0 0 1.76rem;
}

.text_23 {
  width: 1.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 8.16rem 0 0 -1.174rem;
}

.text_24 {
  width: 1.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 6.24rem 0 0 -1.174rem;
}

.text_25 {
  width: 1.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 4.4rem 0 0 -1.174rem;
}

.text_26 {
  width: 1.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 2.534rem 0 0 -1.174rem;
}

.text_27 {
  width: 1.174rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.56rem 0 0 -1.174rem;
}

.text_28 {
  width: 0.854rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 10.054rem 0 0 2.694rem;
}

.text_29 {
  width: 0.854rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 8.16rem 0 0 -0.854rem;
}

.text_30 {
  width: 0.854rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 6.24rem 0 0 -0.854rem;
}

.text_31 {
  width: 0.854rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 4.4rem 0 0 -0.854rem;
}

.text_32 {
  width: 0.854rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 2.534rem 0 0 -0.854rem;
}

.text_33 {
  width: 0.854rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.56rem 0 0 -0.854rem;
}

.text_34 {
  width: 5.627rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 10.054rem 0 0 4.427rem;
}

.text_35 {
  width: 5.627rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 8.16rem 0 0 -5.627rem;
}

.text_36 {
  width: 5.627rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 6.24rem 0 0 -5.627rem;
}

.text_37 {
  width: 5.627rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 4.4rem 0 0 -5.627rem;
}

.text_38 {
  width: 5.627rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 2.534rem 0 0 -5.627rem;
}

.text_39 {
  width: 5.627rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.56rem 0 0 -5.627rem;
}

.text_40 {
  width: 4.187rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 10rem 0 0 1.387rem;
}

.text_41 {
  width: 3.067rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 2.454rem 0 0 -3.627rem;
}

.text_42 {
  width: 3.067rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 -3.067rem;
}

.text_43 {
  width: 1.974rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 8.107rem 0 0 -2.507rem;
}

.text_44 {
  width: 1.974rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 6.16rem 0 0 -1.974rem;
}

.text_45 {
  width: 1.974rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 4.32rem 0 0 -1.974rem;
}

.text_46 {
  width: 0.4rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 10.054rem 0 0 4.08rem;
}

.text_47 {
  width: 0.4rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 8.16rem 0 0 -0.4rem;
}

.text_48 {
  width: 0.4rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 6.24rem 0 0 -0.4rem;
}

.text_49 {
  width: 0.4rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 4.4rem 0 0 -0.4rem;
}

.text_50 {
  width: 0.4rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 2.534rem 0 0 -0.4rem;
}

.text_51 {
  width: 0.4rem;
  height: 0.72rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.56rem 2.294rem 0 -0.4rem;
}

.image_2 {
  position: absolute;
  left: 0;
  top: 0;
  width: 39.36rem;
  height: 13.28rem;
}

.box_2 {
  width: 31.84rem;
  height: 2.747rem;
  margin: 0.614rem 0 2.8rem 11.52rem;
}

.image_3 {
  width: 5.6rem;
  height: 1.894rem;
  margin-top: 0.854rem;
}

.image_4 {
  width: 5.6rem;
  height: 1.894rem;
  margin: 0.854rem 0 0 10.907rem;
}

.image-wrapper_1 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 1.52rem;
  border: 1px solid rgba(238, 238, 238, 0.63);
  margin-left: 2.8rem;
  width: 6.934rem;
}

.image_5 {
  width: 6.267rem;
  height: 1.014rem;
  margin: 0.24rem 0 0 0.267rem;
}

.text-wrapper_3 {
  height: 2.054rem;
  background: url(./img/ee2226d2bc022e95a4140f84b614e115.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 5.76rem;
  position: absolute;
  left: 11.44rem;
  top: 17.547rem;
}

.text_52 {
  width: 2.347rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.56rem 0 0 1.76rem;
}

.text-wrapper_4 {
  height: 2.054rem;
  background: url(./img/1c8e5eddae0e5143f83eda86a9623880.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 5.76rem;
  position: absolute;
  left: 27.947rem;
  top: 17.547rem;
}

.text_53 {
  width: 4.614rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 0 0 0.587rem;
}
