package com.Bone.BoneSys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/direct-sql")
@CrossOrigin(origins = "*")
public class DirectSqlController {

    @Autowired
    private DataSource dataSource;

    @PostMapping("/complete-setup")
    public ResponseEntity<Map<String, Object>> completeSetup() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 1. Create all tables
            createAllTables(stmt);
            
            // 2. Insert basic data (users and treatment heads)
            insertBasicData(stmt);
            
            // 3. Insert test data
            insertTestData(stmt);
            
            response.put("success", true);
            response.put("message", "Complete database setup finished successfully");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to complete setup: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/verify-setup")
    public ResponseEntity<Map<String, Object>> verifySetup() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            Map<String, Object> counts = new HashMap<>();
            
            String[] tables = {"users", "patients", "records", "treatment_heads", 
                             "processes", "treatment_details", "body_part_stats"};
            
            for (String table : tables) {
                try {
                    var rs = stmt.executeQuery("SELECT COUNT(*) FROM " + table);
                    if (rs.next()) {
                        counts.put(table, rs.getLong(1));
                    }
                } catch (Exception e) {
                    counts.put(table, "Error: " + e.getMessage());
                }
            }
            
            response.put("success", true);
            response.put("message", "Database verification completed");
            response.put("tableCounts", counts);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to verify setup: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
    
    private void createAllTables(Statement stmt) throws Exception {
        // Create users table
        stmt.execute("""
            CREATE TABLE IF NOT EXISTS `users` (
              `id` INT NOT NULL,
              `username` VARCHAR(50) NOT NULL,
              `password` VARCHAR(50) NOT NULL DEFAULT '123456' COMMENT 'User password (plain text)',
              `last_updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE INDEX `username_UNIQUE` (`username` ASC)
            ) ENGINE = InnoDB COMMENT = 'User table'
            """);
        
        // Create patients table
        stmt.execute("""
            CREATE TABLE IF NOT EXISTS `patients` (
              `id` BIGINT NOT NULL AUTO_INCREMENT,
              `patient_card_id` VARCHAR(50) NOT NULL COMMENT 'Patient card ID',
              `name` VARCHAR(100) NOT NULL COMMENT 'Patient name',
              `gender` VARCHAR(10) NULL DEFAULT NULL,
              `age` VARCHAR(10) NULL DEFAULT NULL,
              `contact_info` VARCHAR(255) NULL DEFAULT NULL,
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              UNIQUE INDEX `patient_card_id_UNIQUE` (`patient_card_id` ASC)
            ) ENGINE = InnoDB COMMENT = 'Patient information table'
            """);
        
        // Create records table
        stmt.execute("""
            CREATE TABLE IF NOT EXISTS `records` (
              `id` BIGINT NOT NULL AUTO_INCREMENT,
              `record_number` VARCHAR(50) NOT NULL COMMENT 'Record unique number',
              `patient_id` BIGINT NOT NULL,
              `diagnosis_description` TEXT NULL DEFAULT NULL,
              `sessions_completed_count` INT NOT NULL DEFAULT 0 COMMENT 'Total completed sessions',
              `created_at` DATE NOT NULL COMMENT 'Record creation date',
              PRIMARY KEY (`id`),
              UNIQUE INDEX `record_number_UNIQUE` (`record_number` ASC),
              INDEX `fk_records_patients_idx` (`patient_id` ASC),
              CONSTRAINT `fk_records_patients`
                FOREIGN KEY (`patient_id`)
                REFERENCES `patients` (`id`)
                ON DELETE RESTRICT
                ON UPDATE CASCADE
            ) ENGINE = InnoDB COMMENT = 'Diagnosis record table'
            """);
        
        // Create treatment_heads table
        stmt.execute("""
            CREATE TABLE IF NOT EXISTS `treatment_heads` (
              `head_id` BIGINT NOT NULL AUTO_INCREMENT,
              `head_number` INT NOT NULL COMMENT 'Treatment head number (1-20)',
              `slot_number` INT NULL DEFAULT NULL COMMENT 'Slot number (1-10 shallow upper, 11-20 deep lower)',
              `light_color` INT NOT NULL DEFAULT 0 COMMENT 'Light color (0=off, 1=orange, 2=blue, 3=green)',
              `realtime_status` VARCHAR(20) NOT NULL DEFAULT 'CHARGING' COMMENT 'Status: CHARGING, CHARGED, TREATING',
              `battery_level` INT NULL DEFAULT NULL COMMENT 'Battery level (0-100)',
              `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT 'Total usage count',
              `total_usage_minutes` INT NOT NULL DEFAULT 0 COMMENT 'Total usage minutes',
              `max_usage_count` INT NOT NULL DEFAULT 500 COMMENT 'Maximum usage count',
              PRIMARY KEY (`head_id`),
              UNIQUE INDEX `head_number_UNIQUE` (`head_number` ASC),
              UNIQUE INDEX `slot_number_UNIQUE` (`slot_number` ASC)
            ) ENGINE = InnoDB COMMENT = 'Treatment head management table'
            """);
        
        // Create processes table
        stmt.execute("""
            CREATE TABLE IF NOT EXISTS `processes` (
              `id` BIGINT NOT NULL AUTO_INCREMENT,
              `record_id` BIGINT NOT NULL,
              `treatment_mode` VARCHAR(20) NOT NULL COMMENT 'Treatment mode: ON_SITE, TAKE_AWAY',
              `status` VARCHAR(20) NOT NULL COMMENT 'Process status: IN_PROGRESS, COMPLETED, CANCELLED',
              `start_time` DATETIME NOT NULL,
              `end_time` DATETIME NULL DEFAULT NULL,
              PRIMARY KEY (`id`),
              INDEX `fk_processes_records_idx` (`record_id` ASC),
              CONSTRAINT `fk_processes_records`
                FOREIGN KEY (`record_id`)
                REFERENCES `records` (`id`)
                ON DELETE CASCADE
                ON UPDATE CASCADE
            ) ENGINE = InnoDB COMMENT = 'Treatment process table'
            """);
        
        // Create treatment_details table
        stmt.execute("""
            CREATE TABLE IF NOT EXISTS `treatment_details` (
              `id` BIGINT NOT NULL AUTO_INCREMENT,
              `process_id` BIGINT NOT NULL,
              `head_number_used` INT NOT NULL COMMENT 'Treatment head number used (1-20)',
              `body_part` VARCHAR(50) NOT NULL COMMENT 'Body part: shoulder-neck, waist-back, hip, upper-limb, lower-limb, other',
              `duration` INT NOT NULL COMMENT 'Treatment duration (minutes)',
              `intensity` DECIMAL(5,2) NOT NULL COMMENT 'Treatment intensity (mW/cm²)',
              `frequency` INT NOT NULL COMMENT 'Pulse frequency (Hz)',
              `patch_type` VARCHAR(20) NOT NULL COMMENT 'Patch type: DEEP, SHALLOW',
              `patch_quantity` INT NOT NULL COMMENT 'Patch quantity (1-4)',
              `status` VARCHAR(20) NOT NULL COMMENT 'Detail status: TREATING, COMPLETED, AWAITING_RETURN, RETURNED, TERMINATED',
              PRIMARY KEY (`id`),
              INDEX `fk_details_processes_idx` (`process_id` ASC),
              INDEX `fk_details_heads_idx` (`head_number_used` ASC),
              CONSTRAINT `fk_details_processes`
                FOREIGN KEY (`process_id`)
                REFERENCES `processes` (`id`)
                ON DELETE CASCADE
                ON UPDATE CASCADE,
              CONSTRAINT `fk_details_heads`
                FOREIGN KEY (`head_number_used`)
                REFERENCES `treatment_heads` (`head_number`)
                ON DELETE RESTRICT
                ON UPDATE CASCADE
            ) ENGINE = InnoDB COMMENT = 'Treatment detail table'
            """);
        
        // Create body_part_stats table
        stmt.execute("""
            CREATE TABLE IF NOT EXISTS `body_part_stats` (
              `id` BIGINT NOT NULL AUTO_INCREMENT,
              `record_id` BIGINT NOT NULL,
              `body_part` VARCHAR(50) NOT NULL,
              `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT 'Total usage count for this body part',
              `total_duration_minutes` INT NOT NULL DEFAULT 0 COMMENT 'Total duration minutes for this body part',
              PRIMARY KEY (`id`),
              INDEX `fk_stats_records_idx` (`record_id` ASC),
              UNIQUE INDEX `record_body_part_UNIQUE` (`record_id` ASC, `body_part` ASC),
              CONSTRAINT `fk_stats_records`
                FOREIGN KEY (`record_id`)
                REFERENCES `records` (`id`)
                ON DELETE CASCADE
                ON UPDATE CASCADE
            ) ENGINE = InnoDB COMMENT = 'Body part statistics table'
            """);
    }
    
    private void insertBasicData(Statement stmt) throws Exception {
        // Insert default user
        stmt.execute("""
            INSERT INTO `users` (`id`, `username`, `password`)
            VALUES (1, 'admin', '123456')
            ON DUPLICATE KEY UPDATE username = VALUES(username)
            """);
        
        // Insert 20 treatment heads
        String[] treatmentHeadInserts = {
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (1, 1, 0, 'CHARGING', 50, 5, 100, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (2, 2, 0, 'CHARGING', 60, 4, 80, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (3, 3, 0, 'CHARGED', 100, 10, 200, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (4, 4, 0, 'CHARGED', 100, 8, 160, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (5, 5, 0, 'CHARGING', 70, 3, 60, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (6, 6, 0, 'CHARGED', 100, 12, 240, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (7, 7, 0, 'TREATING', 85, 6, 120, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (8, 8, 0, 'CHARGED', 100, 9, 180, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (9, 9, 0, 'CHARGING', 80, 7, 140, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (10, 10, 0, 'CHARGED', 100, 5, 100, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (11, 11, 0, 'CHARGING', 65, 8, 160, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (12, 12, 0, 'CHARGED', 100, 11, 220, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (13, 13, 0, 'CHARGING', 55, 4, 80, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (14, 14, 0, 'CHARGED', 100, 6, 120, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (15, 15, 0, 'TREATING', 90, 9, 180, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (16, 16, 0, 'CHARGED', 100, 13, 260, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (17, 17, 0, 'CHARGING', 75, 5, 100, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (18, 18, 0, 'CHARGED', 100, 10, 200, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (19, 19, 0, 'CHARGING', 85, 7, 140, 500)",
            "INSERT IGNORE INTO `treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`, `max_usage_count`) VALUES (20, 20, 0, 'CHARGED', 100, 8, 160, 500)"
        };
        
        for (String sql : treatmentHeadInserts) {
            stmt.execute(sql);
        }
    }
    
    private void insertTestData(Statement stmt) throws Exception {
        // Insert patients
        stmt.execute("""
            INSERT INTO `patients` (`patient_card_id`, `name`, `gender`, `age`, `contact_info`, `created_at`) VALUES
            ('P20250101001', 'Zhang San', 'Male', '45', '13800138001', '2025-01-01 09:00:00'),
            ('P20250102002', 'Li Si', 'Female', '38', '13800138002', '2025-01-02 10:30:00'),
            ('P20250103003', 'Wang Wu', 'Male', '52', '13800138003', '2025-01-03 14:15:00'),
            ('P20250104004', 'Zhao Liu', 'Female', '29', '13800138004', '2025-01-04 16:45:00'),
            ('P20250105005', 'Qian Qi', 'Male', '61', '13800138005', '2025-01-05 08:20:00')
            ON DUPLICATE KEY UPDATE name = VALUES(name)
            """);
        
        // Insert records
        stmt.execute("""
            INSERT INTO `records` (`record_number`, `patient_id`, `diagnosis_description`, `sessions_completed_count`, `created_at`) VALUES
            ('R20250101001', 1, 'Cervical spondylosis, C5-C6 disc herniation', 8, '2025-01-01'),
            ('R20250102001', 2, 'Lumbar disc herniation, L4-L5', 12, '2025-01-02'),
            ('R20250103001', 3, 'Frozen shoulder, right shoulder joint limited mobility', 6, '2025-01-03'),
            ('R20250104001', 4, 'Knee arthritis, bilateral knee pain', 15, '2025-01-04'),
            ('R20250105001', 5, 'Lumbar muscle strain, chronic low back pain', 10, '2025-01-05')
            ON DUPLICATE KEY UPDATE diagnosis_description = VALUES(diagnosis_description)
            """);
        
        // Insert processes
        stmt.execute("""
            INSERT INTO `processes` (`record_id`, `treatment_mode`, `status`, `start_time`, `end_time`) VALUES
            (1, 'ON_SITE', 'COMPLETED', '2025-01-01 09:30:00', '2025-01-01 10:30:00'),
            (2, 'TAKE_AWAY', 'COMPLETED', '2025-01-02 11:00:00', '2025-01-02 11:20:00'),
            (3, 'ON_SITE', 'IN_PROGRESS', '2025-01-26 09:00:00', NULL),
            (4, 'ON_SITE', 'IN_PROGRESS', '2025-01-26 10:30:00', NULL),
            (5, 'TAKE_AWAY', 'CANCELLED', '2025-01-05 09:00:00', '2025-01-05 09:15:00')
            """);
        
        // Insert treatment details
        stmt.execute("""
            INSERT INTO `treatment_details` (`process_id`, `head_number_used`, `body_part`, `duration`, `intensity`, `frequency`, `patch_type`, `patch_quantity`, `status`) VALUES
            (1, 3, 'Shoulder-Neck', 20, 500.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
            (2, 12, 'Waist-Back', 25, 600.00, 1000, 'DEEP', 4, 'RETURNED'),
            (3, 4, 'Shoulder-Neck', 20, 500.00, 1000, 'SHALLOW', 2, 'TREATING'),
            (4, 10, 'Upper-Limb', 25, 480.00, 1000, 'SHALLOW', 1, 'TREATING'),
            (5, 15, 'Waist-Back', 30, 600.00, 1000, 'DEEP', 4, 'TERMINATED')
            """);
        
        // Insert body part stats
        stmt.execute("""
            INSERT INTO `body_part_stats` (`record_id`, `body_part`, `total_usage_count`, `total_duration_minutes`) VALUES
            (1, 'Shoulder-Neck', 1, 20),
            (2, 'Waist-Back', 1, 25),
            (3, 'Shoulder-Neck', 1, 20),
            (4, 'Upper-Limb', 1, 25),
            (5, 'Waist-Back', 1, 30)
            """);
    }
}