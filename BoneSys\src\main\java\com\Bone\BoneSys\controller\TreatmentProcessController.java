package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentDetailStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.entity.enums.PatchType;
import com.Bone.BoneSys.repository.*;
import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.HardwareService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/treatment-process")
@Slf4j
public class TreatmentProcessController {

    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;
    
    @Autowired
    private RecordRepository recordRepository;

    @Autowired
    private HardwareService hardwareService;
    
    @Autowired
    private BodyPartStatRepository bodyPartStatRepository;

    /**
     * 开始治疗进程
     * POST /api/treatment-process/start
     */
    @PostMapping("/start")
    public ApiResponse<StartTreatmentResponse> startTreatment(@RequestBody StartTreatmentRequest request) {
        try {
            log.info("API request: start treatment process for record {}", request.getRecordId());

            // 1. 验证档案是否存在
            var recordOpt = recordRepository.findById(request.getRecordId());
            if (recordOpt.isEmpty()) {
                return ApiResponse.notFound("档案不存在");
            }
            var record = recordOpt.get();

            // 2. 创建治疗进程
            var process = new com.Bone.BoneSys.entity.Process();
            process.setRecord(record);
            process.setTreatmentMode(request.getTreatmentMode());
            process.setStatus(ProcessStatus.IN_PROGRESS);
            process.setStartTime(LocalDateTime.now());
            process = processRepository.save(process);

            // 3. 创建治疗详情并启动硬件治疗
            var details = new java.util.ArrayList<TreatmentDetail>();
            var startedHeads = new java.util.ArrayList<Integer>();

            for (TreatmentDetailRequest detailReq : request.getTreatmentDetails()) {
                // 创建治疗详情记录
                var detail = new TreatmentDetail();
                detail.setProcess(process);
                detail.setBodyPart(detailReq.getBodyPart());
                detail.setHeadNumberUsed(detailReq.getHeadNumber());
                detail.setDuration(detailReq.getDuration());
                detail.setIntensity(new BigDecimal(detailReq.getIntensity()));
                detail.setFrequency(detailReq.getFrequency());
                detail.setPatchType(detailReq.getPatchType());
                detail.setPatchQuantity(detailReq.getPatchQuantity());
                detail.setStatus(TreatmentDetailStatus.TREATING);
                details.add(detail);

                // 启动硬件治疗
                try {
                    boolean success = hardwareService.startTreatment(
                        detailReq.getHeadNumber(),
                        detailReq.getDuration(),
                        detailReq.getIntensity(),
                        detailReq.getFrequency()
                    );

                    if (success) {
                        startedHeads.add(detailReq.getHeadNumber());
                        log.info("Successfully started treatment on head {} for body part {}",
                                detailReq.getHeadNumber(), detailReq.getBodyPart());
                    } else {
                        log.warn("Failed to start treatment on head {} for body part {}",
                                detailReq.getHeadNumber(), detailReq.getBodyPart());
                        detail.setStatus(TreatmentDetailStatus.TERMINATED);
                    }
                } catch (Exception e) {
                    log.error("Error starting treatment on head {}: {}", detailReq.getHeadNumber(), e.getMessage());
                    detail.setStatus(TreatmentDetailStatus.TERMINATED);
                }
            }

            // 4. 保存治疗详情
            treatmentDetailRepository.saveAll(details);

            // 5. 检查是否有任何治疗头启动成功
            if (startedHeads.isEmpty()) {
                // 如果没有任何治疗头启动成功，标记进程为失败
                process.setStatus(ProcessStatus.CANCELLED);
                process.setEndTime(LocalDateTime.now());
                processRepository.save(process);

                return ApiResponse.error(500, "所有治疗头启动失败，治疗进程已取消");
            }

            // 6. 构建响应
            var response = new StartTreatmentResponse();
            response.setProcessId(process.getId());
            response.setStatus("STARTED");
            response.setStartedHeads(startedHeads);
            response.setTotalHeads(request.getTreatmentDetails().size());
            response.setMessage(String.format("治疗进程已启动，成功启动 %d/%d 个治疗头",
                                            startedHeads.size(), request.getTreatmentDetails().size()));

            log.info("Treatment process {} started successfully with {} heads",
                    process.getId(), startedHeads.size());

            return ApiResponse.success("治疗进程启动成功", response);

        } catch (Exception e) {
            log.error("Error starting treatment process", e);
            return ApiResponse.error(500, "启动治疗进程失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗进程详情页面数据
     */
    @GetMapping("/{processId}")
    public ApiResponse<TreatmentProcessVO> getTreatmentProcess(@PathVariable Long processId) {
        var processOpt = processRepository.findById(processId);
        if (processOpt.isEmpty()) {
            return ApiResponse.notFound("治疗进程不存在");
        }
        
        var process = processOpt.get();
        var record = process.getRecord();
        var patient = record.getPatient();
        var details = treatmentDetailRepository.findByProcessId(processId);
        
        return ApiResponse.success(new TreatmentProcessVO(process, patient, details));
    }

    /**
     * 结束整个治疗进程
     */
    @PostMapping("/{processId}/terminate")
    public ApiResponse<Void> terminateProcess(@PathVariable Long processId) {
        var processOpt = processRepository.findById(processId);
        if (processOpt.isEmpty()) {
            return ApiResponse.notFound("治疗进程不存在");
        }
        
        var process = processOpt.get();
        if (process.getStatus() != ProcessStatus.IN_PROGRESS) {
            return ApiResponse.badRequest("只能终止进行中的治疗进程");
        }
        
        // 更新进程状态为取消
        process.setStatus(ProcessStatus.CANCELLED);
        process.setEndTime(LocalDateTime.now());
        
        // 更新所有治疗详情状态为终止
        var details = treatmentDetailRepository.findByProcessId(processId);
        for (var detail : details) {
            if (detail.getStatus() == TreatmentDetailStatus.TREATING) {
                detail.setStatus(TreatmentDetailStatus.TERMINATED);
            }
        }
        
        processRepository.save(process);
        treatmentDetailRepository.saveAll(details);
        
        log.info("治疗进程 {} 已被终止", processId);
        return ApiResponse.success();
    }

    /**
     * 终止指定部位的治疗
     */
    @PostMapping("/detail/{detailId}/terminate")
    public ApiResponse<Void> terminateDetail(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }
        
        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.TREATING) {
            return ApiResponse.badRequest("只能终止正在治疗中的项目");
        }
        
        // 更新治疗详情状态为终止
        detail.setStatus(TreatmentDetailStatus.TERMINATED);
        treatmentDetailRepository.save(detail);
        
        log.info("治疗详情 {} (部位: {}) 已被终止", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 完成指定部位的治疗
     */
    @PostMapping("/detail/{detailId}/complete")
    public ApiResponse<Void> completeDetail(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }

        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.TREATING) {
            return ApiResponse.badRequest("只能完成正在治疗中的项目");
        }

        // 更新治疗详情状态为完成
        detail.setStatus(TreatmentDetailStatus.COMPLETED);
        treatmentDetailRepository.save(detail);

        // 更新部位统计数据
        updateBodyPartStats(detail);

        // 检查是否所有治疗详情都已完成，如果是则完成整个进程
        checkAndCompleteProcess(detail.getProcess().getId());

        log.info("治疗详情 {} (部位: {}) 已完成", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 设置指定部位的治疗为待取回状态
     */
    @PostMapping("/detail/{detailId}/awaiting-return")
    public ApiResponse<Void> setAwaitingReturn(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }

        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.TREATING) {
            return ApiResponse.badRequest("只能设置正在治疗中的项目为待取回状态");
        }

        // 更新治疗详情状态为待取回
        detail.setStatus(TreatmentDetailStatus.AWAITING_RETURN);
        treatmentDetailRepository.save(detail);

        // 检查是否所有治疗详情都已达到待取回状态，如果是则更新整个进程状态
        checkAndUpdateProcessForAwaitingReturn(detail.getProcess().getId());

        log.info("治疗详情 {} (部位: {}) 已设置为待取回状态", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 确认治疗头已归还（硬件检测到归还后调用）
     */
    @PostMapping("/detail/{detailId}/returned")
    public ApiResponse<Void> confirmReturned(@PathVariable Long detailId) {
        var detailOpt = treatmentDetailRepository.findById(detailId);
        if (detailOpt.isEmpty()) {
            return ApiResponse.notFound("治疗详情不存在");
        }

        var detail = detailOpt.get();
        if (detail.getStatus() != TreatmentDetailStatus.COMPLETED &&
            detail.getStatus() != TreatmentDetailStatus.AWAITING_RETURN) {
            return ApiResponse.badRequest("只能确认已完成或待取回状态的治疗头归还");
        }

        // 更新治疗详情状态为已归还
        detail.setStatus(TreatmentDetailStatus.RETURNED);
        treatmentDetailRepository.save(detail);

        // 检查是否所有治疗详情都已归还，如果是则完成整个进程
        checkAndCompleteProcessForReturned(detail.getProcess().getId());

        log.info("治疗详情 {} (部位: {}) 已确认归还", detailId, detail.getBodyPart());
        return ApiResponse.success();
    }

    /**
     * 完成整个治疗进程
     */
    @PostMapping("/{processId}/complete")
    public ApiResponse<Void> completeProcess(@PathVariable Long processId) {
        var processOpt = processRepository.findById(processId);
        if (processOpt.isEmpty()) {
            return ApiResponse.notFound("治疗进程不存在");
        }
        
        var process = processOpt.get();
        if (process.getStatus() != ProcessStatus.IN_PROGRESS) {
            return ApiResponse.badRequest("只能完成进行中的治疗进程");
        }
        
        // 完成所有未完成的治疗详情
        var details = treatmentDetailRepository.findByProcessId(processId);
        for (var detail : details) {
            if (detail.getStatus() == TreatmentDetailStatus.TREATING) {
                detail.setStatus(TreatmentDetailStatus.COMPLETED);
                updateBodyPartStats(detail);
            }
        }
        
        // 更新进程状态为完成
        process.setStatus(ProcessStatus.COMPLETED);
        process.setEndTime(LocalDateTime.now());
        
        // 更新档案的完成次数
        var record = process.getRecord();
        record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);
        
        processRepository.save(process);
        treatmentDetailRepository.saveAll(details);
        recordRepository.save(record);
        
        log.info("治疗进程 {} 已完成，档案 {} 完成次数更新为 {}", 
                processId, record.getRecordNumber(), record.getSessionsCompletedCount());
        return ApiResponse.success();
    }

    /**
     * 更新部位统计数据
     */
    private void updateBodyPartStats(TreatmentDetail detail) {
        var process = detail.getProcess();
        var record = process.getRecord();
        var patient = record.getPatient();
        
        // 查找或创建部位统计记录
        var statOpt = bodyPartStatRepository.findByRecordIdAndBodyPart(record.getId(), detail.getBodyPart());
        BodyPartStat stat;
        
        if (statOpt.isPresent()) {
            stat = statOpt.get();
        } else {
            stat = new BodyPartStat();
            stat.setRecord(record);
            stat.setBodyPart(detail.getBodyPart());
            stat.setTotalUsageCount(0);
            stat.setTotalDurationMinutes(0);
        }
        
        // 更新统计数据
        stat.setTotalUsageCount(stat.getTotalUsageCount() + 1);
        stat.setTotalDurationMinutes(stat.getTotalDurationMinutes() + detail.getDuration());
        
        bodyPartStatRepository.save(stat);
        log.info("更新部位统计 - 患者: {}, 部位: {}, 使用次数: {}, 总时长: {}分钟", 
                patient.getName(), detail.getBodyPart(), stat.getTotalUsageCount(), stat.getTotalDurationMinutes());
    }

    /**
     * 检查并完成进程（只有当所有治疗详情都真正完成时才完成进程）
     */
    private void checkAndCompleteProcess(Long processId) {
        var details = treatmentDetailRepository.findByProcessId(processId);

        // 检查是否所有治疗详情都已真正完成（COMPLETED）或被终止（TERMINATED）
        boolean allCompleted = details.stream()
                .allMatch(d -> d.getStatus() == TreatmentDetailStatus.COMPLETED ||
                             d.getStatus() == TreatmentDetailStatus.TERMINATED);

        // 检查是否所有治疗详情都已到达待取回状态
        boolean allAwaitingReturn = details.stream()
                .allMatch(d -> d.getStatus() == TreatmentDetailStatus.AWAITING_RETURN);

        if (allCompleted) {
            // 所有治疗详情都已真正完成，完成进程并更新档案统计
            var processOpt = processRepository.findById(processId);
            if (processOpt.isPresent()) {
                var process = processOpt.get();
                if (process.getStatus() == ProcessStatus.IN_PROGRESS) {
                    process.setStatus(ProcessStatus.COMPLETED);
                    process.setEndTime(LocalDateTime.now());

                    // 更新档案完成次数
                    var record = process.getRecord();
                    record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);

                    processRepository.save(process);
                    recordRepository.save(record);

                    log.info("进程 {} 自动完成，所有治疗详情已真正完成", processId);
                }
            }
        } else if (allAwaitingReturn) {
            // 所有治疗详情都已到达待取回状态，根据治疗模式更新进程状态
            checkAndUpdateProcessForAwaitingReturn(processId);
        }
    }

    /**
     * 检查并更新进程状态（当所有治疗详情都已达到待取回状态时）
     */
    private void checkAndUpdateProcessForAwaitingReturn(Long processId) {
        var details = treatmentDetailRepository.findByProcessId(processId);
        boolean allAwaitingReturn = details.stream()
                .allMatch(detail -> detail.getStatus() == TreatmentDetailStatus.AWAITING_RETURN);

        if (allAwaitingReturn) {
            var processOpt = processRepository.findById(processId);
            if (processOpt.isPresent()) {
                var process = processOpt.get();
                if (process.getStatus() == ProcessStatus.IN_PROGRESS) {
                    // 根据治疗模式设置不同的进程状态
                    if (process.getTreatmentMode() == TreatmentMode.ON_SITE) {
                        // 现场治疗：设置为COMPLETED（显示"已完成"）
                        process.setStatus(ProcessStatus.COMPLETED);
                        log.info("现场治疗进程 {} 已完成，所有治疗详情已达到待取回状态", processId);
                    } else if (process.getTreatmentMode() == TreatmentMode.TAKE_AWAY) {
                        // 取走治疗：设置为CANCELLED（显示"待取回"）
                        process.setStatus(ProcessStatus.CANCELLED);
                        log.info("取走治疗进程 {} 已更新为待取回状态，所有治疗详情已达到设置时间", processId);
                    }

                    process.setEndTime(LocalDateTime.now());
                    processRepository.save(process);
                }
            }
        }
    }

    /**
     * 检查并完成进程（当所有治疗详情都已归还时）
     */
    private void checkAndCompleteProcessForReturned(Long processId) {
        var details = treatmentDetailRepository.findByProcessId(processId);
        boolean allReturned = details.stream()
                .allMatch(detail -> detail.getStatus() == TreatmentDetailStatus.RETURNED);

        if (allReturned) {
            var processOpt = processRepository.findById(processId);
            if (processOpt.isPresent()) {
                var process = processOpt.get();
                if (process.getStatus() != ProcessStatus.COMPLETED) {
                    // 所有治疗头都已归还，完成进程并更新档案统计
                    process.setStatus(ProcessStatus.COMPLETED);
                    process.setEndTime(LocalDateTime.now());

                    // 更新档案完成次数
                    var record = process.getRecord();
                    record.setSessionsCompletedCount(record.getSessionsCompletedCount() + 1);

                    processRepository.save(process);
                    recordRepository.save(record);

                    log.info("进程 {} 已完成，所有治疗头已归还", processId);
                }
            }
        }
    }

    /**
     * 治疗进程页面数据VO
     */
    @Data
    public static class TreatmentProcessVO {
        private Long processId;
        private String patientName;
        private String patientCardId;
        private String treatmentMode;
        private String status;
        private LocalDateTime startTime;
        private List<TreatmentDetailVO> details;
        
        public TreatmentProcessVO(com.Bone.BoneSys.entity.Process process, Patient patient, List<TreatmentDetail> details) {
            this.processId = process.getId();
            this.patientName = patient.getName();
            this.patientCardId = patient.getPatientCardId();
            this.treatmentMode = process.getTreatmentMode().name();
            this.status = process.getStatus().name();
            this.startTime = process.getStartTime();
            this.details = details.stream().map(TreatmentDetailVO::new).collect(Collectors.toList());
        }
    }

    /**
     * 治疗详情VO
     */
    @Data
    public static class TreatmentDetailVO {
        private Long detailId;
        private String bodyPart;
        private Integer headNumberUsed;
        private Integer duration;
        private BigDecimal intensity;
        private Integer frequency;
        private String patchType;
        private Integer patchQuantity;
        private String status;
        private String statusDescription;
        
        public TreatmentDetailVO(TreatmentDetail detail) {
            this.detailId = detail.getId();
            this.bodyPart = detail.getBodyPart();
            this.headNumberUsed = detail.getHeadNumberUsed();
            this.duration = detail.getDuration();
            this.intensity = detail.getIntensity();
            this.frequency = detail.getFrequency();
            this.patchType = detail.getPatchType().name();
            this.patchQuantity = detail.getPatchQuantity();
            this.status = detail.getStatus().name();
            this.statusDescription = getStatusDescription(detail.getStatus());
        }
        
        private String getStatusDescription(TreatmentDetailStatus status) {
            switch (status) {
                case TREATING: return "治疗中";
                case COMPLETED: return "已完成";
                case TERMINATED: return "已终止";
                case AWAITING_RETURN: return "等待归还";
                case RETURNED: return "已归还";
                default: return status.name();
            }
        }
    }

    /**
     * 开始治疗请求DTO
     */
    @Data
    public static class StartTreatmentRequest {
        private Long recordId;
        private TreatmentMode treatmentMode;
        private List<TreatmentDetailRequest> treatmentDetails;
    }

    /**
     * 治疗详情请求DTO
     */
    @Data
    public static class TreatmentDetailRequest {
        private String bodyPart;
        private Integer headNumber;
        private Integer duration;
        private Integer intensity;
        private Integer frequency;
        private PatchType patchType;
        private Integer patchQuantity;
    }

    /**
     * 开始治疗响应DTO
     */
    @Data
    public static class StartTreatmentResponse {
        private Long processId;
        private String status;
        private List<Integer> startedHeads;
        private Integer totalHeads;
        private String message;
    }
}


