package com.Bone.BoneSys.dto.hardware;

/**
 * 身体部位贴片需求
 */
public class BodyPartPatchRequest {
    
    private String bodyPart; // 身体部位名称
    private String patchType; // 贴片类型：SHALLOW（浅部）或 DEEP（深部）
    private int patchCount; // 该部位需要的贴片数量
    
    public BodyPartPatchRequest() {}
    
    public BodyPartPatchRequest(String bodyPart, String patchType, int patchCount) {
        this.bodyPart = bodyPart;
        this.patchType = patchType;
        this.patchCount = patchCount;
    }
    
    // Getters and Setters
    public String getBodyPart() {
        return bodyPart;
    }
    
    public void setBodyPart(String bodyPart) {
        this.bodyPart = bodyPart;
    }
    
    public String getPatchType() {
        return patchType;
    }
    
    public void setPatchType(String patchType) {
        this.patchType = patchType;
    }
    
    public int getPatchCount() {
        return patchCount;
    }
    
    public void setPatchCount(int patchCount) {
        this.patchCount = patchCount;
    }
    
    @Override
    public String toString() {
        return String.format("BodyPartPatchRequest{bodyPart='%s', patchType='%s', patchCount=%d}", 
                           bodyPart, patchType, patchCount);
    }
}