html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.block_1 {
  height: 28.8rem;
  background: url(./img/3d029cda48755aa691231fbb520c6188.png)
    0rem -0.027rem no-repeat;
  background-size: 51.174rem 28.827rem;
  margin-left: 0.027rem;
  width: 51.174rem;
}

.group_1 {
  width: 26.747rem;
  height: 1.867rem;
  margin: 0.507rem 0 0 2.747rem;
}

.image_1 {
  width: 4.507rem;
  height: 1.867rem;
}

.text_1 {
  width: 7.84rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.267rem;
}

.group_2 {
  width: 51.2rem;
  height: 24.134rem;
  margin: 2.267rem 0 0.027rem -0.027rem;
}

.group_3 {
  width: 12rem;
  height: 23.814rem;
  background: url(./img/15c8a1c74e2a4fbc4a43617cc6e49eae.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.32rem;
}

.image-wrapper_1 {
  width: 4.88rem;
  height: 16.774rem;
  background: url(./img/8f83464412e369b3a9420e2ac275dc75.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 2.107rem 0 0 1.307rem;
}

.label_1 {
  width: 1.12rem;
  height: 1.12rem;
  margin: 2.72rem 0 0 1.894rem;
}

.label_2 {
  width: 1.12rem;
  height: 1.12rem;
  margin: 1.307rem 0 0 1.894rem;
}

.label_3 {
  width: 1.12rem;
  height: 1.12rem;
  margin: 2.294rem 0 7.094rem 1.04rem;
}

.text_2 {
  width: 1.52rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 11.12rem 1.92rem 0 2.374rem;
}

.group_4 {
  width: 27.414rem;
  height: 22.774rem;
  margin-left: 0.134rem;
}

.group_5 {
  height: 20.16rem;
  background: url(./img/a3a10ec9dc5a8ce5937f0394d3819db4.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 27.414rem;
}

.text-wrapper_1 {
  width: 23.254rem;
  height: 0.667rem;
  margin: 1.787rem 0 0 1.867rem;
}

.text_3 {
  width: 1.36rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
}

.text_4 {
  width: 1.387rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 2rem;
}

.text_5 {
  width: 1.307rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 2.72rem;
}

.text_6 {
  width: 1.334rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 2.214rem;
}

.text_7 {
  width: 2.774rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 2.774rem;
}

.text_8 {
  width: 2.774rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-left: 2.614rem;
}

.block_2 {
  width: 22.4rem;
  height: 1.387rem;
  margin: 1.574rem 0 0 2.054rem;
}

.image_2 {
  width: 1.387rem;
  height: 1.387rem;
}

.text_9 {
  width: 2.16rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.347rem 0 0 1.387rem;
}

.text_10 {
  width: 1.974rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.454rem 0 0 1.947rem;
}

.text_11 {
  width: 2.72rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.507rem 0 0 1.28rem;
}

.text_12 {
  width: 2.427rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.507rem 0 0 2.347rem;
}

.text-wrapper_2 {
  width: 1.867rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.48rem 0 0 2.907rem;
}

.text_13 {
  width: 1.867rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.2rem;
}

.text_14 {
  width: 1.867rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: TimesNewRomanPSMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.2rem;
}

.block_3 {
  width: 23.974rem;
  height: 8.774rem;
  margin: 1.227rem 0 0 2.054rem;
}

.image-wrapper_2 {
  width: 1.414rem;
  height: 8.774rem;
}

.image_3 {
  width: 1.414rem;
  height: 1.414rem;
}

.image_4 {
  width: 1.387rem;
  height: 1.387rem;
  margin-top: 0.987rem;
}

.image_5 {
  width: 1.387rem;
  height: 1.387rem;
  margin-top: 1.12rem;
}

.image_6 {
  width: 1.387rem;
  height: 1.387rem;
  margin-top: 1.094rem;
}

.text-wrapper_3 {
  width: 2.16rem;
  height: 8.08rem;
  margin: 0.374rem 0 0 1.36rem;
}

.text_15 {
  width: 2.16rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
}

.text_16 {
  width: 1.414rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 1.76rem 0 0 0.374rem;
}

.text_17 {
  width: 1.414rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 1.814rem 0 0 0.374rem;
}

.text_18 {
  width: 1.414rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 1.84rem 0 0 0.374rem;
}

.text_19 {
  width: 1.974rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.374rem 0 0 1.947rem;
}

.section_1 {
  width: 4.16rem;
  height: 5.12rem;
  margin: 0.427rem 0 0 0.48rem;
}

.text_20 {
  width: 2.72rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin-left: 0.8rem;
}

.box_1 {
  width: 4.16rem;
  height: 4.214rem;
  background: url(./img/2e95215ed0f0444f301ee0b62616fd5b.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.267rem;
}

.text-wrapper_4 {
  height: 1.014rem;
  background: url(./img/ae050ead14bd1bf8b5d31ccccfada9a3.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 3.067rem;
  margin: 0.267rem 0 0 0.88rem;
}

.text_21 {
  width: 2.72rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.24rem 0 0 -0.08rem;
}

.text_22 {
  width: 2.774rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.4rem 0 0 0.747rem;
}

.text_23 {
  width: 2.747rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.507rem 0 0.774rem 0.774rem;
}

.section_2 {
  width: 4.24rem;
  height: 3.947rem;
  margin: 0.427rem 0 0 0.667rem;
}

.text_24 {
  width: 2.427rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin-left: 1.04rem;
}

.group_6 {
  width: 4.24rem;
  height: 3.067rem;
  background: url(./img/ba72f0579c9ab501b8a3f49b8cf492bb.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.32rem;
}

.image-text_1 {
  width: 3.067rem;
  height: 2rem;
  margin: 0.294rem 0 0 0.587rem;
}

.text-wrapper_5 {
  height: 1.014rem;
  background: url(./img/4f0df8afb90136f25779fb6cba455acc.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 3.067rem;
}

.text_25 {
  width: 2.027rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.24rem 0 0 0.667rem;
}

.text-group_1 {
  width: 2.427rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin: 0.427rem 0 0 0.454rem;
}

.section_3 {
  width: 5.12rem;
  height: 7.174rem;
  margin: 0.4rem 0 0 0.454rem;
}

.text-wrapper_6 {
  width: 1.867rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.2rem;
  margin-left: 1.68rem;
}

.text_26 {
  width: 1.867rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.2rem;
}

.text_27 {
  width: 1.867rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: TimesNewRomanPSMT;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 1.2rem;
}

.box_2 {
  height: 6.267rem;
  background: url(./img/4524106bc6ce83a638427fc2024cec09.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.267rem;
  width: 5.12rem;
}

.text-wrapper_7 {
  width: 3.494rem;
  height: 0.64rem;
  margin: 0.48rem 0 0 0.827rem;
}

.text_28 {
  width: 1.307rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
}

.text_29 {
  width: 1.307rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
}

.group_7 {
  width: 3.547rem;
  height: 0.614rem;
  margin: 0.614rem 0 0 0.64rem;
}

.image-text_2 {
  width: 1.52rem;
  height: 0.614rem;
}

.label_4 {
  width: 0.56rem;
  height: 0.56rem;
}

.text-group_2 {
  width: 0.64rem;
  height: 0.614rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
}

.label_5 {
  width: 0.56rem;
  height: 0.56rem;
  margin-left: 0.88rem;
}

.text_30 {
  width: 0.214rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
  margin: 0.027rem 0 0 0.374rem;
}

.group_8 {
  width: 1.227rem;
  height: 0.56rem;
  margin: 0.347rem 0 0 3.04rem;
}

.label_6 {
  width: 0.56rem;
  height: 0.56rem;
}

.text_31 {
  width: 0.32rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
  margin-top: 0.027rem;
}

.group_9 {
  width: 3.627rem;
  height: 0.827rem;
  margin: 0.16rem 0 0 0.64rem;
}

.image-text_3 {
  width: 1.52rem;
  height: 0.64rem;
}

.label_7 {
  width: 0.56rem;
  height: 0.56rem;
  margin-top: 0.054rem;
}

.text-group_3 {
  width: 0.614rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
}

.label_8 {
  width: 0.56rem;
  height: 0.56rem;
  margin: 0.267rem 0 0 0.88rem;
}

.text_32 {
  width: 0.32rem;
  height: 0.534rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.666rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
  margin: 0.294rem 0 0 0.347rem;
}

.group_10 {
  width: 1.254rem;
  height: 0.56rem;
  margin: 0.454rem 0 1.014rem 3.04rem;
}

.label_9 {
  width: 0.56rem;
  height: 0.56rem;
}

.text_33 {
  width: 0.374rem;
  height: 0.507rem;
  overflow-wrap: break-word;
  color: rgba(80, 80, 80, 1);
  font-size: 0.64rem;
  font-family: Adobe Heiti Std R;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.76rem;
  margin-top: 0.027rem;
}

.block_4 {
  width: 5.307rem;
  height: 1.387rem;
  margin: 1.307rem 0 2.054rem 2.054rem;
}

.image_7 {
  width: 1.387rem;
  height: 1.387rem;
}

.text_34 {
  width: 2.907rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.454rem;
}

.group_11 {
  width: 10.24rem;
  height: 2.614rem;
  margin-left: 8.374rem;
}

.text-wrapper_8 {
  height: 2.614rem;
  background: url(./img/67fb60a08c6e6c8a7895e55b353be4ee.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 7.414rem;
}

.text_35 {
  width: 5.334rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.854rem 0 0 0.987rem;
}

.image_8 {
  width: 2.827rem;
  height: 2.614rem;
}

.group_12 {
  width: 11.92rem;
  height: 23.814rem;
  background: url(./img/b77a209b7c30be85df4005a3907944c6.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 0.32rem 0 0 -0.267rem;
}

.text_36 {
  width: 1.52rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(50, 50, 50, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 11.094rem 0 0 2rem;
}

.image_9 {
  width: 4.88rem;
  height: 16.774rem;
  margin: 2.107rem 0.374rem 0 3.147rem;
}
