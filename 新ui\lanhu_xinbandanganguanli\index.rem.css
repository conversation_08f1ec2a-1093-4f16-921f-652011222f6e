html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.block_1 {
  height: 28.8rem;
  background: url(./img/51da9ff83e9a4afabde39995a337d4f7.png)
    0rem 0rem no-repeat;
  background-size: 51.2rem 28.8rem;
  margin-left: 0.054rem;
  width: 51.147rem;
}

.box_1 {
  width: 51.147rem;
  height: 28.8rem;
  background: url(./img/5ef5f27141c70c1e7e5a0f61a5b734f0.png)
    0rem 0rem no-repeat;
  background-size: 52.054rem 29.174rem;
}

.group_9 {
  width: 26.747rem;
  height: 1.867rem;
  margin: 0.534rem 0 0 2.747rem;
}

.image_1 {
  width: 4.507rem;
  height: 1.867rem;
}

.text_1 {
  width: 7.84rem;
  height: 1.307rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.267rem;
}

.group_10 {
  width: 42.267rem;
  height: 2.427rem;
  margin: 1.334rem 0 0 3.787rem;
}

.group_1 {
  box-shadow: 10px 16px 19px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  width: 12.64rem;
  height: 1.76rem;
  border: 0.5px solid rgba(124, 121, 121, 0.6);
  margin-top: 0.347rem;
}

.text_2 {
  width: 4.08rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 0.96rem;
}

.image_2 {
  width: 0.027rem;
  height: 1.147rem;
  margin: 0.347rem 0 0 0.8rem;
}

.text_3 {
  width: 2.534rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 2rem 0 2.24rem;
}

.group_2 {
  box-shadow: 5px 9px 24px 4px rgba(29, 35, 36, 0.08);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 6px;
  width: 9.227rem;
  height: 1.76rem;
  border: 0.5px solid rgba(166, 164, 162, 1);
  margin: 0.347rem 0 0 1.547rem;
}

.text_4 {
  width: 1.867rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 0.987rem;
}

.image_3 {
  width: 0.027rem;
  height: 1.147rem;
  margin: 0.347rem 0 0 0.72rem;
}

.text_5 {
  width: 2.534rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(202, 201, 202, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 1.574rem 0 1.52rem;
}

.text-wrapper_1 {
  height: 2.427rem;
  background: url(./img/e6ee0182dc4d4797b0875b0a7f9e836c.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 12.8rem;
  width: 6.054rem;
}

.text_6 {
  width: 2.134rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 0 0 1.894rem;
}

.section_3 {
  width: 45.04rem;
  height: 20.88rem;
  background: url(./img/f7ef7ec148e0f2b11bc0f46f49c5c8a0.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 0.667rem 0 1.094rem 2.907rem;
}

.text-wrapper_10 {
  width: 36.48rem;
  height: 0.907rem;
  margin: 1.254rem 0 0 4.48rem;
}

.text_7 {
  width: 4.16rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.027rem;
}

.text_8 {
  width: 1.894rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.027rem 0 0 2.987rem;
}

.text_9 {
  width: 1.974rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-left: 2.507rem;
}

.text_10 {
  width: 1.947rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.027rem 0 0 1.627rem;
}

.text_11 {
  width: 4.187rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.027rem 0 0 2.64rem;
}

.text_12 {
  width: 4.134rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.027rem 0 0 2rem;
}

.text_13 {
  width: 2rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.027rem 0 0 4.427rem;
}

.image_4 {
  width: 39.36rem;
  height: 0.027rem;
  margin: 0.454rem 0 0 3.094rem;
}

.box_4 {
  width: 37.254rem;
  height: 1.254rem;
  margin: 0.294rem 0 0 5.014rem;
}

.text_14 {
  width: 3.04rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.4rem;
}

.text_15 {
  width: 3.067rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.374rem 0 0 3.014rem;
}

.text_16 {
  width: 0.907rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.4rem 0 0 2.4rem;
}

.text_17 {
  width: 0.64rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.4rem 0 0 2.934rem;
}

.text_18 {
  width: 1.2rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.374rem 0 0 4.694rem;
}

.text_19 {
  width: 4.24rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.507rem 0 0 2.934rem;
}

.text-wrapper_3 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 3.067rem;
  width: 2.374rem;
}

.text_20 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 0.374rem;
}

.text-wrapper_4 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.374rem;
  width: 2.374rem;
}

.text_21 {
  width: 1.627rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 0.374rem;
}

.image_5 {
  width: 39.36rem;
  height: 0.027rem;
  margin: 0.32rem 0 0 3.094rem;
}

.box_5 {
  width: 37.254rem;
  height: 1.254rem;
  margin: 0.294rem 0 0 5.014rem;
}

.text_22 {
  width: 3.04rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.48rem;
}

.text_23 {
  width: 3.067rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.454rem 0 0 3.014rem;
}

.text_24 {
  width: 0.907rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.48rem 0 0 2.4rem;
}

.text_25 {
  width: 0.64rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.48rem 0 0 2.934rem;
}

.text_26 {
  width: 1.227rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 4.667rem;
}

.text_27 {
  width: 4.24rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.454rem 0 0 2.934rem;
}

.text-wrapper_5 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 3.067rem;
  width: 2.374rem;
}

.text_28 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 0.374rem;
}

.text-wrapper_6 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.374rem;
  width: 2.374rem;
}

.text_29 {
  width: 1.627rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 0.374rem;
}

.image-wrapper_1 {
  height: 0.027rem;
  background: url(./img/86bf3c543f63e690373da9f224fe1a88.png)
    0rem -0.027rem no-repeat;
  background-size: 39.36rem 0.054rem;
  width: 39.36rem;
  margin: 0.32rem 0 0 3.094rem;
}

.image_6 {
  width: 39.36rem;
  height: 0.027rem;
}

.box_6 {
  width: 37.254rem;
  height: 1.254rem;
  margin: 0.294rem 0 0 5.014rem;
}

.text_30 {
  width: 3.04rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.454rem;
}

.text_31 {
  width: 3.067rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 3.014rem;
}

.text_32 {
  width: 0.907rem;
  height: 0.56rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.454rem 0 0 2.4rem;
}

.text_33 {
  width: 0.64rem;
  height: 0.64rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.454rem 0 0 2.934rem;
}

.text_34 {
  width: 1.2rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(154, 154, 154, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.427rem 0 0 4.694rem;
}

.text-wrapper_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(71, 161, 151, 1);
  margin-left: 10.24rem;
  width: 2.374rem;
}

.text_35 {
  width: 1.6rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 0.374rem;
}

.text-wrapper_8 {
  background-color: rgba(231, 231, 231, 1);
  border-radius: 10px;
  height: 1.254rem;
  border: 1px solid rgba(202, 201, 202, 1);
  margin-left: 0.374rem;
  width: 2.374rem;
}

.text_36 {
  width: 1.627rem;
  height: 0.667rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.267rem 0 0 0.374rem;
}

.image-wrapper_2 {
  height: 0.027rem;
  background: url(./img/69de334a917d2b69b72c6daeca0f5a57.png)
    0rem -0.027rem no-repeat;
  background-size: 39.36rem 0.054rem;
  width: 39.36rem;
  margin: 0.32rem 0 0 3.094rem;
}

.image_7 {
  width: 39.36rem;
  height: 0.027rem;
}

.image-wrapper_3 {
  box-shadow: 6px 10px 9px 0px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 0.63);
  border-radius: 16px;
  height: 1.52rem;
  border: 1px solid rgba(238, 238, 238, 0.63);
  width: 6.934rem;
  margin: 8.187rem 0 2.854rem 36.427rem;
}

.image_8 {
  width: 6.267rem;
  height: 1.014rem;
  margin: 0.24rem 0 0 0.267rem;
}
