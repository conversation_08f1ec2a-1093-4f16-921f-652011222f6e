.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1080px;
  background: url(./img/66c5f27211164f992364facf79ecf52d.png) -4px
    0px no-repeat;
  background-size: 1924px 1080px;
  width: 1920px;
}

.group_1 {
  height: 1080px;
  background: url(./img/732eb040d6ffb4c5cf58adcb09bc3cc4.png) -57px -8055px
    no-repeat;
  background-size: 1977px 9135px;
  width: 1920px;
}

.group_2 {
  height: 752px;
  background: url(./img/dc02f849428605e71b359a0193094b4e.png) -9px
    0px no-repeat;
  background-size: 631px 774px;
  width: 612px;
  margin: 147px 0 0 658px;
}

.image-wrapper_2 {
  width: 60px;
  height: 60px;
  margin: 31px 0 0 510px;
}

.image_2 {
  width: 60px;
  height: 60px;
}

.box_4 {
  width: 417px;
  height: 88px;
  margin: 67px 0 0 93px;
}

.image_1 {
  width: 88px;
  height: 88px;
}

.text_1 {
  width: 285px;
  height: 57px;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 58px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 356px;
  margin-top: 15px;
}

.box_5 {
  width: 314px;
  height: 75px;
  margin: 103px 0 328px 156px;
}

.text-wrapper_1 {
  width: 314px;
  height: 75px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 200px;
}

.paragraph_1 {
  width: 314px;
  height: 75px;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 200px;
}

.text_2 {
  width: 314px;
  height: 75px;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 42px;
}
