.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.section_1 {
  width: 9.43vw;
  height: 1.31vw;
  margin: -489.47vw 0 0 -106.66vw;
}

.block_1 {
  height: 56.25vw;
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
}

.image-wrapper_5 {
  width: 9.43vw;
  height: 1.31vw;
  margin: -489.47vw 0 0 -106.66vw;
}

.section_2 {
  width: 9.43vw;
  height: 1.31vw;
}

.image-wrapper_6 {
  width: 3.13vw;
  height: 3.13vw;
  margin: 497.39vw 0 0 60.83vw;
}

.image_3 {
  width: 3.13vw;
  height: 3.13vw;
}

.group_1 {
  width: 21.72vw;
  height: 4.59vw;
  margin: 3.54vw 0 0 39.11vw;
}

.image_1 {
  width: 4.59vw;
  height: 4.59vw;
}

.text_1 {
  width: 14.85vw;
  height: 2.97vw;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 3.02vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 18.55vw;
  margin-top: 0.79vw;
}

.group_2 {
  width: 9.22vw;
  height: 3.91vw;
  margin: 5.62vw 0 0 45.72vw;
}

.text-wrapper_1 {
  width: 9.22vw;
  height: 3.91vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 10.42vw;
}

.paragraph_1 {
  width: 9.22vw;
  height: 3.91vw;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 10.42vw;
}

.text_2 {
  width: 9.22vw;
  height: 3.91vw;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 2.19vw;
}

.image-wrapper_7 {
  width: 6.25vw;
  height: 6.25vw;
  margin: 5.46vw 0 14.53vw 47.65vw;
}

.image_2 {
  width: 6.25vw;
  height: 6.25vw;
}
