# 治疗头自动同步系统 - 需求文档

## 介绍

本规格定义了治疗头自动同步系统的功能需求。该系统负责在应用启动时初始化治疗头数据，并定期与硬件控制板同步治疗头状态，确保数据库中的治疗头信息与实际硬件状态保持一致。

## 需求

### 需求1：应用启动时初始化治疗头数据

**用户故事**: 作为系统管理员，我希望应用启动时能自动查询并初始化所有治疗头数据，以便系统能够立即开始正常工作。

#### 验收标准

1. WHEN 应用启动时 THEN 系统应自动向硬件控制板发送 `TRZI\r\n` 指令
2. WHEN 收到硬件响应 `TRZI+治疗头数量(2位)+治疗头数据\r\n` THEN 系统应正确解析所有治疗头信息
3. WHEN 解析完成后 THEN 系统应将治疗头数据保存或更新到数据库的 treatment_heads 表中
4. IF 数据库中不存在对应治疗头记录 THEN 系统应创建新记录
5. IF 数据库中已存在对应治疗头记录 THEN 系统应更新现有记录
6. WHEN 初始化失败时 THEN 系统应记录错误日志但不影响应用正常启动

### 需求2：定时同步治疗头状态

**用户故事**: 作为前端开发者，我希望数据库中的治疗头数据能够定期更新，以便前端界面能够显示最新的治疗头状态信息。

#### 验收标准

1. WHEN 系统运行时 THEN 应每隔10秒自动执行一次治疗头状态同步
2. WHEN 执行同步时 THEN 系统应向硬件控制板发送 `TRZI\r\n` 指令
3. WHEN 收到硬件响应时 THEN 系统应解析并更新数据库中的治疗头信息
4. WHEN 同步成功时 THEN 系统应记录调试级别日志
5. WHEN 同步失败时 THEN 系统应记录警告级别日志但继续下次同步
6. WHEN 应用关闭时 THEN 定时同步任务应优雅停止

### 需求3：治疗头数据格式处理

**用户故事**: 作为系统开发者，我希望系统能够正确处理硬件返回的治疗头数据格式，确保数据解析的准确性。

#### 验收标准

1. WHEN 解析TRZI响应时 THEN 系统应正确提取治疗头数量（2位字符）
2. WHEN 处理治疗头数据时 THEN 每个治疗头数据应包含：治疗头编号(2位) + 电量(2位) + 使用次数(3位) + 槽号(2位)
3. WHEN 数据长度不匹配时 THEN 系统应抛出详细的错误信息
4. WHEN 数字解析失败时 THEN 系统应记录具体的解析错误位置
5. WHEN 响应格式无效时 THEN 系统应记录完整的响应内容用于调试

### 需求4：数据库状态映射

**用户故事**: 作为业务逻辑开发者，我希望硬件状态能够正确映射到数据库字段，以便其他模块能够使用准确的治疗头状态信息。

#### 验收标准

1. WHEN 治疗头电量 > 60% 且 < 100% THEN 数据库状态应设置为 CHARGING（充电中）
2. WHEN 治疗头电量 = 100% THEN 数据库状态应设置为 CHARGED（充电完成）
3. WHEN 治疗头正在使用时 THEN 数据库状态应设置为 TREATING（治疗中）
4. WHEN 更新治疗头记录时 THEN 应同时更新电量、使用次数、槽号和状态字段
5. WHEN 创建新治疗头记录时 THEN 应设置合理的默认值（最大使用次数1000、指示灯关闭、使用时间0）

### 需求5：同步任务配置管理

**用户故事**: 作为系统管理员，我希望能够配置同步任务的执行间隔和行为，以便根据实际需要调整系统性能。

#### 验收标准

1. WHEN 配置同步间隔时 THEN 系统应支持通过配置文件设置同步间隔（默认10秒）
2. WHEN 配置启用/禁用同步时 THEN 系统应支持通过配置开关控制定时同步功能
3. WHEN 硬件模拟器模式时 THEN 同步功能应正常工作并使用模拟数据
4. WHEN 真实硬件模式时 THEN 同步功能应通过串口与实际硬件通信
5. WHEN 硬件连接失败时 THEN 系统应继续尝试同步但不影响其他功能

### 需求6：错误处理和恢复

**用户故事**: 作为系统运维人员，我希望同步系统具有良好的错误处理能力，确保单次同步失败不会影响整体系统稳定性。

#### 验收标准

1. WHEN 串口通信超时时 THEN 系统应记录超时错误并在下次同步时重试
2. WHEN 硬件响应格式错误时 THEN 系统应记录详细错误信息并跳过本次同步
3. WHEN 数据库操作失败时 THEN 系统应记录数据库错误并在下次同步时重试
4. WHEN 连续同步失败时 THEN 系统应记录警告但不停止定时任务
5. WHEN 系统资源不足时 THEN 同步任务应优雅降级或延迟执行

### 需求7：性能和监控

**用户故事**: 作为系统监控人员，我希望能够监控同步任务的执行情况和性能指标，以便及时发现和解决问题。

#### 验收标准

1. WHEN 执行同步时 THEN 系统应记录同步开始和完成时间
2. WHEN 同步完成时 THEN 系统应记录处理的治疗头数量和更新的记录数
3. WHEN 同步性能异常时 THEN 系统应记录执行时间超过阈值的警告
4. WHEN 查询同步状态时 THEN 系统应提供API接口返回最后同步时间和状态
5. WHEN 需要手动触发同步时 THEN 系统应提供API接口支持立即执行同步