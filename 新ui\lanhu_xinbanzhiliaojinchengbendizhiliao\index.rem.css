html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.box_1 {
  height: 28.8rem;
  background: url(./img/581596b6024a8797eb084676c601cc04.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 0.027rem;
  width: 51.174rem;
}

.group_1 {
  width: 26.747rem;
  height: 1.867rem;
  margin: 0.534rem 0 0 2.747rem;
}

.image_1 {
  width: 4.507rem;
  height: 1.867rem;
}

.text_1 {
  width: 7.84rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.267rem;
}

.group_2 {
  position: relative;
  width: 45.2rem;
  height: 2.454rem;
  margin: 2.054rem 0 0 2.987rem;
}

.image_2 {
  width: 1.494rem;
  height: 1.494rem;
  margin-top: 0.427rem;
}

.text_2 {
  width: 2.214rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.8rem 0 0 0.64rem;
}

.block_1 {
  width: 9.2rem;
  height: 2.427rem;
  background: url(./img/da69ab339dafb93891d48a37c09b9a2f.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 14.614rem;
}

.label_1 {
  width: 0.854rem;
  height: 0.774rem;
  margin: 0.64rem 0 0 0.667rem;
}

.text_3 {
  width: 5.734rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.614rem 1.307rem 0 0.64rem;
}

.block_2 {
  width: 9.2rem;
  height: 2.427rem;
  background: url(./img/f328c43c1f4187c31ec25f2a47113077.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-left: 0.907rem;
}

.label_2 {
  width: 0.854rem;
  height: 0.774rem;
  margin: 0.64rem 0 0 0.667rem;
}

.text_4 {
  width: 5.734rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.614rem 1.307rem 0 0.64rem;
}

.text_5 {
  width: 1.867rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.614rem 1.52rem 0 3.547rem;
}

.block_3 {
  position: absolute;
  left: 39.147rem;
  top: 0.027rem;
  width: 6.054rem;
  height: 2.427rem;
  background: url(./img/5e50169bee19e094657b18f2bc6e3b7a.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.label_3 {
  width: 0.987rem;
  height: 0.987rem;
  margin: 0.534rem 0 0 0.88rem;
}

.text_6 {
  width: 1.867rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.587rem 1.654rem 0 0.667rem;
}

.group_3 {
  width: 45.307rem;
  height: 7.44rem;
  margin: 3.787rem 0 10.667rem 2.8rem;
}

.group_4 {
  box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
  background-color: rgba(255, 255, 255, 1);
  width: 13.227rem;
  height: 7.414rem;
  border: 0.5px solid rgba(254, 152, 8, 1);
  margin-top: 0.027rem;
}

.group_5 {
  width: 13.227rem;
  height: 2.827rem;
  background: url(./img/8b04f3885006c864b55e5ac79f51715d.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image-text_1 {
  width: 3.414rem;
  height: 0.907rem;
  margin: 0.8rem 0 0 1.067rem;
}

.label_4 {
  width: 0.907rem;
  height: 0.907rem;
}

.text-group_1 {
  width: 2.134rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.107rem;
}

.image_3 {
  width: 1.36rem;
  height: 1.36rem;
  margin: 0.507rem 1.414rem 0 5.974rem;
}

.text-wrapper_1 {
  width: 10.187rem;
  height: 0.88rem;
  margin: 1.227rem 0 0 1.494rem;
}

.text_7 {
  width: 3.76rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_8 {
  width: 4.24rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.image_4 {
  width: 10.88rem;
  height: 0.08rem;
  margin: 0.294rem 0 0 1.28rem;
}

.text-wrapper_2 {
  width: 10.96rem;
  height: 0.88rem;
  margin: 0.347rem 0 0.88rem 1.52rem;
}

.text_9 {
  width: 3.787rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_10 {
  width: 5.867rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.054rem;
}

.group_6 {
  box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
  background-color: rgba(255, 255, 255, 1);
  width: 13.227rem;
  height: 7.414rem;
  border: 0.5px solid rgba(254, 152, 8, 1);
  margin-left: 2.8rem;
}

.section_1 {
  width: 13.227rem;
  height: 2.827rem;
  background: url(./img/61fd8b7a2d7928b12bfc10eef686680c.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image-text_2 {
  width: 3.414rem;
  height: 0.907rem;
  margin: 0.8rem 0 0 1.067rem;
}

.label_5 {
  width: 0.907rem;
  height: 0.907rem;
}

.text-group_2 {
  width: 2.107rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.107rem;
}

.image_5 {
  width: 1.36rem;
  height: 1.36rem;
  margin: 0.507rem 1.414rem 0 5.974rem;
}

.text-wrapper_3 {
  width: 10.16rem;
  height: 0.907rem;
  margin: 1.2rem 0 0 1.52rem;
}

.text_11 {
  width: 3.76rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.027rem;
}

.text_12 {
  width: 4.24rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.image_6 {
  width: 10.88rem;
  height: 0.08rem;
  margin: 0.294rem 0 0 1.307rem;
}

.text-wrapper_4 {
  width: 10.987rem;
  height: 0.907rem;
  margin: 0.32rem 0 0.88rem 1.52rem;
}

.text_13 {
  width: 3.787rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_14 {
  width: 5.867rem;
  height: 0.8rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.08rem;
}

.group_7 {
  box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
  background-color: rgba(255, 255, 255, 1);
  width: 13.227rem;
  height: 7.414rem;
  border: 0.5px solid rgba(254, 152, 8, 1);
  margin: 0.027rem 0 0 2.827rem;
}

.box_2 {
  width: 13.227rem;
  height: 2.827rem;
  background: url(./img/b088b7478763ec69ff3418f572620403.png)
    100% no-repeat;
  background-size: 100% 100%;
}

.image-text_3 {
  width: 3.414rem;
  height: 0.907rem;
  margin: 0.8rem 0 0 1.067rem;
}

.label_6 {
  width: 0.907rem;
  height: 0.907rem;
}

.text-group_3 {
  width: 2.134rem;
  height: 0.694rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.666rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.107rem;
}

.image_7 {
  width: 1.36rem;
  height: 1.36rem;
  margin: 0.507rem 1.414rem 0 5.974rem;
}

.text-wrapper_5 {
  width: 10.187rem;
  height: 0.88rem;
  margin: 1.227rem 0 0 1.494rem;
}

.text_15 {
  width: 3.76rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_16 {
  width: 4.24rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.image_8 {
  width: 10.88rem;
  height: 0.08rem;
  margin: 0.294rem 0 0 1.28rem;
}

.text-wrapper_6 {
  width: 10.96rem;
  height: 0.88rem;
  margin: 0.347rem 0 0.88rem 1.52rem;
}

.text_17 {
  width: 3.787rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}

.text_18 {
  width: 5.867rem;
  height: 0.827rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.054rem;
}
