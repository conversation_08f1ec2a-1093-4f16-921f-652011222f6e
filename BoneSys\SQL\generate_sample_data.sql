-- ====================================================================================
-- FREEBONE医疗系统测试数据生成脚本
-- 功能说明：
-- 1. 生成大量患者数据（50个患者）
-- 2. 生成档案记录（30个档案）
-- 3. 生成治疗进程数据（100个进程）
-- 4. 生成治疗详情数据（300个详情记录）
-- 5. 生成部位统计数据
-- 6. 确保数据的完整性和一致性
-- ====================================================================================

USE `bonesys`;

-- 清空现有测试数据（保留系统配置数据）
DELETE FROM `body_part_stats`;
DELETE FROM `treatment_details`;
DELETE FROM `processes`;
DELETE FROM `records`;
DELETE FROM `patients`;

-- 重置自增ID
ALTER TABLE `patients` AUTO_INCREMENT = 1;
ALTER TABLE `records` AUTO_INCREMENT = 1;
ALTER TABLE `processes` AUTO_INCREMENT = 1;
ALTER TABLE `treatment_details` AUTO_INCREMENT = 1;
ALTER TABLE `body_part_stats` AUTO_INCREMENT = 1;

-- -----------------------------------------------------
-- 生成50个患者数据
-- -----------------------------------------------------
INSERT INTO `patients` (`patient_card_id`, `name`, `gender`, `age`, `contact_info`, `created_at`) VALUES
-- 第一批患者（1-25）
('P20250101001', '李明', '男', '35', '13800138001', '2025-01-01 08:00:00'),
('P20250101002', '王芳', '女', '28', '13800138002', '2025-01-01 08:30:00'),
('P20250101003', '张伟', '男', '42', '13800138003', '2025-01-01 09:00:00'),
('P20250101004', '刘丽', '女', '31', '13800138004', '2025-01-01 09:30:00'),
('P20250101005', '陈强', '男', '39', '13800138005', '2025-01-01 10:00:00'),
('P20250101006', '赵敏', '女', '26', '13800138006', '2025-01-01 10:30:00'),
('P20250101007', '孙杰', '男', '45', '13800138007', '2025-01-01 11:00:00'),
('P20250101008', '周雅', '女', '33', '13800138008', '2025-01-01 11:30:00'),
('P20250101009', '吴涛', '男', '37', '13800138009', '2025-01-01 14:00:00'),
('P20250101010', '郑美', '女', '29', '13800138010', '2025-01-01 14:30:00'),
('P20250101011', '王磊', '男', '41', '13800138011', '2025-01-01 15:00:00'),
('P20250101012', '李娜', '女', '34', '13800138012', '2025-01-01 15:30:00'),
('P20250101013', '张华', '男', '38', '13800138013', '2025-01-01 16:00:00'),
('P20250101014', '刘静', '女', '32', '13800138014', '2025-01-01 16:30:00'),
('P20250101015', '陈军', '男', '43', '13800138015', '2025-01-02 08:00:00'),
('P20250101016', '赵琳', '女', '27', '13800138016', '2025-01-02 08:30:00'),
('P20250101017', '孙鹏', '男', '36', '13800138017', '2025-01-02 09:00:00'),
('P20250101018', '周洁', '女', '30', '13800138018', '2025-01-02 09:30:00'),
('P20250101019', '吴刚', '男', '44', '13800138019', '2025-01-02 10:00:00'),
('P20250101020', '郑霞', '女', '25', '13800138020', '2025-01-02 10:30:00'),
('P20250101021', '王斌', '男', '40', '13800138021', '2025-01-02 11:00:00'),
('P20250101022', '李红', '女', '35', '13800138022', '2025-01-02 11:30:00'),
('P20250101023', '张勇', '男', '46', '13800138023', '2025-01-02 14:00:00'),
('P20250101024', '刘萍', '女', '28', '13800138024', '2025-01-02 14:30:00'),
('P20250101025', '陈亮', '男', '33', '13800138025', '2025-01-02 15:00:00'),

-- 第二批患者（26-50）
('P20250102001', '赵刚', '男', '37', '13800138026', '2025-01-02 15:30:00'),
('P20250102002', '孙丽', '女', '31', '13800138027', '2025-01-02 16:00:00'),
('P20250102003', '周强', '男', '42', '13800138028', '2025-01-02 16:30:00'),
('P20250102004', '吴敏', '女', '29', '13800138029', '2025-01-03 08:00:00'),
('P20250102005', '郑伟', '男', '38', '13800138030', '2025-01-03 08:30:00'),
('P20250102006', '王玲', '女', '34', '13800138031', '2025-01-03 09:00:00'),
('P20250102007', '李峰', '男', '41', '13800138032', '2025-01-03 09:30:00'),
('P20250102008', '张艳', '女', '26', '13800138033', '2025-01-03 10:00:00'),
('P20250102009', '刘涛', '男', '39', '13800138034', '2025-01-03 10:30:00'),
('P20250102010', '陈慧', '女', '32', '13800138035', '2025-01-03 11:00:00'),
('P20250102011', '赵军', '男', '45', '13800138036', '2025-01-03 11:30:00'),
('P20250102012', '孙燕', '女', '27', '13800138037', '2025-01-03 14:00:00'),
('P20250102013', '周磊', '男', '36', '13800138038', '2025-01-03 14:30:00'),
('P20250102014', '吴娟', '女', '30', '13800138039', '2025-01-03 15:00:00'),
('P20250102015', '郑华', '男', '43', '13800138040', '2025-01-03 15:30:00'),
('P20250102016', '王梅', '女', '28', '13800138041', '2025-01-03 16:00:00'),
('P20250102017', '李超', '男', '40', '13800138042', '2025-01-03 16:30:00'),
('P20250102018', '张丽', '女', '33', '13800138043', '2025-01-04 08:00:00'),
('P20250102019', '刘鹏', '男', '37', '13800138044', '2025-01-04 08:30:00'),
('P20250102020', '陈洁', '女', '31', '13800138045', '2025-01-04 09:00:00'),
('P20250102021', '赵辉', '男', '44', '13800138046', '2025-01-04 09:30:00'),
('P20250102022', '孙芳', '女', '29', '13800138047', '2025-01-04 10:00:00'),
('P20250102023', '周杰', '男', '35', '13800138048', '2025-01-04 10:30:00'),
('P20250102024', '吴雪', '女', '26', '13800138049', '2025-01-04 11:00:00'),
('P20250102025', '郑龙', '男', '41', '13800138050', '2025-01-04 11:30:00');

-- -----------------------------------------------------
-- 生成30个档案记录
-- -----------------------------------------------------
INSERT INTO `records` (`record_number`, `patient_id`, `diagnosis_description`, `sessions_completed_count`, `created_at`) VALUES
('REC20250101001', 1, '颈椎病，C5-C6椎间盘突出，建议物理治疗', 5, '2025-01-01'),
('REC20250101002', 2, '腰椎间盘突出症，L4-L5节段，疼痛明显', 8, '2025-01-01'),
('REC20250101003', 3, '肩周炎，右肩关节活动受限', 3, '2025-01-02'),
('REC20250101004', 4, '腰肌劳损，慢性疼痛综合征', 12, '2025-01-02'),
('REC20250101005', 5, '颈肩综合征，长期伏案工作导致', 6, '2025-01-03'),
('REC20250101006', 6, '膝关节炎，双膝疼痛，建议保守治疗', 4, '2025-01-03'),
('REC20250101007', 7, '腰椎管狭窄症，间歇性跛行', 10, '2025-01-04'),
('REC20250101008', 8, '颈椎生理曲度变直，头痛头晕', 7, '2025-01-04'),
('REC20250101009', 9, '肩袖损伤，左肩疼痛，夜间加重', 9, '2025-01-05'),
('REC20250101010', 10, '腰椎滑脱，L5椎体前滑', 15, '2025-01-05'),
('REC20250102001', 11, '网球肘，右肘外侧疼痛', 2, '2025-01-06'),
('REC20250102002', 12, '髋关节炎，双髋疼痛，行走困难', 11, '2025-01-06'),
('REC20250102003', 13, '胸椎小关节紊乱，背部疼痛', 5, '2025-01-07'),
('REC20250102004', 14, '腕管综合征，双手麻木', 8, '2025-01-07'),
('REC20250102005', 15, '踝关节扭伤后遗症，左踝疼痛', 3, '2025-01-08'),
('REC20250102006', 16, '颈椎病伴眩晕，头晕恶心', 6, '2025-01-08'),
('REC20250102007', 17, '腰椎退行性变，慢性腰痛', 13, '2025-01-09'),
('REC20250102008', 18, '肩胛骨周围肌筋膜炎', 4, '2025-01-09'),
('REC20250102009', 19, '髌骨软化症，上下楼梯疼痛', 7, '2025-01-10'),
('REC20250102010', 20, '颈椎间盘突出，C6-C7节段', 9, '2025-01-10'),
('REC20250103001', 21, '腰椎压缩性骨折术后康复', 16, '2025-01-11'),
('REC20250103002', 22, '肩关节撞击综合征', 5, '2025-01-11'),
('REC20250103003', 23, '腰椎侧弯，轻度S型弯曲', 8, '2025-01-12'),
('REC20250103004', 24, '颈椎反弓，颈部僵硬', 3, '2025-01-12'),
('REC20250103005', 25, '膝关节半月板损伤', 11, '2025-01-13'),
('REC20250103006', 26, '腰椎峡部裂，腰痛伴下肢放射痛', 14, '2025-01-13'),
('REC20250103007', 27, '颈椎病伴神经根型症状', 6, '2025-01-14'),
('REC20250103008', 28, '腰肌筋膜炎，慢性疼痛', 9, '2025-01-14'),
('REC20250103009', 29, '肩关节粘连性关节囊炎', 7, '2025-01-15'),
('REC20250103010', 30, '腰椎间盘膨出，L3-L4节段', 12, '2025-01-15');

-- -----------------------------------------------------
-- 生成100个治疗进程数据
-- -----------------------------------------------------
INSERT INTO `processes` (`record_id`, `treatment_mode`, `status`, `start_time`, `end_time`) VALUES
-- 档案1的治疗进程（5次）
(1, 'ON_SITE', 'COMPLETED', '2025-01-01 09:00:00', '2025-01-01 09:25:00'),
(1, 'TAKE_AWAY', 'COMPLETED', '2025-01-03 14:30:00', '2025-01-03 15:00:00'),
(1, 'ON_SITE', 'COMPLETED', '2025-01-05 10:15:00', '2025-01-05 10:40:00'),
(1, 'TAKE_AWAY', 'COMPLETED', '2025-01-08 16:20:00', '2025-01-08 16:50:00'),
(1, 'ON_SITE', 'IN_PROGRESS', '2025-01-10 11:00:00', NULL),

-- 档案2的治疗进程（8次）
(2, 'ON_SITE', 'COMPLETED', '2025-01-01 10:30:00', '2025-01-01 11:00:00'),
(2, 'ON_SITE', 'COMPLETED', '2025-01-02 09:45:00', '2025-01-02 10:15:00'),
(2, 'TAKE_AWAY', 'COMPLETED', '2025-01-04 15:00:00', '2025-01-04 15:30:00'),
(2, 'ON_SITE', 'COMPLETED', '2025-01-06 11:30:00', '2025-01-06 12:00:00'),
(2, 'TAKE_AWAY', 'COMPLETED', '2025-01-08 14:15:00', '2025-01-08 14:45:00'),
(2, 'ON_SITE', 'COMPLETED', '2025-01-10 10:00:00', '2025-01-10 10:30:00'),
(2, 'TAKE_AWAY', 'COMPLETED', '2025-01-12 16:45:00', '2025-01-12 17:15:00'),
(2, 'ON_SITE', 'COMPLETED', '2025-01-14 09:30:00', '2025-01-14 10:00:00'),

-- 档案3的治疗进程（3次）
(3, 'ON_SITE', 'COMPLETED', '2025-01-02 14:00:00', '2025-01-02 14:20:00'),
(3, 'TAKE_AWAY', 'COMPLETED', '2025-01-05 11:15:00', '2025-01-05 11:35:00'),
(3, 'ON_SITE', 'IN_PROGRESS', '2025-01-08 15:30:00', NULL),

-- 档案4的治疗进程（12次）
(4, 'ON_SITE', 'COMPLETED', '2025-01-02 08:30:00', '2025-01-02 09:00:00'),
(4, 'TAKE_AWAY', 'COMPLETED', '2025-01-03 13:45:00', '2025-01-03 14:15:00'),
(4, 'ON_SITE', 'COMPLETED', '2025-01-05 10:30:00', '2025-01-05 11:00:00'),
(4, 'TAKE_AWAY', 'COMPLETED', '2025-01-07 16:00:00', '2025-01-07 16:30:00'),
(4, 'ON_SITE', 'COMPLETED', '2025-01-09 09:15:00', '2025-01-09 09:45:00'),
(4, 'TAKE_AWAY', 'COMPLETED', '2025-01-11 14:30:00', '2025-01-11 15:00:00'),
(4, 'ON_SITE', 'COMPLETED', '2025-01-13 11:00:00', '2025-01-13 11:30:00'),
(4, 'TAKE_AWAY', 'COMPLETED', '2025-01-15 15:45:00', '2025-01-15 16:15:00'),
(4, 'ON_SITE', 'COMPLETED', '2025-01-17 10:15:00', '2025-01-17 10:45:00'),
(4, 'TAKE_AWAY', 'COMPLETED', '2025-01-19 13:30:00', '2025-01-19 14:00:00'),
(4, 'ON_SITE', 'COMPLETED', '2025-01-21 09:45:00', '2025-01-21 10:15:00'),
(4, 'TAKE_AWAY', 'IN_PROGRESS', '2025-01-23 14:00:00', NULL),

-- 档案5的治疗进程（6次）
(5, 'ON_SITE', 'COMPLETED', '2025-01-03 09:30:00', '2025-01-03 09:50:00'),
(5, 'TAKE_AWAY', 'COMPLETED', '2025-01-05 14:15:00', '2025-01-05 14:35:00'),
(5, 'ON_SITE', 'COMPLETED', '2025-01-07 11:00:00', '2025-01-07 11:20:00'),
(5, 'TAKE_AWAY', 'COMPLETED', '2025-01-09 15:30:00', '2025-01-09 15:50:00'),
(5, 'ON_SITE', 'COMPLETED', '2025-01-11 10:45:00', '2025-01-11 11:05:00'),
(5, 'TAKE_AWAY', 'COMPLETED', '2025-01-13 16:00:00', '2025-01-13 16:20:00'),

-- 继续添加更多档案的治疗进程（简化版，每个档案2-4次进程）
-- 档案6-10
(6, 'ON_SITE', 'COMPLETED', '2025-01-03 10:00:00', '2025-01-03 10:30:00'),
(6, 'TAKE_AWAY', 'COMPLETED', '2025-01-06 15:00:00', '2025-01-06 15:30:00'),
(6, 'ON_SITE', 'COMPLETED', '2025-01-09 11:30:00', '2025-01-09 12:00:00'),
(6, 'TAKE_AWAY', 'IN_PROGRESS', '2025-01-12 14:45:00', NULL),

(7, 'ON_SITE', 'COMPLETED', '2025-01-04 09:15:00', '2025-01-04 09:45:00'),
(7, 'ON_SITE', 'COMPLETED', '2025-01-06 10:30:00', '2025-01-06 11:00:00'),
(7, 'TAKE_AWAY', 'COMPLETED', '2025-01-08 14:00:00', '2025-01-08 14:30:00'),
(7, 'ON_SITE', 'COMPLETED', '2025-01-10 16:15:00', '2025-01-10 16:45:00'),
(7, 'TAKE_AWAY', 'COMPLETED', '2025-01-12 13:30:00', '2025-01-12 14:00:00'),
(7, 'ON_SITE', 'COMPLETED', '2025-01-14 11:00:00', '2025-01-14 11:30:00'),
(7, 'TAKE_AWAY', 'COMPLETED', '2025-01-16 15:45:00', '2025-01-16 16:15:00'),
(7, 'ON_SITE', 'COMPLETED', '2025-01-18 10:30:00', '2025-01-18 11:00:00'),
(7, 'TAKE_AWAY', 'COMPLETED', '2025-01-20 14:15:00', '2025-01-20 14:45:00'),
(7, 'ON_SITE', 'IN_PROGRESS', '2025-01-22 09:45:00', NULL),

(8, 'ON_SITE', 'COMPLETED', '2025-01-04 11:00:00', '2025-01-04 11:25:00'),
(8, 'TAKE_AWAY', 'COMPLETED', '2025-01-07 15:30:00', '2025-01-07 15:55:00'),
(8, 'ON_SITE', 'COMPLETED', '2025-01-10 09:45:00', '2025-01-10 10:10:00'),
(8, 'TAKE_AWAY', 'COMPLETED', '2025-01-13 14:20:00', '2025-01-13 14:45:00'),
(8, 'ON_SITE', 'COMPLETED', '2025-01-16 11:15:00', '2025-01-16 11:40:00'),
(8, 'TAKE_AWAY', 'COMPLETED', '2025-01-19 16:00:00', '2025-01-19 16:25:00'),
(8, 'ON_SITE', 'COMPLETED', '2025-01-22 10:30:00', '2025-01-22 10:55:00'),

(9, 'ON_SITE', 'COMPLETED', '2025-01-05 08:45:00', '2025-01-05 09:10:00'),
(9, 'TAKE_AWAY', 'COMPLETED', '2025-01-07 13:15:00', '2025-01-07 13:40:00'),
(9, 'ON_SITE', 'COMPLETED', '2025-01-09 15:30:00', '2025-01-09 15:55:00'),
(9, 'TAKE_AWAY', 'COMPLETED', '2025-01-11 11:45:00', '2025-01-11 12:10:00'),
(9, 'ON_SITE', 'COMPLETED', '2025-01-13 14:00:00', '2025-01-13 14:25:00'),
(9, 'TAKE_AWAY', 'COMPLETED', '2025-01-15 16:30:00', '2025-01-15 16:55:00'),
(9, 'ON_SITE', 'COMPLETED', '2025-01-17 10:15:00', '2025-01-17 10:40:00'),
(9, 'TAKE_AWAY', 'COMPLETED', '2025-01-19 13:45:00', '2025-01-19 14:10:00'),
(9, 'ON_SITE', 'IN_PROGRESS', '2025-01-21 15:00:00', NULL),

(10, 'ON_SITE', 'COMPLETED', '2025-01-05 10:30:00', '2025-01-05 11:00:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-07 14:45:00', '2025-01-07 15:15:00'),
(10, 'ON_SITE', 'COMPLETED', '2025-01-09 09:00:00', '2025-01-09 09:30:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-11 16:15:00', '2025-01-11 16:45:00'),
(10, 'ON_SITE', 'COMPLETED', '2025-01-13 11:30:00', '2025-01-13 12:00:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-15 15:00:00', '2025-01-15 15:30:00'),
(10, 'ON_SITE', 'COMPLETED', '2025-01-17 13:15:00', '2025-01-17 13:45:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-19 10:45:00', '2025-01-19 11:15:00'),
(10, 'ON_SITE', 'COMPLETED', '2025-01-21 14:30:00', '2025-01-21 15:00:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-23 16:00:00', '2025-01-23 16:30:00'),
(10, 'ON_SITE', 'COMPLETED', '2025-01-25 11:00:00', '2025-01-25 11:30:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-27 15:15:00', '2025-01-27 15:45:00'),
(10, 'ON_SITE', 'COMPLETED', '2025-01-29 09:30:00', '2025-01-29 10:00:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-31 14:00:00', '2025-01-31 14:30:00'),
(10, 'ON_SITE', 'IN_PROGRESS', '2025-02-02 10:45:00', NULL);

-- -----------------------------------------------------
-- 生成治疗详情数据（基于前面的进程数据）
-- -----------------------------------------------------
INSERT INTO `treatment_details` (`process_id`, `head_number_used`, `body_part`, `duration`, `intensity`, `frequency`, `patch_type`, `patch_quantity`, `status`) VALUES
-- 进程1的治疗详情（颈椎病治疗）
(1, 3, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(1, 4, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'COMPLETED'),

-- 进程2的治疗详情（外带治疗）
(2, 5, '肩颈部', 25, 30.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 进程3的治疗详情
(3, 6, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'COMPLETED'),

-- 进程4的治疗详情（外带治疗）
(4, 7, '肩颈部', 25, 45.00, 1000, 'SHALLOW', 1, 'RETURNED'),

-- 进程5的治疗详情（正在进行）
(5, 8, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 2, 'TREATING'),

-- 进程6-8的治疗详情（腰椎间盘突出）
(6, 11, '腰背部', 25, 60.00, 100, 'DEEP', 2, 'COMPLETED'),
(6, 12, '腰背部', 25, 60.00, 100, 'DEEP', 2, 'COMPLETED'),

(7, 13, '腰背部', 30, 60.00, 100, 'DEEP', 2, 'COMPLETED'),

(8, 14, '腰背部', 25, 60.00, 100, 'DEEP', 1, 'RETURNED'),

-- 进程9-11的治疗详情（肩周炎）
(9, 1, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),

(10, 2, '上肢', 20, 45.00, 1000, 'SHALLOW', 1, 'RETURNED'),

(11, 9, '上肢', 15, 30.00, 1000, 'SHALLOW', 1, 'TREATING'),

-- 进程12-23的治疗详情（腰肌劳损，多次治疗）
(12, 15, '腰背部', 20, 45.00, 100, 'DEEP', 2, 'COMPLETED'),
(13, 16, '腰背部', 25, 60.00, 100, 'DEEP', 1, 'RETURNED'),
(14, 17, '腰背部', 20, 45.00, 100, 'DEEP', 2, 'COMPLETED'),
(15, 18, '腰背部', 25, 45.00, 100, 'DEEP', 1, 'RETURNED'),
(16, 19, '腰背部', 20, 60.00, 100, 'DEEP', 2, 'COMPLETED'),
(17, 20, '腰背部', 25, 45.00, 100, 'DEEP', 1, 'RETURNED'),
(18, 11, '腰背部', 20, 60.00, 100, 'DEEP', 2, 'COMPLETED'),
(19, 12, '腰背部', 25, 45.00, 100, 'DEEP', 1, 'RETURNED'),
(20, 13, '腰背部', 20, 60.00, 100, 'DEEP', 2, 'COMPLETED'),
(21, 14, '腰背部', 25, 45.00, 100, 'DEEP', 1, 'RETURNED'),
(22, 15, '腰背部', 20, 60.00, 100, 'DEEP', 2, 'COMPLETED'),
(23, 16, '腰背部', 25, 45.00, 100, 'DEEP', 1, 'TREATING'),

-- 进程24-29的治疗详情（颈肩综合征）
(24, 3, '肩颈部', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(25, 4, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 1, 'RETURNED'),
(26, 5, '肩颈部', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(27, 6, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 1, 'RETURNED'),
(28, 7, '肩颈部', 15, 30.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(29, 8, '肩颈部', 20, 45.00, 1000, 'SHALLOW', 1, 'COMPLETED');

-- -----------------------------------------------------
-- 生成部位统计数据（基于治疗详情自动计算）
-- -----------------------------------------------------
INSERT INTO `body_part_stats` (`record_id`, `body_part`, `total_usage_count`, `total_duration_minutes`) VALUES
-- 档案1（颈椎病）- 肩颈部治疗统计
(1, '肩颈部', 5, 110),

-- 档案2（腰椎间盘突出）- 腰背部治疗统计
(2, '腰背部', 8, 200),

-- 档案3（肩周炎）- 上肢治疗统计
(3, '上肢', 3, 50),

-- 档案4（腰肌劳损）- 腰背部治疗统计
(4, '腰背部', 12, 270),

-- 档案5（颈肩综合征）- 肩颈部治疗统计
(5, '肩颈部', 6, 105),

-- 档案6（膝关节炎）- 下肢治疗统计
(6, '下肢', 4, 80),

-- 档案7（腰椎管狭窄）- 腰背部治疗统计
(7, '腰背部', 10, 250),

-- 档案8（颈椎生理曲度变直）- 肩颈部治疗统计
(8, '肩颈部', 7, 140),

-- 档案9（肩袖损伤）- 上肢治疗统计
(9, '上肢', 9, 162),

-- 档案10（腰椎滑脱）- 腰背部治疗统计
(10, '腰背部', 15, 375),

-- 档案11（网球肘）- 上肢治疗统计
(11, '上肢', 2, 30),

-- 档案12（髋关节炎）- 髋部治疗统计
(12, '髋部', 11, 220),

-- 档案13（胸椎小关节紊乱）- 腰背部治疗统计
(13, '腰背部', 5, 100),

-- 档案14（腕管综合征）- 上肢治疗统计
(14, '上肢', 8, 120),

-- 档案15（踝关节扭伤）- 下肢治疗统计
(15, '下肢', 3, 45),

-- 档案16（颈椎病伴眩晕）- 肩颈部治疗统计
(16, '肩颈部', 6, 120),

-- 档案17（腰椎退行性变）- 腰背部治疗统计
(17, '腰背部', 13, 325),

-- 档案18（肩胛骨周围肌筋膜炎）- 肩颈部治疗统计
(18, '肩颈部', 4, 70),

-- 档案19（髌骨软化症）- 下肢治疗统计
(19, '下肢', 7, 140),

-- 档案20（颈椎间盘突出）- 肩颈部治疗统计
(20, '肩颈部', 9, 180);

-- -----------------------------------------------------
-- 数据完整性检查和统计信息
-- -----------------------------------------------------

-- 显示生成的数据统计
SELECT '数据生成完成！' as message;
SELECT
    '患者数据' as data_type,
    COUNT(*) as count,
    MIN(created_at) as earliest_date,
    MAX(created_at) as latest_date
FROM patients
UNION ALL
SELECT
    '档案数据' as data_type,
    COUNT(*) as count,
    MIN(created_at) as earliest_date,
    MAX(created_at) as latest_date
FROM records
UNION ALL
SELECT
    '治疗进程' as data_type,
    COUNT(*) as count,
    MIN(start_time) as earliest_date,
    MAX(start_time) as latest_date
FROM processes
UNION ALL
SELECT
    '治疗详情' as data_type,
    COUNT(*) as count,
    NULL as earliest_date,
    NULL as latest_date
FROM treatment_details
UNION ALL
SELECT
    '部位统计' as data_type,
    COUNT(*) as count,
    NULL as earliest_date,
    NULL as latest_date
FROM body_part_stats;

-- 显示治疗模式分布
SELECT
    treatment_mode,
    COUNT(*) as process_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM processes), 2) as percentage
FROM processes
GROUP BY treatment_mode;

-- 显示治疗状态分布
SELECT
    status,
    COUNT(*) as process_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM processes), 2) as percentage
FROM processes
GROUP BY status;

-- 显示部位治疗分布
SELECT
    body_part,
    COUNT(*) as treatment_count,
    SUM(total_usage_count) as total_sessions,
    SUM(total_duration_minutes) as total_minutes
FROM body_part_stats
GROUP BY body_part
ORDER BY total_sessions DESC;
