package com.Bone.BoneSys.dto.hardware;

/**
 * 治疗身体部位DTO
 * 表示一个治疗部位及其参数
 */
public class TreatmentBodyPart {
    
    private String name;          // 部位名称：肩颈部、腰背部、髋部、上肢、下肢、其他部位
    private String color;         // 颜色标识：blue、green、orange
    private TreatmentPartParameters parameters;
    
    public TreatmentBodyPart() {}
    
    public TreatmentBodyPart(String name, String color, TreatmentPartParameters parameters) {
        this.name = name;
        this.color = color;
        this.parameters = parameters;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public TreatmentPartParameters getParameters() {
        return parameters;
    }
    
    public void setParameters(TreatmentPartParameters parameters) {
        this.parameters = parameters;
    }
    
    @Override
    public String toString() {
        return "TreatmentBodyPart{" +
                "name='" + name + '\'' +
                ", color='" + color + '\'' +
                ", parameters=" + parameters +
                '}';
    }
    
    /**
     * 治疗部位参数
     */
    public static class TreatmentPartParameters {
        private String time;        // 时间：如 "15分钟"
        private String intensity;   // 强度：如 "30mW/cm²"
        private String frequency;   // 频率：如 "1000Hz"
        private String depth;       // 深浅：深部、浅部
        private int count;          // 数量：1-4
        
        public TreatmentPartParameters() {}
        
        public TreatmentPartParameters(String time, String intensity, String frequency, String depth, int count) {
            this.time = time;
            this.intensity = intensity;
            this.frequency = frequency;
            this.depth = depth;
            this.count = count;
        }
        
        // Getters and Setters
        public String getTime() {
            return time;
        }
        
        public void setTime(String time) {
            this.time = time;
        }
        
        public String getIntensity() {
            return intensity;
        }
        
        public void setIntensity(String intensity) {
            this.intensity = intensity;
        }
        
        public String getFrequency() {
            return frequency;
        }
        
        public void setFrequency(String frequency) {
            this.frequency = frequency;
        }
        
        public String getDepth() {
            return depth;
        }
        
        public void setDepth(String depth) {
            this.depth = depth;
        }
        
        public int getCount() {
            return count;
        }
        
        public void setCount(int count) {
            this.count = count;
        }
        
        @Override
        public String toString() {
            return "TreatmentPartParameters{" +
                    "time='" + time + '\'' +
                    ", intensity='" + intensity + '\'' +
                    ", frequency='" + frequency + '\'' +
                    ", depth='" + depth + '\'' +
                    ", count=" + count +
                    '}';
        }
    }
} 