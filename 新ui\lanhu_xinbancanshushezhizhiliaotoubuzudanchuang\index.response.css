.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 56.25vw;
  overflow: hidden;
}

.box_1 {
  height: 56.25vw;
  background: url(./img/66c5f27211164f992364facf79ecf52d.png) -0.21vw
    0vw no-repeat;
  background-size: 100.2vw 56.25vw;
  width: 100vw;
}

.group_1 {
  height: 56.25vw;
  background: url(./img/732eb040d6ffb4c5cf58adcb09bc3cc4.png) -2.97vw -419.54vw
    no-repeat;
  background-size: 102.96vw 475.78vw;
  width: 100vw;
}

.group_2 {
  height: 39.17vw;
  background: url(./img/dc02f849428605e71b359a0193094b4e.png) -0.47vw
    0vw no-repeat;
  background-size: 32.86vw 40.31vw;
  width: 31.88vw;
  margin: 7.65vw 0 0 34.27vw;
}

.image-wrapper_2 {
  width: 3.13vw;
  height: 3.13vw;
  margin: 1.61vw 0 0 26.56vw;
}

.image_2 {
  width: 3.13vw;
  height: 3.13vw;
}

.box_4 {
  width: 21.72vw;
  height: 4.59vw;
  margin: 3.48vw 0 0 4.84vw;
}

.image_1 {
  width: 4.59vw;
  height: 4.59vw;
}

.text_1 {
  width: 14.85vw;
  height: 2.97vw;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 3.02vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 18.55vw;
  margin-top: 0.79vw;
}

.box_5 {
  width: 16.36vw;
  height: 3.91vw;
  margin: 5.36vw 0 17.08vw 8.12vw;
}

.text-wrapper_1 {
  width: 16.36vw;
  height: 3.91vw;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  line-height: 10.42vw;
}

.paragraph_1 {
  width: 16.36vw;
  height: 3.91vw;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 10.42vw;
}

.text_2 {
  width: 16.36vw;
  height: 3.91vw;
  overflow-wrap: break-word;
  color: rgba(124, 121, 121, 1);
  font-size: 1.71vw;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  line-height: 2.19vw;
}
