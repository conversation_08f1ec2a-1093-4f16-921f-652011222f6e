import http from '@/utils/axios'

/**
 * 主界面相关API
 */

// 获取主界面数据
export const getMainDashboard = () => {
  return http.get('/dashboard/main')
}

// 系统信息接口类型定义
export interface SystemInfo {
  systemName: string
  version: string
  uptime: string
}

export interface DataOverview {
  totalPatients: number
  totalRecords: number
  totalProcesses: number
  todayProcesses: number
}

export interface MainDashboardResponse {
  availableHeads: number
  totalHeads: number
  systemInfo: SystemInfo
  dataOverview: DataOverview
} 