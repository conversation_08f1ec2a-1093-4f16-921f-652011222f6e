package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 治疗头调试控制器
 */
@RestController
@RequestMapping("/api/debug/treatment-heads")
@CrossOrigin(origins = "*")
public class TreatmentHeadDebugController {
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    /**
     * 获取所有治疗头数据
     */
    @GetMapping("/all")
    public ApiResponse<List<TreatmentHead>> getAllTreatmentHeads() {
        try {
            List<TreatmentHead> heads = treatmentHeadRepository.findAll();
            return ApiResponse.success("Found " + heads.size() + " treatment heads", heads);
        } catch (Exception e) {
            return ApiResponse.error(500, "Error retrieving treatment heads: " + e.getMessage());
        }
    }
    
    /**
     * 获取治疗头数量
     */
    @GetMapping("/count")
    public ApiResponse<Long> getTreatmentHeadCount() {
        try {
            long count = treatmentHeadRepository.count();
            return ApiResponse.success("Treatment head count: " + count, count);
        } catch (Exception e) {
            return ApiResponse.error(500, "Error counting treatment heads: " + e.getMessage());
        }
    }
    
    /**
     * 清空所有治疗头数据
     */
    @DeleteMapping("/clear")
    public ApiResponse<Void> clearAllTreatmentHeads() {
        try {
            treatmentHeadRepository.deleteAll();
            return ApiResponse.success("All treatment heads cleared");
        } catch (Exception e) {
            return ApiResponse.error(500, "Error clearing treatment heads: " + e.getMessage());
        }
    }
}