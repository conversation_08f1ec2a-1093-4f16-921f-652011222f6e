html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.image_1 {
  width: 5.6rem;
  height: 1.894rem;
  margin: 24.294rem 0 0 24.374rem;
}

.text-wrapper_1 {
  height: 2.054rem;
  background: url(./img/27de145c7e2db8f2cb4b4ceff7ea6b1f.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 5.76rem;
  position: absolute;
  left: 24.294rem;
  top: 24.214rem;
}

.text_1 {
  width: 4.614rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 0 0 0.587rem;
}

.block_1 {
  height: 28.8rem;
  background: url(./img/44e5c9121fe18c932e5368005de9617e.png) -0.027rem -144.187rem
    no-repeat;
  background-size: 105.84rem 192.64rem;
  width: 51.2rem;
  position: absolute;
  left: 0;
  top: 0;
}

.label_1 {
  width: 1.307rem;
  height: 1.307rem;
  margin: 2.774rem 0 0 39.094rem;
}

.group_1 {
  height: 25.734rem;
  background: url(./img/ab702eaeed1b324761de28e170dc71bf.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 33.414rem;
  position: absolute;
  left: 8.214rem;
  top: 1.68rem;
}

.group_11 {
  width: 18.16rem;
  height: 1.6rem;
  margin: 0.934rem 0 0 13.467rem;
}

.text_2 {
  width: 7.867rem;
  height: 1.307rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin-top: 0.16rem;
}

.label_2 {
  width: 1.6rem;
  height: 1.6rem;
}

.group_12 {
  width: 14.107rem;
  height: 1.254rem;
  margin: 2.267rem 0 0 4.294rem;
}

.text_3 {
  width: 2.08rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.24rem;
}

.section_1 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 11.307rem;
  height: 1.254rem;
}

.group_13 {
  width: 23.84rem;
  height: 1.28rem;
  margin: 1.147rem 0 0 4.267rem;
}

.text_4 {
  width: 2.454rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.187rem;
}

.group_2 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 5.654rem;
  height: 1.254rem;
  margin: 0.027rem 0 0 0.374rem;
}

.text_5 {
  width: 4.267rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.187rem 0 0 4.267rem;
}

.group_3 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 6.347rem;
  height: 1.227rem;
  margin-left: 0.48rem;
}

.group_14 {
  width: 23.28rem;
  height: 1.227rem;
  margin: 1.12rem 0 0 4.294rem;
}

.text_6 {
  width: 2.08rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.187rem;
}

.group_4 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 5.654rem;
  height: 1.227rem;
  margin-left: 0.72rem;
}

.text_7 {
  width: 2.054rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.134rem 0 0 6.48rem;
}

.thumbnail_1 {
  width: 0.454rem;
  height: 0.454rem;
  margin: 0.4rem 0 0 1.147rem;
}

.text_8 {
  width: 0.8rem;
  height: 0.8rem;
  overflow-wrap: break-word;
  color: rgba(64, 65, 65, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.187rem 0 0 0.667rem;
}

.thumbnail_2 {
  width: 0.454rem;
  height: 0.454rem;
  margin: 0.4rem 0 0 1.28rem;
}

.text_9 {
  width: 0.854rem;
  height: 0.854rem;
  overflow-wrap: break-word;
  color: rgba(64, 65, 65, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin: 0.134rem 0 0 0.64rem;
}

.group_15 {
  width: 9.867rem;
  height: 1.227rem;
  margin: 1.52rem 0 0 4.374rem;
}

.text_10 {
  width: 2.027rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.187rem;
}

.box_1 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 7.147rem;
  height: 1.227rem;
}

.group_16 {
  width: 23.814rem;
  height: 6.187rem;
  margin: 0.96rem 0 0 4.294rem;
}

.text_11 {
  width: 2.08rem;
  height: 0.88rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 4.134rem;
  margin-top: 0.16rem;
}

.box_2 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 21.014rem;
  height: 6.187rem;
}

.group_17 {
  width: 6.054rem;
  height: 2.427rem;
  margin: 1.254rem 0 1.334rem 14.507rem;
}

.text-wrapper_2 {
  height: 2.427rem;
  background: url(./img/4849a1de40a4c4579615db3ce298d8cd.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 6.054rem;
}

.text_12 {
  width: 4.614rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.534rem 0 0 0.587rem;
}
