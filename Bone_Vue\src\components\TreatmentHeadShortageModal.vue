<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="shortage-modal">
        <!-- 关闭按钮 -->
        <img
          class="close-btn"
          :src="closeIcon"
          alt="关闭"
          @click="closeModal"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 导入图片
import shortageBackground from '@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/-h-温馨提示治疗头数量不足.png'
import closeIcon from '@/assets/images/交互界面/参数设置/×.png'

// Props
const props = defineProps<{
  visible: boolean
}>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const closeModal = () => {
  emit('close')
}
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background: transparent;
  overflow: hidden;
  position: absolute;
  top: 147px;
  left: 658px;
}

/* 治疗头数量不足弹窗样式 */
.shortage-modal {
  height: 752px;
  background: url('@/assets/images/treatmentsettingsview/img/治疗头选择弹窗/-h-温馨提示治疗头数量不足.png') 0px 0px no-repeat;
  background-size: 631px 774px;
  width: 612px;
  position: relative;
}

/* 关闭按钮样式 */
.close-btn {
  width: 60px;
  height: 60px;
  margin: 31px 0 0 510px;
  cursor: pointer;
  position: absolute;
  transition: transform 0.2s ease;
}

.close-btn:hover {
  transform: scale(1.1);
}
</style> 