.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.image_1 {
  width: 210px;
  height: 71px;
  margin: 911px 0 0 914px;
}

.text-wrapper_1 {
  height: 77px;
  background: url(./img/27de145c7e2db8f2cb4b4ceff7ea6b1f.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 216px;
  position: absolute;
  left: 911px;
  top: 908px;
}

.text_1 {
  width: 173px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 0 0 22px;
}

.block_1 {
  height: 1080px;
  background: url(./img/44e5c9121fe18c932e5368005de9617e.png) -1px -5407px
    no-repeat;
  background-size: 3969px 7224px;
  width: 1920px;
  position: absolute;
  left: 0;
  top: 0;
}

.label_1 {
  width: 49px;
  height: 49px;
  margin: 104px 0 0 1466px;
}

.group_1 {
  height: 965px;
  background: url(./img/ab702eaeed1b324761de28e170dc71bf.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1253px;
  position: absolute;
  left: 308px;
  top: 63px;
}

.group_11 {
  width: 681px;
  height: 60px;
  margin: 35px 0 0 505px;
}

.text_2 {
  width: 295px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 6px;
}

.label_2 {
  width: 60px;
  height: 60px;
}

.group_12 {
  width: 529px;
  height: 47px;
  margin: 85px 0 0 161px;
}

.text_3 {
  width: 78px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 9px;
}

.section_1 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 424px;
  height: 47px;
}

.group_13 {
  width: 894px;
  height: 48px;
  margin: 43px 0 0 160px;
}

.text_4 {
  width: 92px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 7px;
}

.group_2 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 212px;
  height: 47px;
  margin: 1px 0 0 14px;
}

.text_5 {
  width: 160px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin: 7px 0 0 160px;
}

.group_3 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 238px;
  height: 46px;
  margin-left: 18px;
}

.group_14 {
  width: 873px;
  height: 46px;
  margin: 42px 0 0 161px;
}

.text_6 {
  width: 78px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 7px;
}

.group_4 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 212px;
  height: 46px;
  margin-left: 27px;
}

.text_7 {
  width: 77px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin: 5px 0 0 243px;
}

.thumbnail_1 {
  width: 17px;
  height: 17px;
  margin: 15px 0 0 43px;
}

.text_8 {
  width: 30px;
  height: 30px;
  overflow-wrap: break-word;
  color: rgba(64, 65, 65, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin: 7px 0 0 25px;
}

.thumbnail_2 {
  width: 17px;
  height: 17px;
  margin: 15px 0 0 48px;
}

.text_9 {
  width: 32px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(64, 65, 65, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin: 5px 0 0 24px;
}

.group_15 {
  width: 370px;
  height: 46px;
  margin: 57px 0 0 164px;
}

.text_10 {
  width: 76px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 7px;
}

.box_1 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 268px;
  height: 46px;
}

.group_16 {
  width: 893px;
  height: 232px;
  margin: 36px 0 0 161px;
}

.text_11 {
  width: 78px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 6px;
}

.box_2 {
  background-color: rgba(244, 244, 244, 1);
  border-radius: 2px;
  width: 788px;
  height: 232px;
}

.group_17 {
  width: 227px;
  height: 91px;
  margin: 47px 0 50px 544px;
}

.text-wrapper_2 {
  height: 91px;
  background: url(./img/4849a1de40a4c4579615db3ce298d8cd.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 227px;
}

.text_12 {
  width: 173px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 20px 0 0 22px;
}
