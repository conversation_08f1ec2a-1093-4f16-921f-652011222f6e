<template>
  <div class="test-diagnosis-page">
    <h1>诊断详细功能测试页面</h1>
    
    <div class="test-section">
      <h2>1. 获取患者信息测试</h2>
      <div class="form-group">
        <label>患者ID:</label>
        <input v-model="patientId" type="number" placeholder="输入患者ID" />
        <button @click="getPatientInfo">获取患者信息</button>
      </div>
      <div class="result-box">
        <h3>患者信息:</h3>
        <pre>{{ JSON.stringify(patientInfo, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>2. 更新诊断信息测试</h2>
      <div class="form-group">
        <label>诊断信息:</label>
        <textarea v-model="diagnosisText" placeholder="输入诊断信息" rows="4"></textarea>
        <button @click="updateDiagnosis">更新诊断信息</button>
      </div>
      <div class="result-box">
        <h3>更新结果:</h3>
        <pre>{{ updateResult }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 诊断次数统计测试</h2>
      <button @click="getDiagnosisStats">获取诊断统计</button>
      <div class="result-box">
        <h3>诊断统计:</h3>
        <pre>{{ JSON.stringify(diagnosisStats, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>4. 档案记录测试</h2>
      <button @click="getRecords">获取档案记录</button>
      <div class="result-box">
        <h3>档案记录:</h3>
        <pre>{{ JSON.stringify(records, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import axios from 'axios'

const patientId = ref(1)
const patientInfo = ref(null)
const diagnosisText = ref('肩颈部疼痛，建议进行物理治疗')
const updateResult = ref('')
const diagnosisStats = ref(null)
const records = ref(null)

// 获取JWT Token
const getToken = async () => {
  try {
    const response = await axios.post('/api/auth/login', {
      password: '123456'
    })
    console.log('登录响应:', response.data)
    if (response.data && response.data.token) {
      return response.data.token
    } else {
      console.error('响应中没有token字段:', response.data)
      return null
    }
  } catch (error) {
    console.error('获取Token失败:', error)
    return null
  }
}

// 获取患者信息
const getPatientInfo = async () => {
  try {
    const token = await getToken()
    if (!token) {
      patientInfo.value = { error: '无法获取认证Token' }
      return
    }

    const response = await axios.get(`/api/patients/${patientId.value}`, {
      headers: { Authorization: `Bearer ${token}` }
    })
    patientInfo.value = response.data
  } catch (error: any) {
    console.error('获取患者信息失败:', error)
    patientInfo.value = { 
      error: error.response?.data?.message || error.message 
    }
  }
}

// 更新诊断信息
const updateDiagnosis = async () => {
  try {
    const token = await getToken()
    if (!token) {
      updateResult.value = '无法获取认证Token'
      return
    }

    const response = await axios.put(`/api/patients/${patientId.value}`, {
      diagnosis: diagnosisText.value
    }, {
      headers: { Authorization: `Bearer ${token}` }
    })
    updateResult.value = JSON.stringify(response.data, null, 2)
  } catch (error: any) {
    console.error('更新诊断信息失败:', error)
    updateResult.value = JSON.stringify({
      error: error.response?.data?.message || error.message
    }, null, 2)
  }
}

// 获取诊断统计
const getDiagnosisStats = async () => {
  try {
    const token = await getToken()
    if (!token) {
      diagnosisStats.value = { error: '无法获取认证Token' }
      return
    }

    const response = await axios.get(`/api/patients/${patientId.value}/diagnoses`, {
      headers: { Authorization: `Bearer ${token}` }
    })
    diagnosisStats.value = response.data
  } catch (error: any) {
    console.error('获取诊断统计失败:', error)
    diagnosisStats.value = { 
      error: error.response?.data?.message || error.message 
    }
  }
}

// 获取档案记录
const getRecords = async () => {
  try {
    const token = await getToken()
    if (!token) {
      records.value = { error: '无法获取认证Token' }
      return
    }

    const response = await axios.get('/api/records', {
      headers: { Authorization: `Bearer ${token}` }
    })
    records.value = response.data
  } catch (error: any) {
    console.error('获取档案记录失败:', error)
    records.value = { 
      error: error.response?.data?.message || error.message 
    }
  }
}
</script>

<style scoped>
.test-diagnosis-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 300px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 10px;
}

.form-group button {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-group button:hover {
  background-color: #0056b3;
}

.result-box {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.result-box h3 {
  margin-top: 0;
  color: #495057;
}

.result-box pre {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
