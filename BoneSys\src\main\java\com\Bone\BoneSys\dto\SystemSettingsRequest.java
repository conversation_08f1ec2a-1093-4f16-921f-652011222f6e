package com.Bone.BoneSys.dto;

public class SystemSettingsRequest {
    private Integer volume;
    private String screenTimeout;
    private String language;
    private String[] reminderTimes;

    public SystemSettingsRequest() {}

    public Integer getVolume() {
        return volume;
    }

    public void setVolume(Integer volume) {
        this.volume = volume;
    }

    public String getScreenTimeout() {
        return screenTimeout;
    }

    public void setScreenTimeout(String screenTimeout) {
        this.screenTimeout = screenTimeout;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String[] getReminderTimes() {
        return reminderTimes;
    }

    public void setReminderTimes(String[] reminderTimes) {
        this.reminderTimes = reminderTimes;
    }
} 