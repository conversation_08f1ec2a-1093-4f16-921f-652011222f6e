package com.Bone.BoneSys.service;

import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.fazecast.jSerialComm.SerialPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 串口连接管理器
 * 负责连接健康检查、自动重连、连接状态管理
 */
@Component
public class SerialConnectionManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SerialConnectionManager.class);
    
    @Value("${serial.port.name:COM1}")
    private String portName;
    
    @Value("${serial.port.baud-rate:115200}")
    private int baudRate;
    
    @Value("${serial.port.timeout:5000}")
    private int timeoutMs;
    
    @Value("${serial.connection.health-check.interval:30000}")
    private long healthCheckInterval;
    
    @Value("${serial.connection.max-retry-attempts:5}")
    private int maxRetryAttempts;
    
    @Value("${serial.connection.retry-delay:2000}")
    private long retryDelay;
    
    private SerialPort serialPort;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicBoolean isReconnecting = new AtomicBoolean(false);
    private final AtomicInteger retryCount = new AtomicInteger(0);
    private final AtomicLong lastSuccessfulCommunication = new AtomicLong(System.currentTimeMillis());
    
    private ScheduledExecutorService healthCheckExecutor;
    private ScheduledExecutorService reconnectExecutor;
    
    @PostConstruct
    public void initialize() {
        healthCheckExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "SerialHealthCheck");
            t.setDaemon(true);
            return t;
        });
        
        reconnectExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "SerialReconnect");
            t.setDaemon(true);
            return t;
        });
        
        // 启动健康检查
        startHealthCheck();
        logger.info("Serial connection manager initialized");
    }
    
    @PreDestroy
    public void cleanup() {
        stopHealthCheck();
        disconnect();
        logger.info("Serial connection manager cleaned up");
    }
    
    /**
     * 建立串口连接
     */
    public synchronized boolean connect() {
        if (isConnected.get()) {
            return true;
        }
        
        try {
            logger.info("Attempting to connect to serial port: {}", portName);
            
            // 获取所有可用的串口
            SerialPort[] availablePorts = SerialPort.getCommPorts();
            logger.debug("Available serial ports: {}", java.util.Arrays.toString(availablePorts));
            
            // 查找指定的串口
            serialPort = null;
            for (SerialPort port : availablePorts) {
                if (port.getSystemPortName().equals(portName)) {
                    serialPort = port;
                    break;
                }
            }
            
            if (serialPort == null) {
                // 如果找不到指定串口，尝试使用第一个可用串口（用于测试）
                if (availablePorts.length > 0) {
                    serialPort = availablePorts[0];
                    logger.warn("Port {} not found, using {} instead", portName, serialPort.getSystemPortName());
                } else {
                    throw new SerialCommunicationException("No serial ports available");
                }
            }
            
            // 配置串口参数
            serialPort.setBaudRate(baudRate);
            serialPort.setNumDataBits(8);
            serialPort.setNumStopBits(1);
            serialPort.setParity(SerialPort.NO_PARITY);
            serialPort.setComPortTimeouts(SerialPort.TIMEOUT_READ_SEMI_BLOCKING, timeoutMs, 0);
            
            // 打开串口
            if (!serialPort.openPort()) {
                throw new SerialCommunicationException("Failed to open serial port: " + serialPort.getSystemPortName());
            }
            
            isConnected.set(true);
            retryCount.set(0);
            lastSuccessfulCommunication.set(System.currentTimeMillis());
            
            logger.info("Successfully connected to serial port: {} at baud rate: {}", 
                       serialPort.getSystemPortName(), baudRate);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to connect to serial port", e);
            if (serialPort != null && serialPort.isOpen()) {
                serialPort.closePort();
            }
            isConnected.set(false);
            return false;
        }
    }
    
    /**
     * 断开串口连接
     */
    public synchronized void disconnect() {
        try {
            if (serialPort != null && serialPort.isOpen()) {
                serialPort.removeDataListener();
                serialPort.closePort();
                logger.info("Disconnected from serial port: {}", serialPort.getSystemPortName());
            }
        } catch (Exception e) {
            logger.error("Error disconnecting from serial port", e);
        } finally {
            isConnected.set(false);
            serialPort = null;
        }
    }
    
    /**
     * 获取串口实例
     */
    public SerialPort getSerialPort() {
        return serialPort;
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected.get() && serialPort != null && serialPort.isOpen();
    }
    
    /**
     * 更新最后成功通信时间
     */
    public void updateLastSuccessfulCommunication() {
        lastSuccessfulCommunication.set(System.currentTimeMillis());
    }
    
    /**
     * 获取连接信息
     */
    public ConnectionInfo getConnectionInfo() {
        ConnectionInfo info = new ConnectionInfo();
        info.connected = isConnected();
        info.reconnecting = isReconnecting.get();
        info.retryCount = retryCount.get();
        info.lastSuccessfulCommunication = lastSuccessfulCommunication.get();
        
        if (serialPort != null) {
            info.portName = serialPort.getSystemPortName();
            info.baudRate = serialPort.getBaudRate();
        } else {
            info.portName = portName;
            info.baudRate = baudRate;
        }
        
        return info;
    }
    
    /**
     * 启动健康检查
     */
    private void startHealthCheck() {
        healthCheckExecutor.scheduleWithFixedDelay(this::performHealthCheck, 
                                                  healthCheckInterval, 
                                                  healthCheckInterval, 
                                                  TimeUnit.MILLISECONDS);
        logger.info("Health check started with interval: {}ms", healthCheckInterval);
    }
    
    /**
     * 停止健康检查
     */
    private void stopHealthCheck() {
        if (healthCheckExecutor != null && !healthCheckExecutor.isShutdown()) {
            healthCheckExecutor.shutdown();
            try {
                if (!healthCheckExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    healthCheckExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                healthCheckExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (reconnectExecutor != null && !reconnectExecutor.isShutdown()) {
            reconnectExecutor.shutdown();
            try {
                if (!reconnectExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    reconnectExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                reconnectExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            if (!isConnected()) {
                logger.warn("Serial port is not connected, attempting to reconnect");
                scheduleReconnect();
                return;
            }
            
            // 检查最后成功通信时间
            long timeSinceLastCommunication = System.currentTimeMillis() - lastSuccessfulCommunication.get();
            if (timeSinceLastCommunication > healthCheckInterval * 2) {
                logger.warn("No successful communication for {}ms, connection may be stale", timeSinceLastCommunication);
                // 可以选择发送心跳包或重连
                scheduleReconnect();
            }
            
        } catch (Exception e) {
            logger.error("Error during health check", e);
        }
    }
    
    /**
     * 安排重连任务
     */
    private void scheduleReconnect() {
        if (isReconnecting.compareAndSet(false, true)) {
            reconnectExecutor.schedule(this::attemptReconnect, retryDelay, TimeUnit.MILLISECONDS);
        }
    }
    
    /**
     * 尝试重连
     */
    private void attemptReconnect() {
        try {
            int currentRetry = retryCount.incrementAndGet();
            logger.info("Attempting reconnection #{}/{}", currentRetry, maxRetryAttempts);
            
            // 先断开现有连接
            disconnect();
            
            // 等待一段时间后重连
            Thread.sleep(1000);
            
            if (connect()) {
                logger.info("Reconnection successful after {} attempts", currentRetry);
                retryCount.set(0);
            } else if (currentRetry < maxRetryAttempts) {
                logger.warn("Reconnection attempt #{} failed, scheduling next attempt", currentRetry);
                reconnectExecutor.schedule(this::attemptReconnect, retryDelay, TimeUnit.MILLISECONDS);
                return;
            } else {
                logger.error("Max reconnection attempts ({}) reached, giving up", maxRetryAttempts);
                retryCount.set(0);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Reconnection interrupted");
        } catch (Exception e) {
            logger.error("Error during reconnection attempt", e);
        } finally {
            isReconnecting.set(false);
        }
    }
    
    /**
     * 连接信息类
     */
    public static class ConnectionInfo {
        public boolean connected;
        public boolean reconnecting;
        public int retryCount;
        public long lastSuccessfulCommunication;
        public String portName;
        public int baudRate;
        
        @Override
        public String toString() {
            return String.format("ConnectionInfo{connected=%s, reconnecting=%s, retryCount=%d, " +
                               "lastSuccessfulCommunication=%d, portName='%s', baudRate=%d}",
                               connected, reconnecting, retryCount, lastSuccessfulCommunication, 
                               portName, baudRate);
        }
    }
}