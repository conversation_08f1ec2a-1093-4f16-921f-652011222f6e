html {
  font-size: 37.5px;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 51.2rem;
  height: 28.8rem;
  overflow: hidden;
}

.block_1 {
  width: 51.174rem;
  height: 28.8rem;
  background: url(./img/dfbbbd175fc7977960a30f12940c43df.png)
    0rem 0rem no-repeat;
  background-size: 52.054rem 29.174rem;
  margin-left: 0.027rem;
}

.image_1 {
  width: 4.507rem;
  height: 1.867rem;
  margin: 0.534rem 0 0 2.747rem;
}

.text_1 {
  width: 7.84rem;
  height: 1.334rem;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 1.333rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
  margin: 0.8rem 21.68rem 0 14.4rem;
}

.section_1 {
  height: 28.8rem;
  background: url(./img/6184e356ba89d2d0bf272d625fb1e50e.png) -1.52rem -423.174rem
    no-repeat;
  background-size: 105.814rem 602.24rem;
  width: 51.2rem;
  position: absolute;
  left: 0;
  top: 0;
}

.block_2 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 16px;
  height: 15.067rem;
  border: 3px solid rgba(145, 201, 194, 1);
  width: 20.374rem;
  margin: 9.574rem 0 0 15.387rem;
}

.image-wrapper_3 {
  width: 14.107rem;
  height: 10rem;
  margin: 0.774rem 0 0 5.547rem;
}

.image_2 {
  width: 9.36rem;
  height: 9.36rem;
  margin-top: 0.64rem;
}

.image_3 {
  width: 1.6rem;
  height: 1.6rem;
}

.text-wrapper_2 {
  width: 9.014rem;
  height: 0.907rem;
  margin: 0.694rem 0 2.694rem 5.387rem;
}

.text_2 {
  width: 9.014rem;
  height: 0.907rem;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 0.88rem;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 1.094rem;
}
