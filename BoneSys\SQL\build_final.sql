-- ====================================================================================
-- FREEBONE医疗系统数据库表结构创建脚本 (最终版)
-- 更新说明：
-- 1. 治疗头表增加颜色属性
-- 2. 治疗头编号改为1-20的数字
-- 3. 槽号1-10代表浅部（上仓），11-20代表深部（下仓）
-- 4. 治疗头只有三种状态：充电中、充电完成、治疗中
-- ====================================================================================

-- 确保当前操作的数据库是 `bonesys`
USE `bonesys`;

-- -----------------------------------------------------
-- 表 1: `users` (用户表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`users` (
  `id` INT NOT NULL,
  `username` VARCHAR(50) NOT NULL,
  `factory_password_hash` VARCHAR(255) NOT NULL COMMENT '厂家密码的哈希值',
  `user_password_hash` VARCHAR(255) NOT NULL COMMENT '用户自定义密码的哈希值',
  `last_updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `username_UNIQUE` (`username` ASC) VISIBLE)
ENGINE = InnoDB
COMMENT = '用户表，单行存储默认用户和密码信息';

-- -----------------------------------------------------
-- 表 2: `patients` (患者表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`patients` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `patient_card_id` VARCHAR(50) NOT NULL COMMENT '就诊卡号',
  `name` VARCHAR(100) NOT NULL COMMENT '患者姓名',
  `gender` VARCHAR(10) NULL DEFAULT NULL,
  `age` VARCHAR(10) NULL DEFAULT NULL,
  `contact_info` VARCHAR(255) NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `patient_card_id_UNIQUE` (`patient_card_id` ASC) VISIBLE)
ENGINE = InnoDB
COMMENT = '患者基本信息表';

-- -----------------------------------------------------
-- 表 3: `records` (档案表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`records` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_number` VARCHAR(50) NOT NULL COMMENT '档案唯一编号',
  `patient_id` BIGINT NOT NULL,
  `diagnosis_description` TEXT NULL DEFAULT NULL,
  `sessions_completed_count` INT NOT NULL DEFAULT 0 COMMENT '已完成的总会话次数',
  `created_at` DATE NOT NULL COMMENT '建档日期',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `record_number_UNIQUE` (`record_number` ASC) VISIBLE,
  INDEX `fk_records_patients_idx` (`patient_id` ASC) VISIBLE,
  CONSTRAINT `fk_records_patients`
    FOREIGN KEY (`patient_id`)
    REFERENCES `bonesys`.`patients` (`id`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE)
ENGINE = InnoDB
COMMENT = '诊断档案信息表';

-- -----------------------------------------------------
-- 表 4: `treatment_heads` (治疗头管理表) - 最终版
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`treatment_heads` (
  `head_id` BIGINT NOT NULL AUTO_INCREMENT,
  `head_number` INT NOT NULL COMMENT '治疗头编号 (1-20)',
  `slot_number` INT NULL DEFAULT NULL COMMENT '槽位号 (1-10浅部上仓, 11-20深部下仓)',
  `light_color` INT NOT NULL DEFAULT 0 COMMENT '指示灯颜色 (0=关闭, 1=橙色, 2=蓝色, 3=绿色)',
  `realtime_status` VARCHAR(20) NOT NULL DEFAULT 'CHARGING' COMMENT '实时状态: CHARGING(充电中), CHARGED(充电完成), TREATING(治疗中)',
  `battery_level` INT NULL DEFAULT NULL COMMENT '实时电量 (0-100)',
  `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT '累计总使用次数',
  `total_usage_minutes` INT NOT NULL DEFAULT 0 COMMENT '累计总使用时长（分钟）',
  `max_usage_count` INT NOT NULL DEFAULT 500 COMMENT '设计的总可用次数',
  PRIMARY KEY (`head_id`),
  UNIQUE INDEX `head_number_UNIQUE` (`head_number` ASC) VISIBLE,
  UNIQUE INDEX `slot_number_UNIQUE` (`slot_number` ASC) VISIBLE)
ENGINE = InnoDB
COMMENT = '治疗头设备管理表 - 支持20个治疗头，上下两仓设计，三种状态';

-- -----------------------------------------------------
-- 表 5: `processes` (进程表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`processes` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_id` BIGINT NOT NULL,
  `treatment_mode` VARCHAR(20) NOT NULL COMMENT '治疗模式: ON_SITE, TAKE_AWAY',
  `status` VARCHAR(20) NOT NULL COMMENT '进程状态: IN_PROGRESS, COMPLETED, CANCELLED',
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_processes_records_idx` (`record_id` ASC) VISIBLE,
  CONSTRAINT `fk_processes_records`
    FOREIGN KEY (`record_id`)
    REFERENCES `bonesys`.`records` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
ENGINE = InnoDB
COMMENT = '单次治疗进程表';

-- -----------------------------------------------------
-- 表 6: `treatment_details` (治疗详情表) - 更新外键引用
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`treatment_details` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `process_id` BIGINT NOT NULL,
  `head_number_used` INT NOT NULL COMMENT '使用的治疗头编号 (1-20)',
  `body_part` VARCHAR(50) NOT NULL COMMENT '肩颈部、腰背部、髋部、上肢、下肢、其他部位（可编辑）',
  `duration` INT NOT NULL COMMENT '治疗时长（分钟）',
  `intensity` DECIMAL(5,2) NOT NULL COMMENT '治疗声强 (mW/cm²)',
  `frequency` INT NOT NULL COMMENT '脉冲频率(Hz)',
  `patch_type` VARCHAR(20) NOT NULL COMMENT '贴片类型: DEEP (深部), SHALLOW (浅层)',
  `patch_quantity` INT NOT NULL COMMENT '贴片数量 (1-4)',
  `status` VARCHAR(20) NOT NULL COMMENT '详情状态: TREATING, COMPLETED, AWAITING_RETURN, RETURNED, TERMINATED',
  PRIMARY KEY (`id`),
  INDEX `fk_details_processes_idx` (`process_id` ASC) VISIBLE,
  INDEX `fk_details_heads_idx` (`head_number_used` ASC) VISIBLE,
  CONSTRAINT `fk_details_processes`
    FOREIGN KEY (`process_id`)
    REFERENCES `bonesys`.`processes` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE,
  CONSTRAINT `fk_details_heads`
    FOREIGN KEY (`head_number_used`)
    REFERENCES `bonesys`.`treatment_heads` (`head_number`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE)
ENGINE = InnoDB
COMMENT = '单次进程中的具体治疗项';

-- -----------------------------------------------------
-- 表 7: `body_part_stats` (部位统计表)
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `bonesys`.`body_part_stats` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_id` BIGINT NOT NULL,
  `body_part` VARCHAR(50) NOT NULL,
  `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT '该部位累计使用次数',
  `total_duration_minutes` INT NOT NULL DEFAULT 0 COMMENT '该部位累计使用时长（分钟）',
  PRIMARY KEY (`id`),
  INDEX `fk_stats_records_idx` (`record_id` ASC) VISIBLE,
  UNIQUE INDEX `record_body_part_UNIQUE` (`record_id` ASC, `body_part` ASC) VISIBLE,
  CONSTRAINT `fk_stats_records`
    FOREIGN KEY (`record_id`)
    REFERENCES `bonesys`.`records` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
ENGINE = InnoDB
COMMENT = '档案下各部位的累计统计表';

-- -----------------------------------------------------
-- 初始化数据
-- -----------------------------------------------------

-- 插入默认用户数据
INSERT INTO `bonesys`.`users` (`id`, `username`, `factory_password_hash`, `user_password_hash`) 
VALUES (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cY8jLbveVFWQou', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cY8jLbveVFWQou')
ON DUPLICATE KEY UPDATE 
  `factory_password_hash` = VALUES(`factory_password_hash`),
  `user_password_hash` = VALUES(`user_password_hash`);

-- 插入20个治疗头数据（三种状态分布）
INSERT INTO `bonesys`.`treatment_heads` (`head_number`, `slot_number`, `light_color`, `realtime_status`, `battery_level`, `total_usage_count`, `total_usage_minutes`) 
VALUES 
  -- 上仓浅部治疗头 (槽号1-10)
  (1, 1, 0, 'CHARGING', 50, 5, 100),
  (2, 2, 0, 'CHARGING', 60, 4, 80),
  (3, 3, 0, 'CHARGED', 100, 10, 200),
  (4, 4, 0, 'CHARGED', 100, 8, 160),
  (5, 5, 0, 'CHARGING', 70, 3, 60),
  (6, 6, 0, 'CHARGED', 100, 12, 240),
  (7, 7, 0, 'TREATING', 85, 6, 120),
  (8, 8, 0, 'CHARGED', 100, 9, 180),
  (9, 9, 0, 'CHARGING', 80, 7, 140),
  (10, 10, 0, 'CHARGED', 100, 5, 100),
  
  -- 下仓深部治疗头 (槽号11-20)
  (11, 11, 0, 'CHARGING', 65, 8, 160),
  (12, 12, 0, 'CHARGED', 100, 11, 220),
  (13, 13, 0, 'CHARGING', 55, 4, 80),
  (14, 14, 0, 'CHARGED', 100, 6, 120),
  (15, 15, 0, 'TREATING', 90, 9, 180),
  (16, 16, 0, 'CHARGED', 100, 13, 260),
  (17, 17, 0, 'CHARGING', 75, 5, 100),
  (18, 18, 0, 'CHARGED', 100, 10, 200),
  (19, 19, 0, 'CHARGING', 85, 7, 140),
  (20, 20, 0, 'CHARGED', 100, 8, 160)
ON DUPLICATE KEY UPDATE 
  `realtime_status` = VALUES(`realtime_status`),
  `battery_level` = VALUES(`battery_level`);

-- 验证表结构
SHOW TABLES;

-- 验证治疗头数据和状态分布
SELECT 
    head_number, 
    slot_number, 
    CASE 
        WHEN slot_number BETWEEN 1 AND 10 THEN '上仓(浅部)'
        WHEN slot_number BETWEEN 11 AND 20 THEN '下仓(深部)'
        ELSE '未知'
    END as compartment_type,
    light_color,
    CASE light_color
        WHEN 0 THEN '关闭'
        WHEN 1 THEN '橙色'
        WHEN 2 THEN '蓝色'
        WHEN 3 THEN '绿色'
        ELSE '未知'
    END as light_status,
    realtime_status,
    CASE realtime_status
        WHEN 'CHARGING' THEN '充电中'
        WHEN 'CHARGED' THEN '充电完成'
        WHEN 'TREATING' THEN '治疗中'
        ELSE '未知状态'
    END as status_description,
    battery_level
FROM treatment_heads 
ORDER BY head_number;

-- 统计各状态的治疗头数量
SELECT 
    realtime_status,
    CASE realtime_status
        WHEN 'CHARGING' THEN '充电中'
        WHEN 'CHARGED' THEN '充电完成'
        WHEN 'TREATING' THEN '治疗中'
        ELSE '未知状态'
    END as status_description,
    COUNT(*) as count
FROM treatment_heads 
GROUP BY realtime_status
ORDER BY realtime_status;