package com.Bone.BoneSys.repository;

import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 治疗头数据访问接口
 */
@Repository
public interface TreatmentHeadRepository extends JpaRepository<TreatmentHead, Long> {
    
    /**
     * 根据治疗头编号查找治疗头
     */
    Optional<TreatmentHead> findByHeadNumber(Integer headNumber);
    
    /**
     * 根据槽位号查找治疗头
     */
    Optional<TreatmentHead> findBySlotNumber(Integer slotNumber);
    
    /**
     * 检查治疗头编号是否存在
     */
    boolean existsByHeadNumber(Integer headNumber);
    
    /**
     * 检查槽位号是否存在
     */
    boolean existsBySlotNumber(Integer slotNumber);
    
    /**
     * 根据状态查找治疗头
     */
    List<TreatmentHead> findByRealtimeStatus(TreatmentHeadStatus status);
    
    /**
     * 查找可用的治疗头（充电完成状态）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGED' AND t.batteryLevel >= :minBattery")
    List<TreatmentHead> findAvailableHeads(@Param("minBattery") Integer minBattery);
    
    /**
     * 查找推荐的治疗头（按电量和使用次数排序）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGED' AND t.batteryLevel >= :minBattery " +
           "ORDER BY t.batteryLevel DESC, t.totalUsageCount ASC")
    List<TreatmentHead> findRecommendedHeads(@Param("minBattery") Integer minBattery, Pageable pageable);
    
    /**
     * 查找正在充电的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGING'")
    List<TreatmentHead> findChargingHeads();
    
    /**
     * 查找充电完成的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'CHARGED'")
    List<TreatmentHead> findChargedHeads();
    
    /**
     * 查找正在治疗的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.realtimeStatus = 'TREATING'")
    List<TreatmentHead> findTreatingHeads();
    
    /**
     * 根据仓位类型查找治疗头（浅部：1-10，深部：11-20）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.slotNumber BETWEEN :startSlot AND :endSlot")
    List<TreatmentHead> findBySlotRange(@Param("startSlot") Integer startSlot, @Param("endSlot") Integer endSlot);
    
    /**
     * 查找浅部治疗头（上仓，槽号1-10）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.slotNumber BETWEEN 1 AND 10")
    List<TreatmentHead> findShallowCompartmentHeads();
    
    /**
     * 查找深部治疗头（下仓，槽号11-20）
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.slotNumber BETWEEN 11 AND 20")
    List<TreatmentHead> findDeepCompartmentHeads();
    
    /**
     * 根据指示灯颜色查找治疗头
     */
    List<TreatmentHead> findByLightColor(Integer lightColor);
    
    /**
     * 查找指示灯开启的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.lightColor > 0")
    List<TreatmentHead> findHeadsWithLightOn();
    
    /**
     * 查找电量不足的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.batteryLevel < :lowBatteryThreshold")
    List<TreatmentHead> findLowBatteryHeads(@Param("lowBatteryThreshold") Integer lowBatteryThreshold);
    
    /**
     * 查找需要更换的治疗头
     */
    @Query("SELECT t FROM TreatmentHead t WHERE t.totalUsageCount >= t.maxUsageCount * 0.9")
    List<TreatmentHead> findHeadsNeedingReplacement();
    
    /**
     * 统计各状态的治疗头数量
     */
    @Query("SELECT t.realtimeStatus, COUNT(t) FROM TreatmentHead t GROUP BY t.realtimeStatus")
    List<Object[]> countByStatus();
    
    /**
     * 获取治疗头使用统计
     */
    @Query("SELECT AVG(t.totalUsageCount), MAX(t.totalUsageCount), MIN(t.totalUsageCount) FROM TreatmentHead t")
    Object[] getUsageStatistics();
}